# Notification System Integration Guide

## Quick Start

This guide shows how to integrate notification triggers into existing components and user flows.

## Integration Examples

### 1. Post Like Notifications

When a user likes a post, trigger a notification for the post author:

```typescript
// In your like button component or service
import { integratedNotificationService } from '@/services/integratedNotificationService'

async function handleLike(postId: string, postAuthorId: string, postTitle: string) {
  try {
    // Your existing like logic here
    await likePost(postId)
    
    // Add notification trigger
    const { data: { user } } = await supabase.auth.getUser()
    if (user && user.id !== postAuthorId) {
      await integratedNotificationService.createPostLikeNotification(
        postAuthorId,
        user.id,
        postId,
        postTitle
      )
    }
  } catch (error) {
    console.error('Error handling like:', error)
  }
}
```

### 2. Connection Request Integration

The connection system is already integrated. Here's how it works:

```typescript
// In connectionService.ts (already implemented)
import { integratedNotificationService } from './integratedNotificationService'

// When sending connection request
await integratedNotificationService.createConnectionRequestNotification(
  requesterId,
  recipientId,
  connectionId
)

// When accepting connection
await integratedNotificationService.createConnectionAcceptedNotification(
  requesterId,
  acceptorId,
  connectionId
)
```

### 3. Message Notifications

For direct message notifications:

```typescript
// In your messaging service
async function sendMessage(recipientId: string, messageContent: string) {
  try {
    // Send the message
    const message = await createMessage(recipientId, messageContent)
    
    // Create notification
    const { data: { user } } = await supabase.auth.getUser()
    if (user) {
      await integratedNotificationService.createNotification({
        userId: recipientId,
        type: 'message_received',
        title: 'New Message',
        message: `You have a new message from ${user.email}`,
        data: {
          senderId: user.id,
          messageId: message.id,
          messagePreview: messageContent.substring(0, 100)
        }
      })
    }
  } catch (error) {
    console.error('Error sending message:', error)
  }
}
```

### 4. Comment Notifications

For post comment notifications:

```typescript
// In your comment component
async function handleComment(postId: string, postAuthorId: string, commentText: string) {
  try {
    // Create the comment
    const comment = await createComment(postId, commentText)
    
    // Notify post author (if not commenting on own post)
    const { data: { user } } = await supabase.auth.getUser()
    if (user && user.id !== postAuthorId) {
      await integratedNotificationService.createNotification({
        userId: postAuthorId,
        type: 'post_commented',
        title: 'New Comment',
        message: `Someone commented on your post`,
        data: {
          commenterId: user.id,
          postId: postId,
          commentId: comment.id,
          commentText: commentText.substring(0, 100)
        }
      })
    }
  } catch (error) {
    console.error('Error handling comment:', error)
  }
}
```

### 5. Profile View Notifications

For profile view tracking:

```typescript
// In profile view component
import { integratedNotificationService } from '@/services/integratedNotificationService'

async function trackProfileView(profileUserId: string) {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (user && user.id !== profileUserId) {
      // Check if user wants profile view notifications
      const preferences = await integratedNotificationService.getNotificationPreferences()
      
      if (preferences?.profile_views) {
        await integratedNotificationService.createNotification({
          userId: profileUserId,
          type: 'profile_viewed',
          title: 'Profile View',
          message: `Someone viewed your profile`,
          data: {
            viewerId: user.id,
            viewedAt: new Date().toISOString()
          }
        })
      }
    }
  } catch (error) {
    console.error('Error tracking profile view:', error)
  }
}

// Call this when profile component mounts
onMounted(() => {
  if (route.params.userId) {
    trackProfileView(route.params.userId as string)
  }
})
```

## Real-time Notification Subscription

Add real-time notifications to any component:

```typescript
// In your main layout or dashboard component
import { integratedNotificationService } from '@/services/integratedNotificationService'
import { useNotificationStore } from '@/stores/notifications'

export default {
  setup() {
    const notificationStore = useNotificationStore()
    let subscription: any = null

    onMounted(async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        // Subscribe to real-time notifications
        subscription = integratedNotificationService.subscribeToNotifications(
          user.id,
          (payload) => {
            const notification = payload.new
            
            // Show toast notification
            notificationStore.add({
              type: 'info',
              message: notification.title,
              caption: notification.message,
              timeout: 5000
            })
            
            // Update notification count or list if needed
            // refreshNotificationCount()
          }
        )
      }
    })

    onUnmounted(() => {
      if (subscription) {
        subscription.unsubscribe()
      }
    })
  }
}
```

## Notification Preferences Integration

Add notification preferences to user settings:

```vue
<template>
  <div class="notification-settings">
    <h3>Notification Preferences</h3>
    <NotificationPreferences />
  </div>
</template>

<script setup>
import NotificationPreferences from '@/components/notifications/NotificationPreferences.vue'
</script>
```

## Testing Notifications

### Manual Testing

1. **Connection Requests**: Send a connection request and check recipient's notifications
2. **Email Delivery**: Check email queue table and SendGrid dashboard
3. **Preferences**: Toggle preferences and verify behavior changes
4. **Real-time**: Open multiple browser tabs and test real-time updates

### Automated Testing

```typescript
// Example test for notification creation
import { integratedNotificationService } from '@/services/integratedNotificationService'

describe('Notification Integration', () => {
  it('should create connection request notification', async () => {
    const result = await integratedNotificationService.createConnectionRequestNotification(
      'requester-id',
      'recipient-id',
      'connection-id'
    )
    
    expect(result).toBe(true)
    
    // Verify notification was created in database
    const notifications = await integratedNotificationService.getUserNotifications(10, 0)
    expect(notifications).toHaveLength(1)
    expect(notifications[0].type).toBe('connection_request')
  })
})
```

## Best Practices

### 1. Error Handling
Always wrap notification calls in try-catch blocks and don't let notification failures break main functionality:

```typescript
try {
  await integratedNotificationService.createNotification(notificationData)
} catch (error) {
  console.error('Notification failed:', error)
  // Continue with main functionality
}
```

### 2. User Privacy
Check user preferences before creating notifications:

```typescript
const preferences = await integratedNotificationService.getNotificationPreferences()
if (preferences?.likes) {
  // Create like notification
}
```

### 3. Avoid Self-Notifications
Never notify users about their own actions:

```typescript
const { data: { user } } = await supabase.auth.getUser()
if (user && user.id !== targetUserId) {
  // Create notification
}
```

### 4. Batch Operations
For bulk operations, consider batching notifications:

```typescript
// Instead of creating notifications one by one
const notifications = users.map(user => ({
  userId: user.id,
  type: 'system_announcement',
  title: 'System Update',
  message: 'New features available'
}))

// Process in batches
for (const notification of notifications) {
  await integratedNotificationService.createNotification(notification)
}
```

## Troubleshooting

### Common Issues

1. **Notifications not appearing**: Check RLS policies and user authentication
2. **Emails not sending**: Verify SendGrid configuration and user preferences
3. **Duplicate notifications**: Implement idempotency checks
4. **Performance issues**: Use database functions for bulk operations

### Debug Commands

```sql
-- Check notification preferences
SELECT * FROM notification_preferences WHERE user_id = 'user-uuid';

-- Check email queue
SELECT * FROM email_queue WHERE status = 'pending' ORDER BY created_at DESC;

-- Check recent notifications
SELECT * FROM notifications WHERE user_id = 'user-uuid' ORDER BY created_at DESC LIMIT 10;
```

## Migration from Old System

If you have existing notification code using the old `user_notifications` table:

1. Replace `notificationService.createNotification()` calls with `integratedNotificationService.createNotification()`
2. Update notification type enums to match new system
3. Test email delivery with new templates
4. Migrate user preferences if needed

## Support

For integration help:
- Check the main [NOTIFICATION_SYSTEM.md](./NOTIFICATION_SYSTEM.md) documentation
- Review existing implementations in `connectionService.ts`
- Test with the integrated notification service methods
