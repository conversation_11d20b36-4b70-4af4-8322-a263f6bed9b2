/**
 * Unified Real-time Service
 * 
 * Provides centralized real-time subscription management with connection handling,
 * duplicate prevention, and automatic cleanup.
 */

import { supabase } from '../lib/supabase'
import { ref, computed, onUnmounted } from 'vue'

export type RealtimeEvent = 'INSERT' | 'UPDATE' | 'DELETE' | '*'
export type ConnectionState = 'CONNECTING' | 'OPEN' | 'CLOSED' | 'ERROR'

export interface SubscriptionConfig {
  table: string
  event: RealtimeEvent
  filter?: string
  schema?: string
}

export interface Subscription {
  id: string
  config: SubscriptionConfig
  callback: Function
  channel: any
  createdAt: number
  lastActivity: number
  errorCount: number
}

export interface RealtimeStats {
  totalSubscriptions: number
  activeSubscriptions: number
  connectionState: ConnectionState
  totalEvents: number
  errorCount: number
  reconnectCount: number
  averageLatency: number
}

class UnifiedRealtimeService {
  private subscriptions = new Map<string, Subscription>()
  private connectionState = ref<ConnectionState>('CLOSED')
  private stats = ref<RealtimeStats>({
    totalSubscriptions: 0,
    activeSubscriptions: 0,
    connectionState: 'CLOSED',
    totalEvents: 0,
    errorCount: 0,
    reconnectCount: 0,
    averageLatency: 0
  })
  private eventLatencies: number[] = []
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000 // Start with 1 second
  private heartbeatInterval: number | null = null

  constructor() {
    this.initializeConnection()
    this.startHeartbeat()
  }

  /**
   * Subscribe to real-time events
   */
  subscribe(
    config: SubscriptionConfig,
    callback: Function,
    options: { deduplicate?: boolean } = {}
  ): Subscription {
    const subscriptionId = this.generateSubscriptionId(config)
    
    // Check for existing subscription if deduplication is enabled
    if (options.deduplicate && this.subscriptions.has(subscriptionId)) {
      console.log(`RealtimeService: Reusing existing subscription for ${subscriptionId}`)
      return this.subscriptions.get(subscriptionId)!
    }

    console.log(`RealtimeService: Creating subscription for ${config.table}`)

    const channel = supabase
      .channel(`${config.table}-${Date.now()}-${Math.random()}`)
      .on(
        'postgres_changes',
        {
          event: config.event,
          schema: config.schema || 'public',
          table: config.table,
          filter: config.filter
        },
        (payload) => {
          this.handleRealtimeEvent(subscriptionId, payload, callback)
        }
      )
      .subscribe((status) => {
        this.handleSubscriptionStatus(subscriptionId, status)
      })

    const subscription: Subscription = {
      id: subscriptionId,
      config,
      callback,
      channel,
      createdAt: Date.now(),
      lastActivity: Date.now(),
      errorCount: 0
    }

    this.subscriptions.set(subscriptionId, subscription)
    this.updateStats()

    return subscription
  }

  /**
   * Unsubscribe from real-time events
   */
  unsubscribe(subscription: Subscription | string): boolean {
    const id = typeof subscription === 'string' ? subscription : subscription.id
    const sub = this.subscriptions.get(id)

    if (!sub) {
      console.warn(`RealtimeService: Subscription ${id} not found`)
      return false
    }

    console.log(`RealtimeService: Unsubscribing from ${sub.config.table}`)

    try {
      supabase.removeChannel(sub.channel)
      this.subscriptions.delete(id)
      this.updateStats()
      return true
    } catch (error) {
      console.error(`RealtimeService: Error unsubscribing from ${id}:`, error)
      return false
    }
  }

  /**
   * Unsubscribe from all subscriptions
   */
  unsubscribeAll(): void {
    console.log(`RealtimeService: Unsubscribing from all ${this.subscriptions.size} subscriptions`)
    
    for (const subscription of this.subscriptions.values()) {
      try {
        supabase.removeChannel(subscription.channel)
      } catch (error) {
        console.error(`RealtimeService: Error removing channel:`, error)
      }
    }
    
    this.subscriptions.clear()
    this.updateStats()
  }

  /**
   * Get subscription by ID
   */
  getSubscription(id: string): Subscription | null {
    return this.subscriptions.get(id) || null
  }

  /**
   * Get all subscriptions for a table
   */
  getSubscriptionsForTable(table: string): Subscription[] {
    return Array.from(this.subscriptions.values())
      .filter(sub => sub.config.table === table)
  }

  /**
   * Get connection state
   */
  getConnectionState(): ConnectionState {
    return this.connectionState.value
  }

  /**
   * Get real-time statistics
   */
  getStats(): RealtimeStats {
    this.updateStats()
    return { ...this.stats.value }
  }

  /**
   * Manually reconnect
   */
  async reconnect(): Promise<void> {
    console.log('RealtimeService: Manual reconnection requested')
    this.connectionState.value = 'CONNECTING'
    
    // Store current subscriptions
    const currentSubscriptions = Array.from(this.subscriptions.values())
    
    // Unsubscribe from all
    this.unsubscribeAll()
    
    // Wait a bit before reconnecting
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Recreate subscriptions
    for (const sub of currentSubscriptions) {
      this.subscribe(sub.config, sub.callback, { deduplicate: false })
    }
    
    this.stats.value.reconnectCount++
  }

  /**
   * Check if service is healthy
   */
  isHealthy(): boolean {
    return this.connectionState.value === 'OPEN' && 
           this.stats.value.errorCount < 10 &&
           this.subscriptions.size > 0
  }

  /**
   * Cleanup and destroy service
   */
  destroy(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
    
    this.unsubscribeAll()
    this.connectionState.value = 'CLOSED'
  }

  // Private methods

  private generateSubscriptionId(config: SubscriptionConfig): string {
    const parts = [
      config.table,
      config.event,
      config.filter || 'no-filter',
      config.schema || 'public'
    ]
    return parts.join(':')
  }

  private handleRealtimeEvent(subscriptionId: string, payload: any, callback: Function): void {
    const startTime = Date.now()
    const subscription = this.subscriptions.get(subscriptionId)
    
    if (!subscription) {
      console.warn(`RealtimeService: Received event for unknown subscription ${subscriptionId}`)
      return
    }

    try {
      // Update subscription activity
      subscription.lastActivity = Date.now()
      
      // Call the callback
      callback(payload)
      
      // Track latency
      const latency = Date.now() - startTime
      this.trackLatency(latency)
      
      // Update stats
      this.stats.value.totalEvents++
      
    } catch (error) {
      console.error(`RealtimeService: Error handling event for ${subscriptionId}:`, error)
      subscription.errorCount++
      this.stats.value.errorCount++
    }
  }

  private handleSubscriptionStatus(subscriptionId: string, status: string): void {
    console.log(`RealtimeService: Subscription ${subscriptionId} status: ${status}`)
    
    switch (status) {
      case 'SUBSCRIBED':
        this.connectionState.value = 'OPEN'
        this.reconnectAttempts = 0
        this.reconnectDelay = 1000
        break
      case 'CHANNEL_ERROR':
      case 'TIMED_OUT':
        this.connectionState.value = 'ERROR'
        this.handleConnectionError()
        break
      case 'CLOSED':
        this.connectionState.value = 'CLOSED'
        break
    }
    
    this.updateStats()
  }

  private handleConnectionError(): void {
    this.stats.value.errorCount++
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1) // Exponential backoff
      
      console.log(`RealtimeService: Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`)
      
      setTimeout(() => {
        this.reconnect()
      }, delay)
    } else {
      console.error('RealtimeService: Max reconnection attempts reached')
    }
  }

  private trackLatency(latency: number): void {
    this.eventLatencies.push(latency)
    
    // Keep only last 100 latency measurements
    if (this.eventLatencies.length > 100) {
      this.eventLatencies.shift()
    }
    
    // Update average latency
    const sum = this.eventLatencies.reduce((a, b) => a + b, 0)
    this.stats.value.averageLatency = sum / this.eventLatencies.length
  }

  private updateStats(): void {
    this.stats.value.totalSubscriptions = this.subscriptions.size
    this.stats.value.activeSubscriptions = Array.from(this.subscriptions.values())
      .filter(sub => Date.now() - sub.lastActivity < 60000) // Active in last minute
      .length
    this.stats.value.connectionState = this.connectionState.value
  }

  private initializeConnection(): void {
    this.connectionState.value = 'CONNECTING'
    
    // Monitor Supabase connection state
    supabase.auth.onAuthStateChange((event) => {
      if (event === 'SIGNED_OUT') {
        this.unsubscribeAll()
        this.connectionState.value = 'CLOSED'
      } else if (event === 'SIGNED_IN') {
        this.connectionState.value = 'OPEN'
      }
    })
  }

  private startHeartbeat(): void {
    // Check connection health every 5 minutes - Reduced frequency
    this.heartbeatInterval = setInterval(() => {
      this.checkConnectionHealth()
    }, 300000) // 5 minutes instead of 30 seconds
  }

  private checkConnectionHealth(): void {
    const now = Date.now()
    const staleThreshold = 5 * 60 * 1000 // 5 minutes
    
    // Check for stale subscriptions
    for (const [id, subscription] of this.subscriptions.entries()) {
      if (now - subscription.lastActivity > staleThreshold) {
        console.warn(`RealtimeService: Subscription ${id} appears stale, removing`)
        this.unsubscribe(id)
      }
    }
    
    // Update stats
    this.updateStats()
  }
}

// Create singleton instance
const unifiedRealtimeService = new UnifiedRealtimeService()

// Export composable function
export function useUnifiedRealtime() {
  // Cleanup on component unmount
  onUnmounted(() => {
    // Note: We don't destroy the service here as it's shared across components
    // Individual subscriptions should be cleaned up by components
  })

  return {
    subscribe: unifiedRealtimeService.subscribe.bind(unifiedRealtimeService),
    unsubscribe: unifiedRealtimeService.unsubscribe.bind(unifiedRealtimeService),
    unsubscribeAll: unifiedRealtimeService.unsubscribeAll.bind(unifiedRealtimeService),
    getSubscription: unifiedRealtimeService.getSubscription.bind(unifiedRealtimeService),
    getSubscriptionsForTable: unifiedRealtimeService.getSubscriptionsForTable.bind(unifiedRealtimeService),
    getConnectionState: unifiedRealtimeService.getConnectionState.bind(unifiedRealtimeService),
    getStats: unifiedRealtimeService.getStats.bind(unifiedRealtimeService),
    reconnect: unifiedRealtimeService.reconnect.bind(unifiedRealtimeService),
    isHealthy: unifiedRealtimeService.isHealthy.bind(unifiedRealtimeService),
    connectionState: computed(() => unifiedRealtimeService.getConnectionState()),
    stats: computed(() => unifiedRealtimeService.getStats())
  }
}

// Export service instance for direct access if needed
export { unifiedRealtimeService }

// Export types
export type { SubscriptionConfig, Subscription, RealtimeStats }
