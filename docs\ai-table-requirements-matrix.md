# AI Implementation: Table Requirements Matrix

## Quick Reference Guide

This document provides a clear matrix of which tables need embeddings vs text2sql, with practical examples.

## 📊 **Table Requirements Matrix**

| Table Name | Embeddings Needed | Text2SQL | Primary Use Case | Example Query |
|------------|------------------|----------|------------------|---------------|
| **PROFILE TABLES** | | | | |
| `innovator_profiles` | ✅ `profile_embedding` | ✅ | Semantic matching + filtering | "Find AI startups seeking mentorship" |
| `mentor_profiles` | ✅ `profile_embedding` | ✅ | Semantic matching + filtering | "Mentors with fintech expertise >5 years" |
| `investor_profiles` | ✅ `profile_embedding` | ✅ | Semantic matching + filtering | "VCs interested in healthcare AI" |
| `professional_profiles` | ✅ `profile_embedding` | ✅ | Semantic matching + filtering | "Consultants with blockchain experience" |
| `industry_expert_profiles` | ✅ `profile_embedding` | ✅ | Semantic matching + filtering | "Experts in renewable energy" |
| `academic_student_profiles` | ✅ `profile_embedding` | ✅ | Semantic matching + filtering | "PhD students researching ML" |
| `academic_institution_profiles` | ✅ `profile_embedding` | ✅ | Semantic matching + filtering | "Universities with AI research programs" |
| `organisation_profiles` | ✅ `profile_embedding` | ✅ | Semantic matching + filtering | "NGOs focused on sustainability" |
| **CONTENT TABLES** | | | | |
| `posts` | ✅ `content_embedding` | ✅ | Content discovery + filtering | "Posts about AI ethics" |
| **INTERACTION TABLES** | | | | |
| `user_connections` | ❌ | ✅ | Relationship queries | "Show my pending connections" |
| `user_interests` | ❌ | ✅ | Interest tracking | "Users interested in fintech" |
| `matchmaking_results` | ❌ | ✅ | Analytics | "My match success rate" |
| `ai_user_interactions` | ❌ | ✅ | Behavior tracking | "My recent activity" |
| **SYSTEM TABLES** | | | | |
| `groups` | ❌ | ✅ | Group management | "Groups I'm a member of" |
| `marketplace_listings` | ❌ | ✅ | Marketplace queries | "Products under $1000" |
| `notifications` | ❌ | ✅ | Notification management | "Unread notifications" |
| `auth.users` | ❌ | ✅ | User management | "Users by signup date" |

## 🎯 **Embedding Requirements (Only 9 Columns)**

### **Profile Embeddings (8 tables)**
```sql
-- Core profile matching - ONE embedding per profile type
ALTER TABLE innovator_profiles ADD COLUMN profile_embedding vector(1536);
ALTER TABLE mentor_profiles ADD COLUMN profile_embedding vector(1536);
ALTER TABLE investor_profiles ADD COLUMN profile_embedding vector(1536);
ALTER TABLE professional_profiles ADD COLUMN profile_embedding vector(1536);
ALTER TABLE industry_expert_profiles ADD COLUMN profile_embedding vector(1536);
ALTER TABLE academic_student_profiles ADD COLUMN profile_embedding vector(1536);
ALTER TABLE academic_institution_profiles ADD COLUMN profile_embedding vector(1536);
ALTER TABLE organisation_profiles ADD COLUMN profile_embedding vector(1536);
```

### **Content Embeddings (1 table)**
```sql
-- Content discovery - ONE embedding for posts
ALTER TABLE posts ADD COLUMN content_embedding vector(1536);
```

**Total: 9 embedding columns across your entire database**

## 🔍 **Text2SQL Requirements (Everything Else)**

### **Relationship Queries**
```sql
-- user_connections: "Show my connections by type"
SELECT connection_type, COUNT(*) 
FROM user_connections 
WHERE user_id = $1 
GROUP BY connection_type;

-- user_interests: "Find users with similar interests"
SELECT ui1.user_id 
FROM user_interests ui1
JOIN user_interests ui2 ON ui1.interest_value = ui2.interest_value
WHERE ui2.user_id = $1 AND ui1.user_id != $1;
```

### **Analytics Queries**
```sql
-- matchmaking_results: "My successful matches"
SELECT * FROM matchmaking_results 
WHERE user_id = $1 AND match_score > 0.8
ORDER BY created_at DESC;

-- ai_user_interactions: "Platform usage analytics"
SELECT interaction_type, COUNT(*) 
FROM ai_user_interactions 
WHERE created_at > NOW() - INTERVAL '30 days'
GROUP BY interaction_type;
```

### **System Queries**
```sql
-- groups: "My active groups"
SELECT g.* FROM groups g
JOIN group_members gm ON g.id = gm.group_id
WHERE gm.user_id = $1 AND gm.status = 'active';

-- marketplace_listings: "Recent listings in category"
SELECT * FROM marketplace_listings 
WHERE category = $1 AND created_at > NOW() - INTERVAL '7 days'
ORDER BY created_at DESC;
```

## 💡 **Practical Implementation Examples**

### **Hybrid Query: Smart Mentor Discovery**
```typescript
async findMentors(query: string, filters: any) {
  // Determine if semantic or structured query
  if (isSemanticQuery(query)) {
    // "Find mentors who can help with AI startups"
    const embedding = await generateEmbedding(query);
    const semanticResults = await supabase.rpc('find_similar_profiles', {
      profile_type: 'mentor',
      query_embedding: embedding,
      match_threshold: 0.7
    });
    
    // Apply additional filters via SQL
    return await supabase
      .from('mentor_profiles')
      .select('*')
      .in('mentor_profile_id', semanticResults.map(r => r.profile_id))
      .gte('years_of_experience', filters.min_experience || 0)
      .eq('country', filters.country || null);
      
  } else {
    // "Mentors with >10 years experience in fintech"
    return await supabase
      .from('mentor_profiles')
      .select('*')
      .gte('years_of_experience', 10)
      .contains('areas_of_expertise', ['fintech']);
  }
}
```

### **Hybrid Query: Intelligent Content Feed**
```typescript
async getPersonalizedFeed(userId: string, section: string) {
  // Get user profile for semantic matching
  const userProfile = await getUserProfile(userId);
  const userEmbedding = await generateEmbedding(userProfile);
  
  // Find semantically similar content
  const similarContent = await supabase.rpc('find_similar_content', {
    content_type: 'posts',
    query_embedding: userEmbedding,
    match_threshold: 0.6
  });
  
  // Apply business rules via SQL
  return await supabase
    .from('posts')
    .select('*')
    .in('id', similarContent.map(c => c.content_id))
    .eq('status', 'published')
    .eq('post_type', section) // 'blog', 'event', 'opportunity'
    .gte('created_at', getDateFilter(section))
    .order('created_at', { ascending: false })
    .limit(20);
}
```

## 📈 **Performance & Storage Impact**

### **Storage Requirements**
```typescript
// Embedding storage calculation
const embeddingSize = 1536 * 4; // 6KB per embedding
const estimatedProfiles = {
  innovator: 5000,
  mentor: 2000, 
  investor: 1000,
  professional: 3000,
  expert: 1500,
  student: 4000,
  institution: 500,
  organisation: 1000
}; // Total: 18,000 profiles

const totalProfileEmbeddings = 18000 * 6; // ~108KB per profile type
const totalContentEmbeddings = 50000 * 6; // ~300MB for posts

// Total embedding storage: ~400MB (very manageable)
```

### **Query Performance**
```sql
-- Embedding queries with proper indexes
CREATE INDEX mentor_profiles_embedding_idx 
ON mentor_profiles USING ivfflat (profile_embedding vector_cosine_ops);
-- Performance: 50-200ms for similarity search

-- Text2SQL queries with existing indexes  
CREATE INDEX user_connections_user_idx ON user_connections(user_id);
-- Performance: 10-50ms for structured queries
```

## 🚀 **Implementation Priority**

### **Phase 1: Essential Embeddings (Week 1)**
```sql
-- Start with most important profile types
ALTER TABLE innovator_profiles ADD COLUMN profile_embedding vector(1536);
ALTER TABLE mentor_profiles ADD COLUMN profile_embedding vector(1536);
ALTER TABLE investor_profiles ADD COLUMN profile_embedding vector(1536);
ALTER TABLE posts ADD COLUMN content_embedding vector(1536);
```

### **Phase 2: Complete Profile Coverage (Week 2)**
```sql
-- Add remaining profile types
ALTER TABLE professional_profiles ADD COLUMN profile_embedding vector(1536);
ALTER TABLE industry_expert_profiles ADD COLUMN profile_embedding vector(1536);
-- Continue with other profile types
```

### **Phase 3: Optimization (Week 3)**
```sql
-- Optimize text2sql performance
CREATE INDEX user_connections_type_status_idx 
ON user_connections(connection_type, connection_status);

CREATE INDEX posts_type_date_idx 
ON posts(post_type, created_at DESC);
```

## ✅ **Summary**

### **Embeddings Needed:**
- **9 columns total** across 9 tables
- **~400MB storage** for entire platform
- **Used for**: Semantic similarity, content discovery, profile matching

### **Text2SQL Used:**
- **90% of your database** remains unchanged
- **All existing queries** continue to work
- **Used for**: Filtering, analytics, relationships, system operations

### **Benefits:**
- ✅ **Minimal overhead**: Only 9 embedding columns needed
- ✅ **Maximum impact**: Covers all semantic search use cases  
- ✅ **Cost effective**: Leverages existing database structure
- ✅ **Performance optimized**: Right tool for each job

**Bottom line**: You get powerful AI capabilities with minimal database changes!
