-- Migration: Add Vector Similarity Functions for Profile and Content Matching
-- File: supabase/migrations/20250116_add_vector_similarity_functions.sql
-- Description: Creates SQL functions for semantic similarity search across profiles and content

-- =============================================================================
-- PROFILE SIMILARITY SEARCH FUNCTIONS
-- =============================================================================

-- Function to search similar innovator profiles
CREATE OR REPLACE FUNCTION search_similar_innovator_profiles(
    query_embedding vector(384),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    exclude_user_id uuid DEFAULT NULL
)
RETURNS TABLE (
    innovator_profile_id uuid,
    user_id uuid,
    profile_name varchar,
    bio text,
    innovation_area text,
    similarity float,
    embedding_type text
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ip.innovator_profile_id,
        ip.user_id,
        ip.profile_name,
        ip.bio,
        ip.innovation_area,
        1 - (ip.profile_embedding <=> query_embedding) as similarity,
        'profile'::text as embedding_type
    FROM innovator_profiles ip
    WHERE ip.profile_embedding IS NOT NULL
        AND (exclude_user_id IS NULL OR ip.user_id != exclude_user_id)
        AND 1 - (ip.profile_embedding <=> query_embedding) > match_threshold
    ORDER BY ip.profile_embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Function to search similar investor profiles
CREATE OR REPLACE FUNCTION search_similar_investor_profiles(
    query_embedding vector(384),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    exclude_user_id uuid DEFAULT NULL
)
RETURNS TABLE (
    investor_profile_id uuid,
    user_id uuid,
    profile_name varchar,
    bio text,
    investment_focus jsonb,
    similarity float,
    embedding_type text
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ip.investor_profile_id,
        ip.user_id,
        ip.profile_name,
        ip.bio,
        ip.investment_focus,
        1 - (ip.profile_embedding <=> query_embedding) as similarity,
        'profile'::text as embedding_type
    FROM investor_profiles ip
    WHERE ip.profile_embedding IS NOT NULL
        AND (exclude_user_id IS NULL OR ip.user_id != exclude_user_id)
        AND 1 - (ip.profile_embedding <=> query_embedding) > match_threshold
    ORDER BY ip.profile_embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Function to search similar mentor profiles
CREATE OR REPLACE FUNCTION search_similar_mentor_profiles(
    query_embedding vector(384),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    exclude_user_id uuid DEFAULT NULL
)
RETURNS TABLE (
    mentor_profile_id uuid,
    user_id uuid,
    profile_name varchar,
    bio text,
    areas_of_expertise jsonb,
    similarity float,
    embedding_type text
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mp.mentor_profile_id,
        mp.user_id,
        mp.profile_name,
        mp.bio,
        mp.areas_of_expertise,
        1 - (mp.profile_embedding <=> query_embedding) as similarity,
        'profile'::text as embedding_type
    FROM mentor_profiles mp
    WHERE mp.profile_embedding IS NOT NULL
        AND (exclude_user_id IS NULL OR mp.user_id != exclude_user_id)
        AND 1 - (mp.profile_embedding <=> query_embedding) > match_threshold
    ORDER BY mp.profile_embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Generic function to search across all profile types
CREATE OR REPLACE FUNCTION search_similar_profiles_all_types(
    query_embedding vector(384),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    exclude_user_id uuid DEFAULT NULL,
    profile_types text[] DEFAULT NULL
)
RETURNS TABLE (
    profile_id uuid,
    user_id uuid,
    profile_type text,
    profile_name varchar,
    bio text,
    similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Search innovator profiles
    IF profile_types IS NULL OR 'innovator' = ANY(profile_types) THEN
        RETURN QUERY
        SELECT 
            ip.innovator_profile_id as profile_id,
            ip.user_id,
            'innovator'::text as profile_type,
            ip.profile_name,
            ip.bio,
            1 - (ip.profile_embedding <=> query_embedding) as similarity
        FROM innovator_profiles ip
        WHERE ip.profile_embedding IS NOT NULL
            AND (exclude_user_id IS NULL OR ip.user_id != exclude_user_id)
            AND 1 - (ip.profile_embedding <=> query_embedding) > match_threshold
        ORDER BY ip.profile_embedding <=> query_embedding
        LIMIT match_count;
    END IF;

    -- Search investor profiles
    IF profile_types IS NULL OR 'investor' = ANY(profile_types) THEN
        RETURN QUERY
        SELECT 
            ip.investor_profile_id as profile_id,
            ip.user_id,
            'investor'::text as profile_type,
            ip.profile_name,
            ip.bio,
            1 - (ip.profile_embedding <=> query_embedding) as similarity
        FROM investor_profiles ip
        WHERE ip.profile_embedding IS NOT NULL
            AND (exclude_user_id IS NULL OR ip.user_id != exclude_user_id)
            AND 1 - (ip.profile_embedding <=> query_embedding) > match_threshold
        ORDER BY ip.profile_embedding <=> query_embedding
        LIMIT match_count;
    END IF;

    -- Search mentor profiles
    IF profile_types IS NULL OR 'mentor' = ANY(profile_types) THEN
        RETURN QUERY
        SELECT 
            mp.mentor_profile_id as profile_id,
            mp.user_id,
            'mentor'::text as profile_type,
            mp.profile_name,
            mp.bio,
            1 - (mp.profile_embedding <=> query_embedding) as similarity
        FROM mentor_profiles mp
        WHERE mp.profile_embedding IS NOT NULL
            AND (exclude_user_id IS NULL OR mp.user_id != exclude_user_id)
            AND 1 - (mp.profile_embedding <=> query_embedding) > match_threshold
        ORDER BY mp.profile_embedding <=> query_embedding
        LIMIT match_count;
    END IF;
END;
$$;

-- =============================================================================
-- CONTENT SIMILARITY SEARCH FUNCTIONS
-- =============================================================================

-- Function to search similar posts by content
CREATE OR REPLACE FUNCTION search_similar_posts(
    query_embedding vector(384),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    exclude_post_id uuid DEFAULT NULL
)
RETURNS TABLE (
    id uuid,
    title varchar,
    content text,
    author_id uuid,
    tags jsonb,
    similarity float,
    created_at timestamp with time zone
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.title,
        p.content,
        p.author_id,
        p.tags,
        1 - (p.content_embedding <=> query_embedding) as similarity,
        p.created_at
    FROM posts p
    WHERE p.content_embedding IS NOT NULL
        AND (exclude_post_id IS NULL OR p.id != exclude_post_id)
        AND 1 - (p.content_embedding <=> query_embedding) > match_threshold
    ORDER BY p.content_embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Function to search posts by title similarity
CREATE OR REPLACE FUNCTION search_similar_posts_by_title(
    query_embedding vector(384),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id uuid,
    title varchar,
    content text,
    author_id uuid,
    similarity float,
    created_at timestamp with time zone
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.title,
        p.content,
        p.author_id,
        1 - (p.title_embedding <=> query_embedding) as similarity,
        p.created_at
    FROM posts p
    WHERE p.title_embedding IS NOT NULL
        AND 1 - (p.title_embedding <=> query_embedding) > match_threshold
    ORDER BY p.title_embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- =============================================================================
-- HYBRID MATCHING FUNCTIONS
-- =============================================================================

-- Function to get AI-enhanced match recommendations for a user
CREATE OR REPLACE FUNCTION get_ai_enhanced_matches(
    user_id_param uuid,
    target_profile_type text DEFAULT NULL,
    match_count int DEFAULT 10,
    similarity_threshold float DEFAULT 0.6
)
RETURNS TABLE (
    matched_user_id uuid,
    matched_profile_type text,
    profile_name varchar,
    bio text,
    ai_similarity_score float,
    traditional_match_score float,
    hybrid_score float,
    match_reasons jsonb
)
LANGUAGE plpgsql
AS $$
DECLARE
    user_profile_embedding vector(384);
    user_profile_type text;
BEGIN
    -- Get user's profile embedding and type
    SELECT 
        CASE 
            WHEN ip.profile_embedding IS NOT NULL THEN ip.profile_embedding
            WHEN inv.profile_embedding IS NOT NULL THEN inv.profile_embedding
            WHEN mp.profile_embedding IS NOT NULL THEN mp.profile_embedding
            ELSE NULL
        END,
        CASE 
            WHEN ip.profile_embedding IS NOT NULL THEN 'professional'
            WHEN inv.profile_embedding IS NOT NULL THEN 'innovator'
            WHEN mp.profile_embedding IS NOT NULL THEN 'mentor'
            ELSE 'unknown'
        END
    INTO user_profile_embedding, user_profile_type
    FROM personal_details pd
    LEFT JOIN professional_profiles ip ON pd.user_id = ip.user_id
    LEFT JOIN innovator_profiles inv ON pd.user_id = inv.user_id
    LEFT JOIN mentor_profiles mp ON pd.user_id = mp.user_id
    WHERE pd.user_id = user_id_param;

    -- If no embedding found, return empty result
    IF user_profile_embedding IS NULL THEN
        RETURN;
    END IF;

    -- Search for similar profiles and combine with traditional matching
    -- This is a simplified version - in practice, you'd integrate with existing matchmaking logic
    RETURN QUERY
    SELECT 
        sp.user_id as matched_user_id,
        sp.profile_type as matched_profile_type,
        sp.profile_name,
        sp.bio,
        sp.similarity as ai_similarity_score,
        0.5::float as traditional_match_score, -- Placeholder for traditional score
        (sp.similarity * 0.6 + 0.5 * 0.4)::float as hybrid_score, -- Weighted combination
        jsonb_build_object(
            'ai_similarity', sp.similarity,
            'traditional_score', 0.5,
            'match_type', 'semantic_similarity'
        ) as match_reasons
    FROM search_similar_profiles_all_types(
        user_profile_embedding, 
        similarity_threshold, 
        match_count * 2, -- Get more candidates for filtering
        user_id_param,
        CASE WHEN target_profile_type IS NOT NULL THEN ARRAY[target_profile_type] ELSE NULL END
    ) sp
    WHERE sp.similarity >= similarity_threshold
    ORDER BY hybrid_score DESC
    LIMIT match_count;
END;
$$;

-- =============================================================================
-- COMMENTS AND DOCUMENTATION
-- =============================================================================

COMMENT ON FUNCTION search_similar_innovator_profiles IS 'Search for innovator profiles similar to a given embedding vector';
COMMENT ON FUNCTION search_similar_investor_profiles IS 'Search for investor profiles similar to a given embedding vector';
COMMENT ON FUNCTION search_similar_mentor_profiles IS 'Search for mentor profiles similar to a given embedding vector';
COMMENT ON FUNCTION search_similar_profiles_all_types IS 'Search across all profile types for semantic similarity';
COMMENT ON FUNCTION search_similar_posts IS 'Search for posts with similar content using vector similarity';
COMMENT ON FUNCTION search_similar_posts_by_title IS 'Search for posts with similar titles using vector similarity';
COMMENT ON FUNCTION get_ai_enhanced_matches IS 'Get AI-enhanced match recommendations combining semantic similarity with traditional matching';
