<template>
  <q-page padding>
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <div class="row items-center q-mb-md">
          <div class="col">
            <h3 class="q-mt-none q-mb-none">Content Matchmaking</h3>
            <p class="q-mt-sm q-mb-none text-body1">
              Discover content tailored to your profile, interests, and goals.
            </p>
          </div>
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-3">
            <q-card class="q-mb-md">
              <q-card-section>
                <div class="text-h6">How Content Matching Works</div>
                <p class="q-mt-sm q-mb-none text-body2">
                  Our AI-powered system analyzes your profile data to find the most relevant content for you.
                </p>
              </q-card-section>
              <q-separator />
              <q-card-section>
                <div class="row q-col-gutter-sm">
                  <div class="col-12">
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="person_search" color="primary" size="sm" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>Profile Analysis</q-item-label>
                        <q-item-label caption>
                          We analyze your goals, interests, and profile data
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>
                  <div class="col-12">
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="auto_awesome" color="primary" size="sm" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>Content Matching</q-item-label>
                        <q-item-label caption>
                          Our algorithm finds relevant content for you
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>
                  <div class="col-12">
                    <q-item>
                      <q-item-section avatar>
                        <q-icon name="explore" color="primary" size="sm" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>Discover</q-item-label>
                        <q-item-label caption>
                          Explore content that matches your interests
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>
                </div>
              </q-card-section>
            </q-card>

            <q-card>
              <q-card-section>
                <div class="text-h6">Content Filters</div>
                <p class="q-mt-sm q-mb-none text-body2">
                  Refine your content matches based on type and match quality
                </p>
              </q-card-section>

              <q-separator />

              <q-card-section>
                <div class="q-mb-md">
                  <div class="text-subtitle2 q-mb-sm">Content Type</div>
                  <q-option-group
                    v-model="selectedContentType"
                    :options="contentTypeOptions"
                    color="primary"
                    type="radio"
                  />
                </div>

                <div class="q-mb-md">
                  <div class="text-subtitle2 q-mb-sm">
                    Match Quality ({{ Math.round(minScore * 100) }}%)
                    <q-tooltip>Higher percentages show only the strongest matches</q-tooltip>
                  </div>
                  <q-slider
                    v-model="minScore"
                    :min="0"
                    :max="1"
                    :step="0.05"
                    label
                    label-always
                    color="primary"
                    :label-value="`${Math.round(minScore * 100)}%`"
                  />
                </div>

                <div class="q-mt-lg">
                  <q-btn
                    color="primary"
                    icon="refresh"
                    label="Generate Matches"
                    class="full-width"
                    :loading="loading"
                    @click="generateContentMatches"
                  />
                </div>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-9">
            <div v-if="loading" class="text-center q-pa-lg">
              <q-spinner color="primary" size="3em" />
              <div class="q-mt-sm">Finding content matches...</div>
            </div>
            <div v-else-if="!contentMatches.length" class="text-center q-pa-lg">
              <q-icon name="search_off" size="3em" color="grey-7" />
              <div class="q-mt-sm">No content matches found</div>
              <div class="q-mt-xs text-caption">
                Try adjusting your filters or click "Generate Matches" to find content that matches your profile.
              </div>
            </div>
            <div v-else>
              <div class="text-h6 q-mb-md">{{ matchListTitle }}</div>
              <content-match-card
                v-for="match in contentMatches"
                :key="match.id"
                :match="match"
              />
              
              <div class="text-center q-mt-md">
                <q-btn
                  v-if="hasMoreMatches"
                  flat
                  color="primary"
                  :loading="loadingMore"
                  @click="loadMoreMatches"
                >
                  Load More
                </q-btn>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useNotificationStore } from '@/stores/notifications';
import { useAuthStore } from '@/stores/auth';
import { useProfileStore } from '@/stores/profile';
import { useMatchmakingStore } from '@/stores/matchmaking';
import ContentMatchCard from '@/components/matchmaking/ContentMatchCard.vue';

// Stores
const notificationStore = useNotificationStore();
const authStore = useAuthStore();
const profileStore = useProfileStore();
const matchmakingStore = useMatchmakingStore();

// State
const selectedContentType = ref('all');
const minScore = ref(0.3);
const loading = ref(false);
const loadingMore = ref(false);
const contentMatches = ref([]);
const hasMoreMatches = ref(false);

// Content type options
const contentTypeOptions = computed(() => [
  { label: 'All Content', value: 'all' },
  { label: 'Events', value: 'event' },
  { label: 'Blog Posts', value: 'blog' },
  { label: 'Opportunities', value: 'opportunity' },
  { label: 'Groups', value: 'group' },
  { label: 'Marketplace', value: 'marketplace' }
]);

// Match list title
const matchListTitle = computed(() => {
  if (selectedContentType.value === 'all') {
    return 'All Content Matches';
  }
  
  // Convert to title case
  return selectedContentType.value
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ') + ' Matches';
});

// Methods
async function generateContentMatches() {
  if (!authStore.isAuthenticated) {
    notificationStore.error('You must be logged in to generate content matches');
    return;
  }

  loading.value = true;
  
  try {
    // This will be replaced with actual implementation
    // For now, we'll simulate content matches
    await simulateContentMatches();
    
    notificationStore.success('Content matches generated successfully');
  } catch (error: any) {
    console.error('Error generating content matches:', error);
    notificationStore.error(`Error generating content matches: ${error.message}`);
  } finally {
    loading.value = false;
  }
}

async function loadMoreMatches() {
  loadingMore.value = true;
  
  try {
    // This will be replaced with actual implementation
    // For now, we'll simulate loading more content matches
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // No more matches to load
    hasMoreMatches.value = false;
  } catch (error: any) {
    console.error('Error loading more content matches:', error);
    notificationStore.error(`Error loading more content matches: ${error.message}`);
  } finally {
    loadingMore.value = false;
  }
}

// Function to fetch real content matches from database
async function fetchContentMatches() {
  // Simulate API call delay for now - replace with actual API call
  await new Promise(resolve => setTimeout(resolve, 1500));

  // TODO: Replace with actual content matching service
  // For now, return empty array until real implementation
  const matches = [];

  // Sort by match score (highest first) and update state
  contentMatches.value = matches.sort((a, b) => b.matchScore - a.matchScore);

  // Set hasMoreMatches based on the number of matches
  hasMoreMatches.value = contentMatches.value.length > 5;

  return matches;
}

// Initialize when component mounts
onMounted(async () => {
  try {
    // Load user profiles if not already loaded
    if (profileStore.userProfiles.length === 0) {
      await profileStore.loadUserProfiles();
    }
    
    // Generate initial content matches
    await generateContentMatches();
  } catch (error) {
    console.error('Error initializing content matchmaking:', error);
    notificationStore.error('Failed to initialize content matchmaking. Please try again later.');
  }
});
</script>
