<template>
  <q-card class="mentorship-event-registration-form">
    <q-card-section class="bg-primary text-white">
      <div class="text-h6">
        <q-icon name="event" class="q-mr-sm" />
        Register for Mentorship Event
      </div>
      <div class="text-subtitle2">
        Join this mentorship event and connect with experienced mentors
      </div>
    </q-card-section>

    <q-form @submit="handleSubmit" class="q-pa-md">
      <!-- Event Information Display -->
      <div class="event-info q-mb-md q-pa-md bg-grey-1 rounded-borders">
        <div class="text-h6 q-mb-xs">{{ event.title }}</div>
        <div class="text-body2 text-grey-7 q-mb-sm">{{ event.description }}</div>
        <div class="row q-gutter-sm">
          <q-chip color="primary" text-color="white" size="sm">
            {{ formatEventType(event.event_type) }}
          </q-chip>
          <q-chip color="green" text-color="white" size="sm" v-if="event.is_free">
            Free
          </q-chip>
          <q-chip color="orange" text-color="white" size="sm" v-else>
            ${{ event.price }}
          </q-chip>
        </div>
      </div>

      <!-- Registration Form -->
      <q-input
        v-model="form.title"
        label="Registration Title *"
        outlined
        :rules="[val => !!val || 'Title is required']"
        hint="Brief title for your registration request"
        class="q-mb-md"
      />

      <q-input
        v-model="form.message"
        label="Message *"
        type="textarea"
        outlined
        rows="4"
        :rules="[val => !!val || 'Message is required']"
        hint="Tell the mentor why you want to join this event and what you hope to learn"
        class="q-mb-md"
      />

      <!-- Goals Selection -->
      <div class="q-mb-md">
        <q-label class="q-mb-sm">What are your main goals for this event? *</q-label>
        <q-option-group
          v-model="form.goals"
          :options="goalOptions"
          type="checkbox"
          color="primary"
        />
      </div>

      <!-- Specific Areas -->
      <div class="q-mb-md">
        <q-label class="q-mb-sm">Specific areas you'd like to focus on:</q-label>
        <q-option-group
          v-model="form.specific_areas"
          :options="specificAreaOptions"
          type="checkbox"
          color="primary"
        />
      </div>

      <!-- Background -->
      <q-input
        v-model="form.background"
        label="Your Background"
        type="textarea"
        outlined
        rows="3"
        hint="Brief description of your background and experience"
        class="q-mb-md"
      />

      <!-- Special Requirements -->
      <q-input
        v-model="form.special_requirements"
        label="Special Requirements"
        type="textarea"
        outlined
        rows="2"
        hint="Any special accommodations or requirements you need"
        class="q-mb-md"
      />

      <!-- Actions -->
      <q-card-actions align="right" class="q-mt-lg">
        <q-btn
          flat
          label="Cancel"
          @click="$emit('cancel')"
          class="q-mr-sm"
        />
        <q-btn
          type="submit"
          color="primary"
          label="Register for Event"
          :loading="loading"
          :disable="!isFormValid"
        />
      </q-card-actions>
    </q-form>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useNotificationStore } from '../../stores/notifications'

// Props
const props = defineProps<{
  event: any
  loading?: boolean
}>()

// Emits
const emit = defineEmits<{
  submit: [registrationData: any]
  cancel: []
}>()

// Stores
const notifications = useNotificationStore()

// Form data
const form = ref({
  title: '',
  message: '',
  goals: [] as string[],
  specific_areas: [] as string[],
  background: '',
  special_requirements: ''
})

// Options
const goalOptions = [
  { label: 'Learn new skills', value: 'learn-skills' },
  { label: 'Get career guidance', value: 'career-guidance' },
  { label: 'Network with professionals', value: 'networking' },
  { label: 'Get project feedback', value: 'project-feedback' },
  { label: 'Explore opportunities', value: 'explore-opportunities' }
]

const specificAreaOptions = [
  { label: 'Technology & Innovation', value: 'technology' },
  { label: 'Business Development', value: 'business' },
  { label: 'Marketing & Sales', value: 'marketing' },
  { label: 'Finance & Investment', value: 'finance' },
  { label: 'Leadership & Management', value: 'leadership' },
  { label: 'Product Development', value: 'product' }
]

// Computed
const isFormValid = computed(() => {
  return form.value.title && 
         form.value.message && 
         form.value.goals.length > 0
})

// Methods
function formatEventType(type: string): string {
  return type.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

function handleSubmit() {
  if (!isFormValid.value) {
    notifications.error('Please fill in all required fields')
    return
  }

  const registrationData = {
    event_id: props.event.id,
    title: form.value.title,
    message: form.value.message,
    goals: form.value.goals,
    specific_areas: form.value.specific_areas,
    background: form.value.background,
    special_requirements: form.value.special_requirements
  }

  emit('submit', registrationData)
}
</script>

<style scoped>
.mentorship-event-registration-form {
  max-width: 600px;
  margin: 0 auto;
}

.event-info {
  border-left: 4px solid #1976d2;
}

.q-label {
  font-weight: 500;
  color: #424242;
}
</style>
