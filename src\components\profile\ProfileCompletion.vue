<template>
  <div class="profile-completion q-pa-md">
    <q-card class="q-mb-lg">
      <q-card-section>
        <div class="text-h6">Complete Your Profile</div>
        <div class="text-subtitle2 q-mt-sm">
          Please complete your profile to get the most out of the ZbInnovation.
        </div>

        <!-- Progress Bar -->
        <div class="q-mt-md">
          <ProfileCompletionIndicator
            :profile-data="profileTypeData"
            show-steps
            :total-steps="totalSteps"
          />
        </div>
      </q-card-section>
    </q-card>

    <!-- Stepper -->
    <q-stepper
      v-model="step"
      vertical
      color="primary"
      animated
      ref="stepper"
      alternative-labels
    >
      <!-- Step 1: Personal Information -->
      <q-step
        :name="1"
        title="Personal Information"
        :done="step > 1"
        :header-nav="step > 1"
        prefix="1"
      >
        <div class="q-pa-md">
          <div class="text-subtitle1 q-mb-md">Basic Information</div>
          <q-form @submit="savePersonalInfo" ref="personalInfoForm">
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <q-input
                  v-model="personalInfo.firstName"
                  label="First Name *"
                  outlined
                  :rules="[val => !!val || 'First name is required']"
                />
              </div>
              <div class="col-12 col-md-6">
                <q-input
                  v-model="personalInfo.lastName"
                  label="Last Name *"
                  outlined
                  :rules="[val => !!val || 'Last name is required']"
                />
              </div>
              <div class="col-12">
                <q-input
                  v-model="personalInfo.email"
                  label="Email *"
                  outlined
                  type="email"
                  readonly
                  disable
                />
              </div>
              <div class="col-12 col-md-6">
                <q-input
                  v-model="personalInfo.phone"
                  label="Phone Number *"
                  outlined
                  mask="(###) ### - ####"
                  :rules="[val => !!val || 'Phone number is required']"
                />
              </div>
              <div class="col-12 col-md-6">
                <q-select
                  v-model="personalInfo.category"
                  :options="categoryOptions"
                  label="Category *"
                  outlined
                  :rules="[val => !!val || 'Category is required']"
                  emit-value
                  map-options
                />
              </div>
              <div class="col-12">
                <q-select
                  v-model="personalInfo.hearAboutUs"
                  :options="hearAboutUsOptions"
                  label="How did you hear about us? *"
                  outlined
                  multiple
                  use-chips
                  :rules="[val => val.length > 0 || 'Please select at least one option']"
                />
              </div>
            </div>
            <div class="q-mt-md">
              <q-btn
                label="Save & Continue"
                type="submit"
                color="primary"
                :loading="saving"
              />
            </div>
          </q-form>
        </div>
      </q-step>

      <!-- Step 2: Profile Type Specific Information -->
      <q-step
        :name="2"
        title="Profile Details"
        :done="step > 2"
        :header-nav="step > 2"
        prefix="2"
      >
        <div class="q-pa-md">
          <div class="text-subtitle1 q-mb-md">{{ getProfileTypeTitle() }} Information</div>

          <!-- Dynamic Profile Form -->
          <dynamic-profile-form
            v-if="getProfileType(personalInfo.category)"
            :profile-type="getProfileType(personalInfo.category)"
            v-model="profileTypeData"
            @save="saveProfileTypeInfo"
            :loading="saving"
          />

          <div v-else class="text-center q-pa-lg">
            <q-icon name="warning" color="warning" size="3rem" />
            <div class="text-h6 q-mt-md">Please select a category in the previous step</div>
            <q-btn
              label="Go Back"
              color="primary"
              class="q-mt-md"
              @click="step = 1"
            />
          </div>
        </div>
      </q-step>

      <!-- Step 3: Review & Finish -->
      <q-step
        :name="3"
        title="Review & Finish"
        prefix="3"
      >
        <div class="q-pa-md">
          <div class="text-subtitle1 q-mb-md">Review Your Information</div>

          <q-card flat bordered class="q-mb-md">
            <q-card-section>
              <div class="text-subtitle2">Personal Information</div>
              <q-list>
                <q-item>
                  <q-item-section>
                    <q-item-label caption>Name</q-item-label>
                    <q-item-label>{{ personalInfo.firstName }} {{ personalInfo.lastName }}</q-item-label>
                  </q-item-section>
                </q-item>
                <q-item>
                  <q-item-section>
                    <q-item-label caption>Email</q-item-label>
                    <q-item-label>{{ personalInfo.email }}</q-item-label>
                  </q-item-section>
                </q-item>
                <q-item>
                  <q-item-section>
                    <q-item-label caption>Phone</q-item-label>
                    <q-item-label>{{ personalInfo.phone }}</q-item-label>
                  </q-item-section>
                </q-item>
                <q-item>
                  <q-item-section>
                    <q-item-label caption>Category</q-item-label>
                    <q-item-label>{{ personalInfo.category }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
            <q-card-actions>
              <q-btn flat color="primary" label="Edit" @click="step = 1" />
            </q-card-actions>
          </q-card>

          <q-card flat bordered>
            <q-card-section>
              <div class="text-subtitle2">{{ getProfileTypeTitle() }} Information</div>
              <profile-type-summary
                :profile-type="personalInfo.category"
                :profile-data="profileTypeData"
              />
            </q-card-section>
            <q-card-actions>
              <q-btn flat color="primary" label="Edit" @click="step = 2" />
            </q-card-actions>
          </q-card>

          <div class="q-mt-lg">
            <q-btn
              label="Complete Profile"
              color="primary"
              @click="completeProfile"
              :loading="saving"
            />
          </div>
        </div>
      </q-step>
    </q-stepper>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { useNotificationStore } from '../../stores/notifications'
import { useProfileStore } from '../../stores/profile'
import { useRouter } from 'vue-router'
import { supabase } from '../../lib/supabase'
import { useGlobalServicesStore } from '../../stores/globalServices'
import ProfileCompletionIndicator from './ProfileCompletionIndicator.vue'
import DynamicProfileForm from './DynamicProfileForm.vue'

const authStore = useAuthStore()
const notifications = useNotificationStore()
const profileStore = useProfileStore()
const router = useRouter()
const globalServices = useGlobalServicesStore()

const step = ref(1)
const saving = ref(false)
const personalInfoForm = ref<any>(null)
const stepper = ref<any>(null)

// Profile completion tracking
const { calculateCompletion } = useProfileCompletion()
const completedSteps = ref(0)
const totalSteps = ref(3)
const completionPercentage = computed(() => {
  // Use the centralized profile completion service
  if (profileStore.currentProfile) {
    return calculateCompletion(profileStore.currentProfile, profileTypeData.value)
  }
  // Fallback to local calculation
  return (completedSteps.value / totalSteps.value) * 100
})

// Form data
const personalInfo = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  category: '',
  hearAboutUs: []
})

const profileTypeData = ref({})

// Options for select fields
const categoryOptions = [
  { label: 'Innovator', value: 'Innovator' },
  { label: 'Business Investor', value: 'Business Investor' },
  { label: 'Mentor', value: 'Mentor' },
  { label: 'Professional', value: 'Professional' },
  { label: 'Industry Expert', value: 'Industry Expert' },
  { label: 'Academic Student', value: 'Academic Student' },
  { label: 'Academic Institution', value: 'Academic Institution' },
  { label: 'Organisation', value: 'Organisation' }
]

const hearAboutUsOptions = [
  'Social Media',
  'Search Engine',
  'Friend/Colleague',
  'News Article',
  'Event',
  'Advertisement',
  'Other'
]

// Load user data on mount
onMounted(async () => {
  if (!authStore.user) {
    router.push('/sign-in')
    return
  }

  // Load existing profile data if available
  if (authStore.userProfile) {
    personalInfo.value.firstName = authStore.userProfile.first_name || ''
    personalInfo.value.lastName = authStore.userProfile.last_name || ''
    personalInfo.value.email = authStore.userProfile.email || ''
    personalInfo.value.phone = authStore.userProfile.phone || ''

    // Get user metadata for category
    const metadata = authStore.user.user_metadata
    if (metadata && metadata.category) {
      personalInfo.value.category = metadata.category
    }

    // Parse hear_about_us if it's a string
    if (authStore.userProfile.hear_about_us) {
      try {
        if (typeof authStore.userProfile.hear_about_us === 'string') {
          personalInfo.value.hearAboutUs = JSON.parse(authStore.userProfile.hear_about_us)
        } else {
          personalInfo.value.hearAboutUs = authStore.userProfile.hear_about_us
        }
      } catch (e) {
        personalInfo.value.hearAboutUs = [authStore.userProfile.hear_about_us]
      }
    }

    // Update completion steps
    updateCompletionSteps()

    // Load profile type specific data if available
    if (authStore.userProfile.profile_type && authStore.userProfile.profile_type_id) {
      await loadProfileTypeData()
    }
  }
})

// Watch for changes in personal info to update completion steps
watch(personalInfo, () => {
  updateCompletionSteps()
}, { deep: true })

// Watch for changes in profile type data to update completion steps
watch(profileTypeData, () => {
  updateCompletionSteps()
}, { deep: true })

// Update completion steps based on form data
function updateCompletionSteps() {
  let completed = 0

  // Check if personal info is complete
  if (
    personalInfo.value.firstName &&
    personalInfo.value.lastName &&
    personalInfo.value.email &&
    personalInfo.value.phone &&
    personalInfo.value.category &&
    personalInfo.value.hearAboutUs.length > 0
  ) {
    completed++
  }

  // Check if profile type data is complete
  if (Object.keys(profileTypeData.value).length > 0) {
    // This is a simplified check - each profile type form should implement its own validation
    completed++
  }

  // Set completed steps
  completedSteps.value = completed
}

// Save personal info and move to next step
async function savePersonalInfo() {
  try {
    const valid = await personalInfoForm.value.validate()
    if (!valid) return

    saving.value = true

    // Update profile in database
    const { error } = await authStore.updateProfile({
      first_name: personalInfo.value.firstName,
      last_name: personalInfo.value.lastName,
      phone: personalInfo.value.phone,
      hear_about_us: personalInfo.value.hearAboutUs
    })

    if (error) throw error

    // Update profile completion using the store
    if (profileStore.currentProfile && typeof profileStore.updateProfileCompletion === 'function') {
      try {
        await profileStore.updateProfileCompletion()
      } catch (error) {
        console.error('Error updating profile completion:', error)
      }
    }

    // Move to next step
    step.value = 2
    notifications.success('Personal information saved successfully')
  } catch (error: any) {
    notifications.error('Failed to save personal information: ' + error.message)
  } finally {
    saving.value = false
  }
}

// Save profile type info and move to next step
async function saveProfileTypeInfo() {
  try {
    saving.value = true

    // Get profile type ID
    const profileType = getProfileTypeKey()
    const { data: profileTypeData, error: profileTypeError } = await supabase
      .from('profile_types')
      .select('id')
      .eq('name', profileType)
      .single()

    if (profileTypeError) throw profileTypeError

    // Update main profile with profile type info
    const { error: profileUpdateError } = await authStore.updateProfile({
      profile_type: profileType,
      profile_type_id: profileTypeData.id,
      profile_completion: ((completedSteps.value + 1) / totalSteps.value) * 100
    })

    if (profileUpdateError) throw profileUpdateError

    // Save profile type specific data
    await saveProfileTypeSpecificData(profileType, profileTypeData.id)

    // Move to next step
    step.value = 3
    notifications.success('Profile details saved successfully')
  } catch (error: any) {
    notifications.error('Failed to save profile details: ' + error.message)
  } finally {
    saving.value = false
  }
}

// Complete profile and redirect to dashboard
async function completeProfile() {
  try {
    saving.value = true

    // Update profile completion using the store
    if (profileStore.currentProfile && typeof profileStore.updateProfileCompletion === 'function') {
      try {
        await profileStore.updateProfileCompletion()
      } catch (error) {
        console.error('Error updating profile completion:', error)
      }
    }

    // Mark profile as complete (100%)
    const { error } = await authStore.updateProfile({
      profile_completion: 100
    })

    if (error) throw error

    notifications.success('Profile completed successfully!')
    router.push('/dashboard')
  } catch (error: any) {
    notifications.error('Failed to complete profile: ' + error.message)
  } finally {
    saving.value = false
  }
}

// Helper function to get profile type key
function getProfileTypeKey() {
  return getProfileType(personalInfo.value.category) || ''
}

// Convert category display name to profile type key
function getProfileType(category: string): string | null {
  const mapping: Record<string, string> = {
    'Innovator': 'innovator',
    'Business Investor': 'investor',
    'Mentor': 'mentor',
    'Professional': 'professional',
    'Industry Expert': 'industry_expert',
    'Academic Student': 'academic_student',
    'Academic Institution': 'academic_institution',
    'Organisation': 'organisation'
  }

  return mapping[category] || null
}

// Helper function to get profile type title
function getProfileTypeTitle() {
  return personalInfo.value.category || 'Profile'
}

// Save profile type specific data
async function saveProfileTypeSpecificData(profileType: string, profileTypeId: string) {
  if (!authStore.user) return

  const tableName = `${profileType}_profiles`

  // Check if profile type data already exists
  const { data: existingData, error: checkError } = await supabase
    .from(tableName)
    .select('id')
    .eq('profile_id', profileTypeId)
    .maybeSingle()

  if (checkError && checkError.code !== 'PGRST116') {
    throw checkError
  }

  // Prepare data with profile_id
  const data = {
    ...profileTypeData.value,
    profile_id: profileTypeId
  }

  if (existingData) {
    // Update existing data
    const { error } = await supabase
      .from(tableName)
      .update(data)
      .eq('id', existingData.id)

    if (error) throw error
  } else {
    // Insert new data
    const { error } = await supabase
      .from(tableName)
      .insert([data])

    if (error) throw error
  }
}

// Load profile type specific data
async function loadProfileTypeSpecificData() {
  if (!authStore.userProfile?.profile_type || !authStore.user) return

  const profileType = authStore.userProfile.profile_type
  const profileId = authStore.user.id
  const tableName = `${profileType}_profiles`

  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .eq('profile_id', profileId)
      .maybeSingle()

    if (error) throw error

    if (data) {
      // Remove id and profile_id from data
      const { id, profile_id, created_at, updated_at, ...rest } = data
      profileTypeData.value = rest
    }
  } catch (error) {
    console.error('Error loading profile type data:', error)
  }
}
</script>

<style scoped>
.profile-completion {
  max-width: 800px;
  margin: 0 auto;
}
</style>
