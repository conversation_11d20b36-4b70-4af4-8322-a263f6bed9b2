<template>
  <!-- Sign In Dialog -->
  <q-dialog v-model="state.isSignInOpen" persistent>
    <q-card class="auth-dialog" style="min-width: 350px; max-width: 400px">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Welcome Back</div>
        <q-space />
        <q-btn flat round dense @click="closeAllDialogs">
          <q-icon name="close" />
        </q-btn>
      </q-card-section>

      <q-card-section>
        <auth-options
          mode="login"
          @email-password="handleEmailPasswordSignIn"
        />
      </q-card-section>

      <!-- Toggle to Sign Up -->
      <q-card-section class="text-center q-pt-none">
        <q-btn
          flat
          no-caps
          color="primary"
          @click="switchToSignUp"
          class="text-caption toggle-btn"
          size="sm"
        >
          Don't have an account? Sign Up
        </q-btn>
      </q-card-section>
    </q-card>
  </q-dialog>

  <!-- Sign Up Dialog -->
  <q-dialog v-model="state.isSignUpOpen" persistent>
    <q-card class="auth-dialog" style="min-width: 350px; max-width: 400px">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Join Our Early Access Program</div>
        <q-space />
        <q-btn flat round dense @click="closeAllDialogs">
          <q-icon name="close" />
        </q-btn>
      </q-card-section>

      <q-card-section>
        <auth-options
          mode="signup"
          @email-password-signup="handleEmailPasswordSignUp"
        />
      </q-card-section>

      <!-- Toggle to Sign In -->
      <q-card-section class="text-center q-pt-none">
        <q-btn
          flat
          no-caps
          color="primary"
          @click="switchToSignIn"
          class="text-caption toggle-btn"
          size="sm"
        >
          Already have an account? Sign In
        </q-btn>
      </q-card-section>
    </q-card>
  </q-dialog>

  <!-- Email/Password Sign In Form Dialog -->
  <q-dialog v-model="state.isEmailPasswordSignInOpen" persistent>
    <q-card class="auth-dialog" style="min-width: 350px; max-width: 400px">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Sign In</div>
        <q-space />
        <q-btn flat round dense @click="closeAllDialogs">
          <q-icon name="close" />
        </q-btn>
      </q-card-section>

      <q-card-section>
        <q-form @submit="handleSignIn" class="q-gutter-md">
          <q-input
            :model-value="signInFormData.email"
            @update:model-value="(val) => updateSignInForm('email', val)"
            type="email"
            label="Email"
            :rules="emailRules"
            outlined
            dense
          />
          <q-input
            :model-value="signInFormData.password"
            @update:model-value="(val) => updateSignInForm('password', val)"
            :type="state.isPwd ? 'password' : 'text'"
            label="Password"
            :rules="passwordRules"
            outlined
            dense
          >
            <template v-slot:append>
              <q-icon
                :name="state.isPwd ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="togglePasswordVisibility"
              />
            </template>
          </q-input>
          <div class="text-center q-mt-md">
            <q-btn
              type="submit"
              label="Sign In"
              :loading="state.loading"
              class="zb-btn-primary full-width"
            />
          </div>
        </q-form>
      </q-card-section>

      <!-- Toggle to Sign Up -->
      <q-card-section class="text-center q-pt-none">
        <q-btn
          @click="switchToSignUpForm"
          class="zb-btn-flat text-caption toggle-btn"
        >
          Don't have an account? Sign Up
        </q-btn>
      </q-card-section>
    </q-card>
  </q-dialog>

  <!-- Email/Password Sign Up Form Dialog -->
  <q-dialog v-model="state.isEmailPasswordSignUpOpen" persistent>
    <q-card class="auth-dialog" style="min-width: 350px; max-width: 400px">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Create Account</div>
        <q-space />
        <q-btn flat round dense @click="closeAllDialogs">
          <q-icon name="close" />
        </q-btn>
      </q-card-section>

      <q-card-section>
        <q-form @submit="handleSignUp" class="q-gutter-md">
          <q-input
            :model-value="signUpFormData.email"
            @update:model-value="(val) => updateSignUpForm('email', val)"
            type="email"
            label="Email"
            :rules="emailRules"
            outlined
            dense
          />
          <q-input
            :model-value="signUpFormData.password"
            @update:model-value="(val) => updateSignUpForm('password', val)"
            :type="state.isPwd ? 'password' : 'text'"
            label="Password"
            :rules="passwordRules"
            outlined
            dense
          >
            <template v-slot:append>
              <q-icon
                :name="state.isPwd ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="togglePasswordVisibility"
              />
            </template>
          </q-input>
          <div class="text-center q-mt-md">
            <q-btn
              type="submit"
              label="Create Account"
              :loading="state.loading"
              class="zb-btn-primary full-width"
            />
          </div>
        </q-form>
      </q-card-section>

      <!-- Toggle to Sign In -->
      <q-card-section class="text-center q-pt-none">
        <q-btn
          @click="switchToSignInForm"
          class="zb-btn-flat text-caption toggle-btn"
        >
          Already have an account? Sign In
        </q-btn>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useUnifiedAuth } from '@/services/unifiedAuthService'
import AuthOptions from './AuthOptions.vue'

const {
  state,
  signInFormData,
  signUpFormData,
  closeAllDialogs,
  switchToSignUp,
  switchToSignIn,
  switchToSignUpForm,
  switchToSignInForm,
  handleEmailPasswordSignIn,
  handleEmailPasswordSignUp,
  handleSignIn,
  handleSignUp,
  updateSignInForm,
  updateSignUpForm,
  togglePasswordVisibility,
  emailRules,
  passwordRules
} = useUnifiedAuth()
</script>

<style scoped>
.auth-dialog {
  border-radius: 12px;
}

.auth-dialog .q-card__section {
  padding: 1rem;
}

.auth-dialog .q-btn {
  border-radius: 8px;
}

.text-caption {
  font-size: 0.875rem;
}

/* Center alignment for form buttons */
.q-form .text-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.q-form .full-width {
  max-width: 280px;
  margin: 0 auto;
}

/* Toggle button styling */
.toggle-btn {
  padding: 8px 16px;
  margin: 0 auto;
  display: inline-block;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.toggle-btn:hover {
  text-decoration: none;
  background-color: rgba(25, 118, 210, 0.04);
}

/* Ensure proper spacing */
.q-card-section.text-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Form input styling */
.q-form .q-input {
  margin-bottom: 1rem;
}

/* Main action button styling */
.q-btn[type="submit"] {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
  transition: all 0.3s ease;
}

.q-btn[type="submit"]:hover {
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
  transform: translateY(-1px);
}
</style>
