/**
 * Profile Matching Service
 * 
 * Handles profile-aware matchmaking with compatibility scoring
 */

import { supabase } from '@/lib/supabase'

export interface ProfileMatch {
  profile_id: string
  user_id: string
  profile_type: string
  profile_name: string
  first_name?: string
  last_name?: string
  bio?: string
  industries?: string[]
  skills?: string[]
  location?: string
  avatar_url?: string
  compatibility_score: number
  match_reasons: string[]
  profile_completion: number
}

export interface MatchingCriteria {
  profile_type?: string[]
  industries?: string[]
  skills?: string[]
  location?: string
  min_completion?: number
  exclude_user_id?: string
  limit?: number
}

class ProfileMatchingService {
  /**
   * Find matching profiles based on criteria with compatibility scoring
   */
  async findMatches(
    userProfile: any,
    criteria: MatchingCriteria = {}
  ): Promise<ProfileMatch[]> {
    try {
      console.log('🔍 Finding profile matches:', { userProfile: userProfile?.profile_type, criteria })

      // Build query
      let query = supabase
        .from('personal_details')
        .select(`
          profile_id,
          user_id,
          profile_type,
          profile_name,
          first_name,
          last_name,
          bio,
          industries,
          skills,
          location,
          avatar_url,
          profile_completion,
          profile_visibility
        `)
        .eq('profile_visibility', 'public')
        .gte('profile_completion', criteria.min_completion || 30)

      // Exclude current user
      if (criteria.exclude_user_id) {
        query = query.neq('user_id', criteria.exclude_user_id)
      }

      // Filter by profile types
      if (criteria.profile_type && criteria.profile_type.length > 0) {
        query = query.in('profile_type', criteria.profile_type)
      }

      // Apply limit
      query = query.limit(criteria.limit || 20)

      const { data: profiles, error } = await query

      if (error) {
        console.error('❌ Error fetching profiles:', error)
        throw error
      }

      if (!profiles || profiles.length === 0) {
        console.log('📭 No profiles found matching criteria')
        return []
      }

      // Calculate compatibility scores and match reasons
      const matches = profiles.map(profile => 
        this.calculateCompatibility(userProfile, profile, criteria)
      )

      // Sort by compatibility score (highest first)
      matches.sort((a, b) => b.compatibility_score - a.compatibility_score)

      console.log(`✅ Found ${matches.length} profile matches`)
      return matches

    } catch (error) {
      console.error('💥 Error in profile matching:', error)
      throw error
    }
  }

  /**
   * Calculate compatibility score between two profiles
   */
  private calculateCompatibility(
    userProfile: any,
    candidateProfile: any,
    criteria: MatchingCriteria
  ): ProfileMatch {
    let score = 0
    const matchReasons: string[] = []

    // Base score for profile completion
    const completionScore = (candidateProfile.profile_completion || 0) / 100 * 20
    score += completionScore

    // Industry alignment (30 points max)
    const userIndustries = userProfile?.industries || []
    const candidateIndustries = candidateProfile.industries || []
    
    if (userIndustries.length > 0 && candidateIndustries.length > 0) {
      const commonIndustries = userIndustries.filter(industry => 
        candidateIndustries.includes(industry)
      )
      
      if (commonIndustries.length > 0) {
        const industryScore = (commonIndustries.length / Math.max(userIndustries.length, candidateIndustries.length)) * 30
        score += industryScore
        matchReasons.push(`Shared industries: ${commonIndustries.join(', ')}`)
      }
    }

    // Skills alignment (25 points max)
    const userSkills = userProfile?.skills || []
    const candidateSkills = candidateProfile.skills || []
    
    if (userSkills.length > 0 && candidateSkills.length > 0) {
      const commonSkills = userSkills.filter(skill => 
        candidateSkills.includes(skill)
      )
      
      if (commonSkills.length > 0) {
        const skillScore = (commonSkills.length / Math.max(userSkills.length, candidateSkills.length)) * 25
        score += skillScore
        matchReasons.push(`Complementary skills: ${commonSkills.join(', ')}`)
      }
    }

    // Profile type compatibility (15 points max)
    const userType = userProfile?.profile_type
    const candidateType = candidateProfile.profile_type

    if (userType && candidateType) {
      const typeCompatibility = this.getProfileTypeCompatibility(userType, candidateType)
      score += typeCompatibility.score
      if (typeCompatibility.reason) {
        matchReasons.push(typeCompatibility.reason)
      }
    }

    // Location proximity (10 points max)
    if (userProfile?.location && candidateProfile.location) {
      if (userProfile.location.toLowerCase() === candidateProfile.location.toLowerCase()) {
        score += 10
        matchReasons.push(`Same location: ${candidateProfile.location}`)
      }
    }

    // Ensure score doesn't exceed 100
    score = Math.min(score, 100)

    // Add default reason if no specific matches found
    if (matchReasons.length === 0) {
      matchReasons.push('Active member of the innovation ecosystem')
    }

    return {
      profile_id: candidateProfile.profile_id,
      user_id: candidateProfile.user_id,
      profile_type: candidateProfile.profile_type,
      profile_name: candidateProfile.profile_name,
      first_name: candidateProfile.first_name,
      last_name: candidateProfile.last_name,
      bio: candidateProfile.bio,
      industries: candidateProfile.industries,
      skills: candidateProfile.skills,
      location: candidateProfile.location,
      avatar_url: candidateProfile.avatar_url,
      compatibility_score: Math.round(score),
      match_reasons: matchReasons,
      profile_completion: candidateProfile.profile_completion || 0
    }
  }

  /**
   * Get compatibility score between profile types
   */
  private getProfileTypeCompatibility(userType: string, candidateType: string): { score: number, reason?: string } {
    const compatibilityMatrix: Record<string, Record<string, { score: number, reason: string }>> = {
      investor: {
        innovator: { score: 15, reason: 'Perfect match: Investor seeking innovators' },
        entrepreneur: { score: 15, reason: 'Excellent match: Investor seeking entrepreneurs' },
        startup: { score: 15, reason: 'Great match: Investor seeking startups' },
        mentor: { score: 8, reason: 'Good synergy: Shared expertise in guiding ventures' },
        expert: { score: 10, reason: 'Valuable connection: Industry expertise' },
        corporate: { score: 12, reason: 'Strategic partnership potential' }
      },
      innovator: {
        investor: { score: 15, reason: 'Perfect match: Seeking investment opportunities' },
        mentor: { score: 12, reason: 'Excellent match: Seeking guidance and mentorship' },
        innovator: { score: 10, reason: 'Peer collaboration opportunities' },
        expert: { score: 10, reason: 'Technical expertise and knowledge sharing' },
        corporate: { score: 8, reason: 'Potential corporate partnerships' }
      },
      mentor: {
        innovator: { score: 12, reason: 'Great match: Mentoring innovative minds' },
        entrepreneur: { score: 12, reason: 'Perfect mentoring opportunity' },
        student: { score: 15, reason: 'Excellent mentoring opportunity' },
        startup: { score: 10, reason: 'Startup guidance and mentorship' },
        mentor: { score: 8, reason: 'Peer mentoring and knowledge exchange' }
      },
      expert: {
        innovator: { score: 10, reason: 'Knowledge sharing and technical guidance' },
        entrepreneur: { score: 10, reason: 'Industry expertise and guidance' },
        corporate: { score: 12, reason: 'Professional expertise alignment' },
        expert: { score: 8, reason: 'Peer expertise exchange' }
      },
      corporate: {
        innovator: { score: 8, reason: 'Innovation partnership potential' },
        startup: { score: 10, reason: 'Corporate-startup collaboration' },
        expert: { score: 12, reason: 'Professional expertise alignment' },
        investor: { score: 12, reason: 'Strategic investment partnerships' }
      }
    }

    const compatibility = compatibilityMatrix[userType]?.[candidateType]
    return compatibility || { score: 5, reason: 'General networking opportunity' }
  }

  /**
   * Get recommended profile types for a user to connect with
   */
  getRecommendedProfileTypes(userProfileType: string): string[] {
    const recommendations: Record<string, string[]> = {
      investor: ['innovator', 'entrepreneur', 'startup'],
      innovator: ['investor', 'mentor', 'expert', 'innovator'],
      mentor: ['innovator', 'entrepreneur', 'student', 'startup'],
      expert: ['innovator', 'entrepreneur', 'corporate'],
      corporate: ['innovator', 'startup', 'expert', 'investor'],
      entrepreneur: ['investor', 'mentor', 'expert', 'entrepreneur'],
      startup: ['investor', 'mentor', 'corporate'],
      student: ['mentor', 'expert', 'innovator']
    }

    return recommendations[userProfileType] || ['innovator', 'entrepreneur', 'expert']
  }
}

// Create singleton instance
let _profileMatchingService: ProfileMatchingService | null = null

export const useProfileMatchingService = (): ProfileMatchingService => {
  if (!_profileMatchingService) {
    _profileMatchingService = new ProfileMatchingService()
  }
  return _profileMatchingService
}

// Default export for compatibility
export default useProfileMatchingService()
