import { RouteRecordRaw } from 'vue-router'

// Dashboard routes (require authentication)
const dashboardRoutes: RouteRecordRaw[] = [
  {
    path: '/dashboard',
    component: () => import(/* webpackChunkName: "layout-dashboard" */ '../layouts/DashboardLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'dashboard',
        component: () => import('../views/dashboard/Dashboard.vue')
      },
      {
        path: 'profile',
        name: 'profile-dashboard',
        component: () => import(/* webpackChunkName: "profile" */ '../views/dashboard/Profile.vue')
      },
      {
        path: 'profile/:id/completion',
        name: 'profile-completion',
        redirect: to => {
          // Redirect completion route to edit route with the same ID
          return { name: 'profile-edit', params: { id: to.params.id } }
        }
      },
      {
        path: 'marketplace',
        name: 'marketplace',
        component: () => import(/* webpackChunkName: "marketplace" */ '../views/dashboard/Marketplace.vue')
      },
      {
        path: 'profile/create',
        name: 'profile-create',
        component: () => import(/* webpackChunkName: "profile-creation" */ '../views/dashboard/ProfileCreation.vue')
      },
      {
        path: 'profile/create/legacy',
        name: 'profile-create-legacy',
        redirect: { name: 'profile-create' }
      },
      {
        path: 'profile/:id/edit',
        name: 'profile-edit',
        component: () => import(/* webpackChunkName: "profile-edit" */ '../views/dashboard/ProfileEdit.vue')
          .catch(error => {
            console.error('Failed to load ProfileEdit component:', error);
            // Return a simplified version of the profile edit component
            return import('../views/dashboard/ProfileEditSimple.vue');
          })
      },
      {
        path: 'profile/:id/edit-new',
        name: 'profile-edit-new',
        redirect: to => {
          // Redirect to profile-edit with the same ID
          return { name: 'profile-edit', params: { id: to.params.id } }
        }
      },
      {
        path: 'profile/:id/view',
        name: 'profile-view',
        component: () => import(/* webpackChunkName: "profile" */ '../views/dashboard/ProfileView.vue')
      },
      {
        path: 'events',
        name: 'dashboard-events',
        component: () => import(/* webpackChunkName: "dashboard-features" */ '../views/dashboard/Events.vue')
      },
      {
        path: 'help',
        name: 'dashboard-help',
        component: () => import(/* webpackChunkName: "dashboard-features" */ '../views/dashboard/Help.vue')
      },
      {
        path: 'feedback',
        name: 'dashboard-feedback',
        component: () => import(/* webpackChunkName: "dashboard-features" */ '../views/dashboard/Feedback.vue')
      },
      {
        path: 'profile-questions',
        name: 'profile-questions-preview',
        component: () => import(/* webpackChunkName: "profile-questions" */ '../views/dashboard/ProfileQuestionsPreview.vue')
      },
      {
        path: 'projects',
        name: 'dashboard-projects',
        component: () => import(/* webpackChunkName: "dashboard-features" */ '../views/dashboard/Projects.vue')
      },
      {
        path: 'content',
        name: 'user-content',
        component: () => import(/* webpackChunkName: "user-content" */ '../views/dashboard/UserContentView.vue')
      },
      {
        path: 'activity',
        name: 'user-activity',
        component: () => import(/* webpackChunkName: "user-activity" */ '../views/dashboard/ActivityView.vue')
      },
      {
        path: 'connections',
        name: 'user-connections',
        component: () => import(/* webpackChunkName: "user-connections" */ '../views/dashboard/ConnectionsView.vue')
      },
      {
        path: 'events',
        name: 'user-events',
        component: () => import(/* webpackChunkName: "user-events" */ '../views/dashboard/EventsView.vue')
      },
      {
        path: 'announcements',
        name: 'user-announcements',
        component: () => import(/* webpackChunkName: "user-announcements" */ '../views/dashboard/AnnouncementsView.vue')
      },
      {
        path: 'messages',
        name: 'user-messages',
        component: () => import(/* webpackChunkName: "user-messages" */ '../views/dashboard/MessagingView.vue')
      },
      {
        path: 'notifications',
        name: 'user-notifications',
        component: () => import(/* webpackChunkName: "user-notifications" */ '../views/dashboard/NotificationsView.vue')
      },
      // MATCHMAKING ROUTES - TEMPORARILY DISABLED
      // Uncomment these routes to re-enable matchmaking functionality
      // {
      //   path: 'matchmaking',
      //   name: 'matchmaking',
      //   component: () => import(/* webpackChunkName: "matchmaking" */ '../views/dashboard/Matchmaking.vue')
      // },
      // {
      //   path: 'matchmaking-simulator',
      //   name: 'matchmaking-simulator',
      //   component: () => import(/* webpackChunkName: "matchmaking-simulator" */ '../components/matchmaking/MatchmakingSimulator.vue'),
      //   meta: { requiresAuth: true }
      // },
      // {
      //   path: 'content-matchmaking',
      //   name: 'content-matchmaking',
      //   component: () => import(/* webpackChunkName: "content-matchmaking" */ '../views/dashboard/ContentMatchmaking.vue'),
      //   meta: { requiresAuth: true }
      // },
      {
        path: 'mentorship',
        name: 'mentorship',
        component: () => import('../views/dashboard/MentorshipView.vue'),
        meta: { requiresAuth: true }
      }
    ]
  }
]

export default dashboardRoutes