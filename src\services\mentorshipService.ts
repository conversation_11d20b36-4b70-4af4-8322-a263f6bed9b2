/**
 * Mentorship Service
 * 
 * Core service for managing mentorship requests, sessions, and events
 */

import { supabase } from '../lib/supabase'
import { 
  sendMentorshipRequestNotification,
  sendMentorshipRequestResponseNotification,
  sendMentorshipSessionReminderNotifications
} from './mentorshipNotificationService'

export interface MentorshipRequest {
  id: string
  mentee_id: string
  mentor_id: string
  title: string
  description: string
  goals?: string
  preferred_duration?: string
  preferred_frequency?: string
  preferred_format?: string
  status: 'pending' | 'accepted' | 'declined' | 'cancelled' | 'completed'
  mentor_response?: string
  responded_at?: string
  created_at: string
  updated_at: string
}

export interface MentorshipSession {
  id: string
  mentor_id: string
  mentee_id: string
  title: string
  description?: string
  session_type: 'one-on-one' | 'group' | 'workshop' | 'consultation'
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled' | 'rescheduled'
  scheduled_start_time: string // Alias for scheduled_start
  scheduled_end_time: string   // Alias for scheduled_end
  scheduled_start: string
  scheduled_end: string
  actual_start?: string
  actual_end?: string
  meeting_link?: string
  meeting_location?: string
  meeting_password?: string
  meeting_platform?: string
  agenda?: string
  notes?: string
  mentor_private_notes?: string
  homework_assigned?: string
  next_session_id?: string
  created_at: string
  updated_at: string
}

/**
 * Create a new mentorship request
 */
export async function createMentorshipRequest(requestData: Partial<MentorshipRequest>): Promise<{ data: MentorshipRequest | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('mentorship_requests')
      .insert({
        ...requestData,
        status: 'pending',
        expiry_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating mentorship request:', error)
      return { data: null, error }
    }

    // Send notification to mentor
    if (data) {
      await sendMentorshipRequestNotification({
        mentorId: data.mentor_id,
        menteeId: data.mentee_id,
        requestId: data.id,
        requestTitle: data.title,
        requestMessage: data.message
      })
    }

    return { data, error: null }
  } catch (error) {
    console.error('Error in createMentorshipRequest:', error)
    return { data: null, error }
  }
}

/**
 * Respond to a mentorship request
 */
export async function respondToMentorshipRequest(
  requestId: string, 
  response: 'accepted' | 'declined', 
  mentorResponse?: string
): Promise<{ data: MentorshipRequest | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('mentorship_requests')
      .update({
        status: response,
        mentor_response: mentorResponse,
        response_date: new Date().toISOString()
      })
      .eq('id', requestId)
      .select()
      .single()

    if (error) {
      console.error('Error responding to mentorship request:', error)
      return { data: null, error }
    }

    // Send notification to mentee
    if (data) {
      await sendMentorshipRequestResponseNotification({
        menteeId: data.mentee_id,
        mentorId: data.mentor_id,
        requestId: data.id,
        requestTitle: data.title,
        responseStatus: response,
        mentorResponse
      })
    }

    return { data, error: null }
  } catch (error) {
    console.error('Error in respondToMentorshipRequest:', error)
    return { data: null, error }
  }
}

/**
 * Get mentorship requests for a mentor
 */
export async function getMentorRequests(mentorId: string): Promise<{ data: MentorshipRequest[] | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('mentorship_requests')
      .select('*')
      .eq('mentor_id', mentorId)
      .order('created_at', { ascending: false })

    return { data, error }
  } catch (error) {
    console.error('Error in getMentorRequests:', error)
    return { data: null, error }
  }
}

/**
 * Get mentorship requests for a mentee
 */
export async function getMenteeRequests(menteeId: string): Promise<{ data: MentorshipRequest[] | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('mentorship_requests')
      .select('*')
      .eq('mentee_id', menteeId)
      .order('created_at', { ascending: false })

    return { data, error }
  } catch (error) {
    console.error('Error in getMenteeRequests:', error)
    return { data: null, error }
  }
}

/**
 * Create a new mentorship session
 */
export async function createMentorshipSession(sessionData: Partial<MentorshipSession>): Promise<{ data: MentorshipSession | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('mentorship_sessions')
      .insert({
        ...sessionData,
        status: 'scheduled'
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating mentorship session:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Error in createMentorshipSession:', error)
    return { data: null, error }
  }
}

/**
 * Get upcoming sessions for a user (mentor or mentee)
 */
export async function getUpcomingSessions(userId: string): Promise<{ data: MentorshipSession[] | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('mentorship_sessions')
      .select('*')
      .or(`mentor_id.eq.${userId},mentee_id.eq.${userId}`)
      .gte('scheduled_start_time', new Date().toISOString())
      .eq('status', 'scheduled')
      .order('scheduled_start_time', { ascending: true })

    if (error) {
      console.error('Error fetching mentorship sessions:', error)
      return { data: null, error }
    }

    // Transform the data to include the expected field names
    const transformedData = data?.map(session => ({
      ...session,
      scheduled_start: session.scheduled_start_time,
      scheduled_end: session.scheduled_end_time,
      meeting_platform: session.meeting_platform || 'Online'
    })) || []

    return { data: transformedData, error: null }
  } catch (error) {
    console.error('Error in getUpcomingSessions:', error)
    return { data: null, error }
  }
}

/**
 * Send session reminders for sessions starting in 24 hours
 */
export async function sendSessionReminders(): Promise<void> {
  try {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const tomorrowStart = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate())
    const tomorrowEnd = new Date(tomorrowStart.getTime() + 24 * 60 * 60 * 1000)

    const { data: sessions, error } = await supabase
      .from('mentorship_sessions')
      .select('*')
      .gte('scheduled_start_time', tomorrowStart.toISOString())
      .lt('scheduled_start_time', tomorrowEnd.toISOString())
      .eq('status', 'scheduled')
      .is('reminder_sent_at', null)

    if (error) {
      console.error('Error fetching sessions for reminders:', error)
      return
    }

    if (!sessions || sessions.length === 0) {
      console.log('No sessions found for reminder sending')
      return
    }

    // Send reminders for each session
    for (const session of sessions) {
      try {
        await sendMentorshipSessionReminderNotifications({
          sessionId: session.id,
          mentorId: session.mentor_id,
          menteeId: session.mentee_id,
          sessionTitle: session.title,
          scheduledStartTime: session.scheduled_start_time,
          meetingLink: session.meeting_link,
          meetingPlatform: session.meeting_platform
        })

        // Mark reminder as sent
        await supabase
          .from('mentorship_sessions')
          .update({ reminder_sent_at: new Date().toISOString() })
          .eq('id', session.id)

        console.log(`Reminder sent for session ${session.id}`)
      } catch (error) {
        console.error(`Error sending reminder for session ${session.id}:`, error)
      }
    }
  } catch (error) {
    console.error('Error in sendSessionReminders:', error)
  }
}
