-- Fix mentorship_sessions table schema
-- Ensure the table exists with the correct column names

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.mentorship_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mentorship_request_id UUID,
  mentor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  mentee_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Session details
  title VARCHAR(255) NOT NULL,
  description TEXT,
  session_type VARCHAR(50) DEFAULT 'one-on-one' CHECK (session_type IN ('one-on-one', 'group', 'workshop', 'review')),
  
  -- Scheduling
  scheduled_start TIMESTAMP WITH TIME ZONE NOT NULL,
  scheduled_end TIMESTAMP WITH TIME ZONE NOT NULL,
  actual_start TIMESTAMP WITH TIME ZONE,
  actual_end TIMESTAMP WITH TIME ZONE,
  
  -- Meeting details
  meeting_link TEXT,
  meeting_location TEXT,
  meeting_password VARCHAR(100),
  
  -- Session management
  status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled', 'no_show')),
  cancellation_reason TEXT,
  
  -- Session content
  agenda TEXT,
  notes TEXT,
  mentor_private_notes TEXT,
  homework_assigned TEXT,
  next_session_id UUID REFERENCES public.mentorship_sessions(id),
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_mentorship_sessions_mentor_id ON public.mentorship_sessions(mentor_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_sessions_mentee_id ON public.mentorship_sessions(mentee_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_sessions_scheduled_start ON public.mentorship_sessions(scheduled_start);
CREATE INDEX IF NOT EXISTS idx_mentorship_sessions_status ON public.mentorship_sessions(status);

-- Enable RLS
ALTER TABLE public.mentorship_sessions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
DROP POLICY IF EXISTS "Users can view their own mentorship sessions" ON public.mentorship_sessions;
CREATE POLICY "Users can view their own mentorship sessions" ON public.mentorship_sessions
  FOR SELECT USING (auth.uid() = mentor_id OR auth.uid() = mentee_id);

DROP POLICY IF EXISTS "Users can insert their own mentorship sessions" ON public.mentorship_sessions;
CREATE POLICY "Users can insert their own mentorship sessions" ON public.mentorship_sessions
  FOR INSERT WITH CHECK (auth.uid() = mentor_id OR auth.uid() = mentee_id);

DROP POLICY IF EXISTS "Users can update their own mentorship sessions" ON public.mentorship_sessions;
CREATE POLICY "Users can update their own mentorship sessions" ON public.mentorship_sessions
  FOR UPDATE USING (auth.uid() = mentor_id OR auth.uid() = mentee_id);

DROP POLICY IF EXISTS "Users can delete their own mentorship sessions" ON public.mentorship_sessions;
CREATE POLICY "Users can delete their own mentorship sessions" ON public.mentorship_sessions
  FOR DELETE USING (auth.uid() = mentor_id OR auth.uid() = mentee_id);

-- Create mentorship_requests table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.mentorship_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mentee_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  mentor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Request details
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  goals TEXT,
  preferred_duration VARCHAR(50),
  preferred_frequency VARCHAR(50),
  preferred_format VARCHAR(50),
  
  -- Request management
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'cancelled')),
  response_message TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  responded_at TIMESTAMP WITH TIME ZONE
);

-- Add indexes for mentorship_requests
CREATE INDEX IF NOT EXISTS idx_mentorship_requests_mentor_id ON public.mentorship_requests(mentor_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_requests_mentee_id ON public.mentorship_requests(mentee_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_requests_status ON public.mentorship_requests(status);

-- Enable RLS for mentorship_requests
ALTER TABLE public.mentorship_requests ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for mentorship_requests
DROP POLICY IF EXISTS "Users can view their own mentorship requests" ON public.mentorship_requests;
CREATE POLICY "Users can view their own mentorship requests" ON public.mentorship_requests
  FOR SELECT USING (auth.uid() = mentor_id OR auth.uid() = mentee_id);

DROP POLICY IF EXISTS "Users can insert their own mentorship requests" ON public.mentorship_requests;
CREATE POLICY "Users can insert their own mentorship requests" ON public.mentorship_requests
  FOR INSERT WITH CHECK (auth.uid() = mentee_id);

DROP POLICY IF EXISTS "Users can update their own mentorship requests" ON public.mentorship_requests;
CREATE POLICY "Users can update their own mentorship requests" ON public.mentorship_requests
  FOR UPDATE USING (auth.uid() = mentor_id OR auth.uid() = mentee_id);
