<template>
  <q-card class="ai-features-card">
    <q-card-section>
      <div class="row items-center q-mb-md">
        <q-icon name="psychology" color="primary" size="md" class="q-mr-sm" />
        <div class="text-h6 text-primary">AI Assistant Features</div>
      </div>
      
      <div class="text-body2 text-grey-7 q-mb-md">
        Discover how our AI assistant can help you navigate the innovation ecosystem
      </div>

      <div class="row q-col-gutter-sm">
        <div class="col-12 col-md-6" v-for="feature in features" :key="feature.key">
          <q-item 
            clickable 
            v-ripple 
            class="feature-item rounded-borders"
            @click="triggerFeature(feature.key)"
          >
            <q-item-section avatar>
              <q-icon :name="feature.icon" :color="feature.color" size="sm" />
            </q-item-section>
            <q-item-section>
              <q-item-label class="text-weight-medium">{{ feature.title }}</q-item-label>
              <q-item-label caption>{{ feature.description }}</q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-icon name="chevron_right" color="grey-5" size="sm" />
            </q-item-section>
          </q-item>
        </div>
      </div>

      <q-separator class="q-my-md" />

      <div class="text-center">
        <q-btn
          @click="openAIChat"
          color="primary"
          icon="chat"
          label="Start AI Conversation"
          class="full-width dashboard-action-btn"
          flat
          no-caps
          :loading="isLoading"
        />
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useGlobalServicesStore } from '../../stores/globalServices'

const globalServices = useGlobalServicesStore()
const isLoading = ref(false)

const features = [
  {
    key: 'content_discovery',
    title: 'Content Discovery',
    description: 'Find relevant posts and discussions',
    icon: 'explore',
    color: 'blue'
  },
  {
    key: 'networking',
    title: 'Smart Networking',
    description: 'Connect with like-minded innovators',
    icon: 'people',
    color: 'green'
  },
  {
    key: 'opportunities',
    title: 'Opportunity Matching',
    description: 'Discover relevant opportunities',
    icon: 'lightbulb',
    color: 'orange'
  },
  {
    key: 'profile_optimization',
    title: 'Profile Enhancement',
    description: 'Optimize your profile for better visibility',
    icon: 'person',
    color: 'purple'
  }
]

const triggerFeature = async (featureKey: string) => {
  try {
    isLoading.value = true
    await globalServices.aiChatTriggerService.triggerChat(featureKey, 'dashboard-features')
  } catch (error) {
    console.error('Error triggering AI feature:', error)
  } finally {
    isLoading.value = false
  }
}

const openAIChat = async () => {
  try {
    isLoading.value = true
    await globalServices.aiChatTriggerService.triggerChat('general_assistance', 'dashboard')
  } catch (error) {
    console.error('Error opening AI chat:', error)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.ai-features-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.feature-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

/* Dashboard action button styling for consistency */
.dashboard-action-btn {
  min-height: 44px;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
  box-shadow: none !important;
}

.dashboard-action-btn:hover {
  background-color: rgba(13, 138, 62, 0.08) !important;
}

.dashboard-action-btn:active {
  background-color: rgba(13, 138, 62, 0.12) !important;
}

.feature-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
</style>
