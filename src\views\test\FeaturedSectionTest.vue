<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">Featured Section Test</div>
    
    <div class="q-mb-lg">
      <p>This page tests the FeaturedSection component in isolation.</p>
    </div>

    <!-- Featured Section Test -->
    <div class="test-container">
      <featured-section 
        @item-click="handleFeaturedItemClick"
      />
    </div>

    <!-- Debug Info -->
    <div class="q-mt-lg">
      <q-card class="q-pa-md">
        <div class="text-h6 q-mb-md">Debug Info</div>
        <p><strong>Last clicked item:</strong> {{ lastClickedItem || 'None' }}</p>
        <p><strong>Component loaded:</strong> {{ componentLoaded ? 'Yes' : 'No' }}</p>
      </q-card>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import FeaturedSection from '../../components/feed/FeaturedSection.vue';

// State
const lastClickedItem = ref<string | null>(null);
const componentLoaded = ref(false);

// Methods
function handleFeaturedItemClick(type: string, item: any) {
  console.log('Featured item clicked in test:', type, item);
  lastClickedItem.value = `${type}: ${item.title || item.displayName || item.name}`;
}

// Lifecycle
onMounted(() => {
  componentLoaded.value = true;
  console.log('FeaturedSectionTest mounted');
});
</script>

<style scoped>
.test-container {
  border: 2px dashed #ccc;
  padding: 16px;
  border-radius: 8px;
  background-color: #f9f9f9;
}
</style>
