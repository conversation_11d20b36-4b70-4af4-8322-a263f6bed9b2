/**
 * Mentorship Event Service
 * 
 * Handles all mentorship event operations including creation, management,
 * registration, and notifications.
 */

import { supabase } from '../lib/supabase'
import { useNotificationStore } from '../stores/notifications'

export interface MentorshipEvent {
  id?: string
  mentor_id: string
  title: string
  description: string
  event_type: string
  category?: string
  max_participants: number
  current_participants?: number
  is_public: boolean
  is_free: boolean
  price?: number
  currency?: string
  status: 'draft' | 'published' | 'cancelled' | 'completed'
  scheduled_start_time: string
  scheduled_end_time: string
  timezone?: string
  meeting_link?: string
  meeting_platform?: string
  location?: string
  prerequisites?: string[]
  learning_objectives?: string[]
  materials_provided?: string[]
  target_audience?: string[]
  tags?: string[]
  registration_deadline?: string
  registration_required?: boolean
  auto_approve_registration?: boolean
  event_image_url?: string
  agenda?: any[]
  resources?: any[]
  feedback_collected?: boolean
  average_rating?: number
  total_ratings?: number
  created_at?: string
  updated_at?: string
}

export interface EventRegistration {
  id?: string
  event_id: string
  user_id: string
  registration_status: 'registered' | 'waitlisted' | 'cancelled' | 'attended' | 'no-show'
  registration_notes?: string
  attendance_confirmed?: boolean
  feedback_rating?: number
  feedback_comment?: string
  special_requirements?: string
  payment_status?: string
  payment_reference?: string
  registered_at?: string
  cancelled_at?: string
  attended_at?: string
}

class MentorshipEventService {
  private notifications = useNotificationStore()

  /**
   * Create a new mentorship event
   */
  async createEvent(eventData: Partial<MentorshipEvent>): Promise<{ data: MentorshipEvent | null, error: any }> {
    try {
      console.log('MentorshipEventService: Creating event:', eventData)

      const { data, error } = await supabase
        .from('mentorship_events')
        .insert([{
          ...eventData,
          current_participants: 0,
          feedback_collected: false,
          average_rating: 0,
          total_ratings: 0
        }])
        .select()
        .single()

      if (error) {
        console.error('MentorshipEventService: Error creating event:', error)
        return { data: null, error }
      }

      console.log('MentorshipEventService: Event created successfully:', data)
      
      // Send notification if event is published
      if (data.status === 'published') {
        await this.notifyEventCreated(data)
      }

      return { data, error: null }
    } catch (err: any) {
      console.error('MentorshipEventService: Exception creating event:', err)
      return { data: null, error: err }
    }
  }

  /**
   * Update an existing mentorship event
   */
  async updateEvent(eventId: string, updates: Partial<MentorshipEvent>): Promise<{ data: MentorshipEvent | null, error: any }> {
    try {
      console.log('MentorshipEventService: Updating event:', eventId, updates)

      const { data, error } = await supabase
        .from('mentorship_events')
        .update(updates)
        .eq('id', eventId)
        .select()
        .single()

      if (error) {
        console.error('MentorshipEventService: Error updating event:', error)
        return { data: null, error }
      }

      console.log('MentorshipEventService: Event updated successfully:', data)
      return { data, error: null }
    } catch (err: any) {
      console.error('MentorshipEventService: Exception updating event:', err)
      return { data: null, error: err }
    }
  }

  /**
   * Delete a mentorship event
   */
  async deleteEvent(eventId: string): Promise<{ success: boolean, error: any }> {
    try {
      console.log('MentorshipEventService: Deleting event:', eventId)

      // First, cancel all registrations
      await supabase
        .from('mentorship_event_registrations')
        .update({ registration_status: 'cancelled', cancelled_at: new Date().toISOString() })
        .eq('event_id', eventId)

      // Then delete the event
      const { error } = await supabase
        .from('mentorship_events')
        .delete()
        .eq('id', eventId)

      if (error) {
        console.error('MentorshipEventService: Error deleting event:', error)
        return { success: false, error }
      }

      console.log('MentorshipEventService: Event deleted successfully')
      return { success: true, error: null }
    } catch (err: any) {
      console.error('MentorshipEventService: Exception deleting event:', err)
      return { success: false, error: err }
    }
  }

  /**
   * Get events by mentor
   */
  async getEventsByMentor(mentorId: string): Promise<{ data: MentorshipEvent[], error: any }> {
    try {
      console.log('MentorshipEventService: Getting events for mentor:', mentorId)

      const { data, error } = await supabase
        .from('mentorship_events')
        .select('*')
        .eq('mentor_id', mentorId)
        .order('scheduled_start_time', { ascending: true })

      if (error) {
        console.error('MentorshipEventService: Error getting mentor events:', error)
        return { data: [], error }
      }

      return { data: data || [], error: null }
    } catch (err: any) {
      console.error('MentorshipEventService: Exception getting mentor events:', err)
      return { data: [], error: err }
    }
  }

  /**
   * Get public events
   */
  async getPublicEvents(filters?: {
    eventType?: string
    category?: string
    status?: string
    limit?: number
    offset?: number
  }): Promise<{ data: MentorshipEvent[], error: any }> {
    try {
      console.log('MentorshipEventService: Getting public events with filters:', filters)

      let query = supabase
        .from('mentorship_events')
        .select(`
          *,
          mentor_profiles!inner(profile_name)
        `)
        .eq('is_public', true)
        .eq('status', 'published')
        .gte('scheduled_start_time', new Date().toISOString())

      // Apply filters
      if (filters?.eventType) {
        query = query.eq('event_type', filters.eventType)
      }
      if (filters?.category) {
        query = query.eq('category', filters.category)
      }

      // Apply pagination
      if (filters?.limit) {
        query = query.limit(filters.limit)
      }
      if (filters?.offset) {
        query = query.range(filters.offset, (filters.offset + (filters.limit || 10)) - 1)
      }

      query = query.order('scheduled_start_time', { ascending: true })

      const { data, error } = await query

      if (error) {
        console.error('MentorshipEventService: Error getting public events:', error)
        return { data: [], error }
      }

      // Transform data to include mentor name
      const transformedData = (data || []).map(event => ({
        ...event,
        mentor_name: event.mentor_profiles?.profile_name || 'Unknown Mentor'
      }))

      return { data: transformedData, error: null }
    } catch (err: any) {
      console.error('MentorshipEventService: Exception getting public events:', err)
      return { data: [], error: err }
    }
  }

  /**
   * Register for an event
   */
  async registerForEvent(eventId: string, userId: string, registrationData?: {
    notes?: string
    specialRequirements?: string
  }): Promise<{ data: EventRegistration | null, error: any }> {
    try {
      console.log('MentorshipEventService: Registering for event:', eventId, userId)

      // Check if already registered
      const { data: existingRegistration } = await supabase
        .from('mentorship_event_registrations')
        .select('*')
        .eq('event_id', eventId)
        .eq('user_id', userId)
        .single()

      if (existingRegistration) {
        return { data: null, error: { message: 'Already registered for this event' } }
      }

      // Check event capacity
      const { data: event } = await supabase
        .from('mentorship_events')
        .select('current_participants, max_participants')
        .eq('id', eventId)
        .single()

      if (!event) {
        return { data: null, error: { message: 'Event not found' } }
      }

      const isFull = (event.current_participants || 0) >= (event.max_participants || 0)
      const registrationStatus = isFull ? 'waitlisted' : 'registered'

      // Create registration
      const { data, error } = await supabase
        .from('mentorship_event_registrations')
        .insert([{
          event_id: eventId,
          user_id: userId,
          registration_status: registrationStatus,
          registration_notes: registrationData?.notes,
          special_requirements: registrationData?.specialRequirements,
          attendance_confirmed: false,
          payment_status: 'not-required'
        }])
        .select()
        .single()

      if (error) {
        console.error('MentorshipEventService: Error registering for event:', error)
        return { data: null, error }
      }

      // Update participant count if registered (not waitlisted)
      if (registrationStatus === 'registered') {
        await supabase
          .from('mentorship_events')
          .update({ 
            current_participants: (event.current_participants || 0) + 1 
          })
          .eq('id', eventId)
      }

      console.log('MentorshipEventService: Registration successful:', data)
      
      // Send confirmation notification
      await this.notifyEventRegistration(eventId, userId, registrationStatus)

      return { data, error: null }
    } catch (err: any) {
      console.error('MentorshipEventService: Exception registering for event:', err)
      return { data: null, error: err }
    }
  }

  /**
   * Cancel event registration
   */
  async cancelRegistration(registrationId: string): Promise<{ success: boolean, error: any }> {
    try {
      console.log('MentorshipEventService: Cancelling registration:', registrationId)

      const { data, error } = await supabase
        .from('mentorship_event_registrations')
        .update({ 
          registration_status: 'cancelled',
          cancelled_at: new Date().toISOString()
        })
        .eq('id', registrationId)
        .select('event_id')
        .single()

      if (error) {
        console.error('MentorshipEventService: Error cancelling registration:', error)
        return { success: false, error }
      }

      // Update participant count
      await supabase.rpc('decrement_event_participants', { event_id: data.event_id })

      console.log('MentorshipEventService: Registration cancelled successfully')
      return { success: true, error: null }
    } catch (err: any) {
      console.error('MentorshipEventService: Exception cancelling registration:', err)
      return { success: false, error: err }
    }
  }

  /**
   * Send notification when event is created
   */
  private async notifyEventCreated(event: MentorshipEvent): Promise<void> {
    try {
      // This would integrate with the notification service
      console.log('MentorshipEventService: Sending event creation notifications for:', event.id)
      
      // TODO: Implement notification logic
      // - Notify followers of the mentor
      // - Send to relevant community members based on tags/category
      
    } catch (err) {
      console.error('MentorshipEventService: Error sending event creation notifications:', err)
    }
  }

  /**
   * Send notification when someone registers for an event
   */
  private async notifyEventRegistration(eventId: string, userId: string, status: string): Promise<void> {
    try {
      console.log('MentorshipEventService: Sending registration notifications:', eventId, userId, status)
      
      // TODO: Implement notification logic
      // - Notify mentor of new registration
      // - Send confirmation to registrant
      
    } catch (err) {
      console.error('MentorshipEventService: Error sending registration notifications:', err)
    }
  }
}

export const mentorshipEventService = new MentorshipEventService()
