import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

// Types for embedding generation
interface EmbeddingRequest {
  table_name: string;
  record_id: string;
  embedding_types?: string[]; // ['profile', 'goals', 'challenges'] etc.
  force_regenerate?: boolean;
}

interface EmbeddingResponse {
  success: boolean;
  embeddings_generated: number;
  errors: string[];
  processing_time_ms: number;
}

interface ProfileEmbeddingConfig {
  table_name: string;
  embeddings: {
    [key: string]: {
      column_name: string;
      source_fields: string[];
      separator?: string;
    };
  };
}

// Configuration for each profile type's embeddings
const PROFILE_EMBEDDING_CONFIGS: ProfileEmbeddingConfig[] = [
  {
    table_name: 'innovator_profiles',
    embeddings: {
      profile: {
        column_name: 'profile_embedding',
        source_fields: ['bio', 'innovation_area', 'innovation_description'],
        separator: ' | '
      },
      goals: {
        column_name: 'goals_embedding',
        source_fields: ['short_term_goals', 'long_term_goals', 'looking_for'],
        separator: ' | '
      },
      challenges: {
        column_name: 'challenges_embedding',
        source_fields: ['current_challenges', 'additional_interests'],
        separator: ' | '
      }
    }
  },
  {
    table_name: 'investor_profiles',
    embeddings: {
      profile: {
        column_name: 'profile_embedding',
        source_fields: ['bio', 'investment_philosophy', 'investment_approach'],
        separator: ' | '
      },
      focus: {
        column_name: 'focus_embedding',
        source_fields: ['investment_focus', 'investment_criteria', 'preferred_sectors'],
        separator: ' | '
      },
      portfolio: {
        column_name: 'portfolio_embedding',
        source_fields: ['portfolio_description', 'investment_achievements'],
        separator: ' | '
      }
    }
  },
  {
    table_name: 'mentor_profiles',
    embeddings: {
      profile: {
        column_name: 'profile_embedding',
        source_fields: ['bio', 'mentoring_approach', 'mentorship_philosophy'],
        separator: ' | '
      },
      expertise: {
        column_name: 'expertise_embedding',
        source_fields: ['areas_of_expertise', 'expertise_areas', 'industry_experience'],
        separator: ' | '
      },
      approach: {
        column_name: 'approach_embedding',
        source_fields: ['mentoring_approach', 'mentorship_methods', 'expectations'],
        separator: ' | '
      }
    }
  },
  {
    table_name: 'professional_profiles',
    embeddings: {
      profile: {
        column_name: 'profile_embedding',
        source_fields: ['bio', 'achievements', 'career_goals'],
        separator: ' | '
      },
      skills: {
        column_name: 'skills_embedding',
        source_fields: ['skills', 'certifications', 'projects'],
        separator: ' | '
      },
      experience: {
        column_name: 'experience_embedding',
        source_fields: ['previous_positions', 'education', 'publications'],
        separator: ' | '
      }
    }
  },
  {
    table_name: 'posts',
    embeddings: {
      content: {
        column_name: 'content_embedding',
        source_fields: ['content'],
        separator: ''
      },
      title: {
        column_name: 'title_embedding',
        source_fields: ['title'],
        separator: ''
      },
      tags: {
        column_name: 'tags_embedding',
        source_fields: ['tags'],
        separator: ' '
      }
    }
  }
];

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// OpenAI API configuration (using Supabase AI for embeddings)
const EMBEDDING_MODEL = 'gte-small';
const EMBEDDING_DIMENSIONS = 384;

/**
 * Generate embedding using Supabase AI
 */
async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const { data, error } = await supabase.functions.invoke('embed', {
      body: {
        input: text,
        model: EMBEDDING_MODEL
      }
    });

    if (error) {
      console.error('Embedding generation error:', error);
      throw new Error(`Failed to generate embedding: ${error.message}`);
    }

    return data.embedding;
  } catch (error) {
    console.error('Error calling embedding function:', error);
    throw error;
  }
}

/**
 * Preprocess text for embedding generation
 */
function preprocessText(text: any): string {
  if (!text) return '';
  
  let processedText = '';
  
  if (typeof text === 'string') {
    processedText = text;
  } else if (Array.isArray(text)) {
    processedText = text.join(', ');
  } else if (typeof text === 'object') {
    processedText = JSON.stringify(text);
  } else {
    processedText = String(text);
  }
  
  // Clean up the text
  return processedText
    .replace(/\s+/g, ' ') // Normalize whitespace
    .replace(/[^\w\s.,!?-]/g, '') // Remove special characters except basic punctuation
    .trim();
}

/**
 * Combine multiple fields into a single text for embedding
 */
function combineFields(record: any, fields: string[], separator: string = ' | '): string {
  const values = fields
    .map(field => {
      const value = record[field];
      return preprocessText(value);
    })
    .filter(value => value.length > 0);
  
  return values.join(separator);
}

/**
 * Get profile record from database
 */
async function getProfileRecord(tableName: string, recordId: string): Promise<any> {
  const { data, error } = await supabase
    .from(tableName)
    .select('*')
    .eq('id', recordId)
    .single();

  if (error) {
    throw new Error(`Failed to fetch record from ${tableName}: ${error.message}`);
  }

  return data;
}

/**
 * Update profile record with embeddings
 */
async function updateProfileEmbeddings(
  tableName: string, 
  recordId: string, 
  embeddings: { [key: string]: number[] },
  metadata: any
): Promise<void> {
  const updateData: any = {
    embeddings_generated_at: new Date().toISOString(),
    embedding_model_version: 'gte-small-v1',
    embedding_metadata: metadata
  };

  // Add each embedding to the update data
  Object.entries(embeddings).forEach(([embeddingType, embedding]) => {
    const config = PROFILE_EMBEDDING_CONFIGS.find(c => c.table_name === tableName);
    if (config && config.embeddings[embeddingType]) {
      const columnName = config.embeddings[embeddingType].column_name;
      updateData[columnName] = `[${embedding.join(',')}]`;
    }
  });

  const { error } = await supabase
    .from(tableName)
    .update(updateData)
    .eq('id', recordId);

  if (error) {
    throw new Error(`Failed to update embeddings for ${tableName}: ${error.message}`);
  }
}

/**
 * Log embedding generation status
 */
async function logEmbeddingStatus(
  tableName: string,
  recordId: string,
  embeddingType: string,
  status: 'processing' | 'completed' | 'failed',
  errorMessage?: string
): Promise<void> {
  const logData: any = {
    table_name: tableName,
    record_id: recordId,
    embedding_type: embeddingType,
    status,
    updated_at: new Date().toISOString()
  };

  if (status === 'processing') {
    logData.processing_started_at = new Date().toISOString();
  } else if (status === 'completed') {
    logData.processing_completed_at = new Date().toISOString();
  } else if (status === 'failed') {
    logData.error_message = errorMessage;
  }

  await supabase
    .from('embedding_generation_log')
    .upsert(logData, {
      onConflict: 'table_name,record_id,embedding_type'
    });
}

/**
 * Generate embeddings for a single profile record
 */
async function generateProfileEmbeddings(
  tableName: string,
  recordId: string,
  embeddingTypes?: string[],
  forceRegenerate: boolean = false
): Promise<{ embeddings_generated: number; errors: string[] }> {
  const errors: string[] = [];
  let embeddingsGenerated = 0;

  try {
    // Get the profile configuration
    const config = PROFILE_EMBEDDING_CONFIGS.find(c => c.table_name === tableName);
    if (!config) {
      throw new Error(`No embedding configuration found for table: ${tableName}`);
    }

    // Get the profile record
    const record = await getProfileRecord(tableName, recordId);
    
    // Determine which embeddings to generate
    const targetEmbeddings = embeddingTypes || Object.keys(config.embeddings);
    
    // Check if embeddings need regeneration
    if (!forceRegenerate && record.embeddings_generated_at) {
      const lastUpdated = new Date(record.updated_at || record.created_at);
      const lastGenerated = new Date(record.embeddings_generated_at);
      
      if (lastGenerated > lastUpdated) {
        console.log(`Embeddings are up to date for ${tableName}:${recordId}`);
        return { embeddings_generated: 0, errors: [] };
      }
    }

    const generatedEmbeddings: { [key: string]: number[] } = {};
    const metadata: any = {
      generated_at: new Date().toISOString(),
      source_fields: {},
      text_lengths: {}
    };

    // Generate each embedding
    for (const embeddingType of targetEmbeddings) {
      try {
        await logEmbeddingStatus(tableName, recordId, embeddingType, 'processing');

        const embeddingConfig = config.embeddings[embeddingType];
        if (!embeddingConfig) {
          throw new Error(`No configuration found for embedding type: ${embeddingType}`);
        }

        // Combine source fields
        const combinedText = combineFields(
          record, 
          embeddingConfig.source_fields, 
          embeddingConfig.separator
        );

        if (combinedText.length === 0) {
          console.log(`No content found for ${embeddingType} embedding in ${tableName}:${recordId}`);
          continue;
        }

        // Generate embedding
        const embedding = await generateEmbedding(combinedText);
        
        if (embedding.length !== EMBEDDING_DIMENSIONS) {
          throw new Error(`Expected ${EMBEDDING_DIMENSIONS} dimensions, got ${embedding.length}`);
        }

        generatedEmbeddings[embeddingType] = embedding;
        metadata.source_fields[embeddingType] = embeddingConfig.source_fields;
        metadata.text_lengths[embeddingType] = combinedText.length;

        await logEmbeddingStatus(tableName, recordId, embeddingType, 'completed');
        embeddingsGenerated++;

      } catch (error) {
        const errorMsg = `Failed to generate ${embeddingType} embedding: ${error.message}`;
        errors.push(errorMsg);
        await logEmbeddingStatus(tableName, recordId, embeddingType, 'failed', errorMsg);
      }
    }

    // Update the record with generated embeddings
    if (Object.keys(generatedEmbeddings).length > 0) {
      await updateProfileEmbeddings(tableName, recordId, generatedEmbeddings, metadata);
    }

    return { embeddings_generated: embeddingsGenerated, errors };

  } catch (error) {
    const errorMsg = `Failed to process ${tableName}:${recordId}: ${error.message}`;
    errors.push(errorMsg);
    return { embeddings_generated: 0, errors };
  }
}

/**
 * Process batch embedding generation
 */
async function processBatchEmbeddings(
  tableName: string,
  limit: number = 10
): Promise<{ processed: number; errors: string[] }> {
  const errors: string[] = [];
  let processed = 0;

  try {
    // Get records that need embedding generation
    const { data: records, error } = await supabase
      .from(tableName)
      .select('id, updated_at, embeddings_generated_at')
      .or('embeddings_generated_at.is.null,updated_at.gt.embeddings_generated_at')
      .limit(limit);

    if (error) {
      throw new Error(`Failed to fetch records from ${tableName}: ${error.message}`);
    }

    if (!records || records.length === 0) {
      console.log(`No records need embedding generation in ${tableName}`);
      return { processed: 0, errors: [] };
    }

    console.log(`Processing ${records.length} records from ${tableName}`);

    // Process each record
    for (const record of records) {
      try {
        const result = await generateProfileEmbeddings(tableName, record.id);
        if (result.embeddings_generated > 0) {
          processed++;
        }
        errors.push(...result.errors);
      } catch (error) {
        errors.push(`Failed to process record ${record.id}: ${error.message}`);
      }
    }

    return { processed, errors };

  } catch (error) {
    errors.push(`Batch processing failed for ${tableName}: ${error.message}`);
    return { processed: 0, errors };
  }
}

/**
 * Main Edge Function handler
 */
Deno.serve(async (req: Request) => {
  const startTime = Date.now();

  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      {
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        }
      }
    );
  }

  try {
    const body = await req.json() as EmbeddingRequest;
    const { table_name, record_id, embedding_types, force_regenerate = false } = body;

    console.log(`Processing embedding request for ${table_name}:${record_id || 'batch'}`);

    let totalEmbeddingsGenerated = 0;
    let allErrors: string[] = [];

    if (record_id) {
      // Process single record
      const result = await generateProfileEmbeddings(
        table_name,
        record_id,
        embedding_types,
        force_regenerate
      );
      totalEmbeddingsGenerated = result.embeddings_generated;
      allErrors = result.errors;
    } else {
      // Process batch
      const result = await processBatchEmbeddings(table_name, 10);
      totalEmbeddingsGenerated = result.processed;
      allErrors = result.errors;
    }

    const processingTime = Date.now() - startTime;

    const response: EmbeddingResponse = {
      success: allErrors.length === 0,
      embeddings_generated: totalEmbeddingsGenerated,
      errors: allErrors,
      processing_time_ms: processingTime
    };

    console.log(`Embedding generation completed:`, response);

    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('Edge function error:', error);

    const processingTime = Date.now() - startTime;

    return new Response(
      JSON.stringify({
        success: false,
        embeddings_generated: 0,
        errors: [error.message],
        processing_time_ms: processingTime
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
