<script lang="ts">
export default {
  name: 'TheFooter'
};
</script>

<script setup lang="ts">
import { ref } from 'vue';
import { useLayoutStore } from '../../stores/layout';

const email = ref('<EMAIL>');
const phone = ref('+263 8677 002 005');
const econetTollfree = ref('08080 555');
const teloneTollfree = ref('08004 555');
const netoneTollfree = ref('08010 555');
const year = ref(new Date().getFullYear());

const socialLinks = {
  twitter: 'https://twitter.com/zb_foryou',
  instagram: 'https://www.instagram.com/zb_foryou',
  linkedin: 'https://www.linkedin.com/company/zbforyou',
  youtube: 'https://www.youtube.com/@zb_foryou',
  tiktok: 'https://www.tiktok.com/@zbforyou',
  whatsapp: 'https://wa.me/+263772442685?text=Hello%20Zb'
};

const layoutStore = useLayoutStore();

const scrollToSignup = () => {
  layoutStore.scrollToSignup();
};
</script>

<template>
  <footer class="bg-menu q-py-lg">
    <div class="container q-mx-auto q-px-md">
      <div class="row q-col-gutter-lg">
        <!-- Contact Us -->
        <div class="col-12 col-md-4">
          <h5 class="text-h6 text-primary q-mb-md">Contact Us</h5>
          <div class="contact-list">
            <div class="contact-item q-mb-sm">
              <a :href="'tel:' + phone" class="text-primary">{{ phone }}</a>
            </div>
            <div class="contact-item q-mb-sm">
              <a :href="'mailto:' + email" class="text-primary">{{ email }}</a>
            </div>
          </div>
        </div>

        <!-- Toll Free Numbers -->
        <div class="col-12 col-md-4">
          <h5 class="text-h6 text-primary q-mb-md">Toll Free Numbers</h5>
          <div class="contact-list">
            <div class="contact-item q-mb-sm">
              <span class="text-primary">Econet: {{ econetTollfree }}</span>
            </div>
            <div class="contact-item q-mb-sm">
              <span class="text-primary">Telone: {{ teloneTollfree }}</span>
            </div>
            <div class="contact-item q-mb-sm">
              <span class="text-primary">Netone: {{ netoneTollfree }}</span>
            </div>
          </div>
        </div>

        <!-- Connect With Us -->
        <div class="col-12 col-md-4">
          <h5 class="text-h6 text-primary q-mb-md">Connect With Us</h5>
          <div class="social-links row q-col-gutter-sm">
            <div class="col-auto">
              <a :href="socialLinks.twitter" target="_blank" class="social-link">
                <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z'/%3E%3C/svg%3E" size="24px" />
              </a>
            </div>
            <div class="col-auto">
              <a :href="socialLinks.instagram" target="_blank" class="social-link">
                <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M7.8 2h8.4C19.4 2 22 4.6 22 7.8v8.4a5.8 5.8 0 0 1-5.8 5.8H7.8C4.6 22 2 19.4 2 16.2V7.8A5.8 5.8 0 0 1 7.8 2m-.2 2A3.6 3.6 0 0 0 4 7.6v8.8C4 18.39 5.61 20 7.6 20h8.8a3.6 3.6 0 0 0 3.6-3.6V7.6C20 5.61 18.39 4 16.4 4H7.6m9.65 1.5a1.25 1.25 0 0 1 1.25 1.25A1.25 1.25 0 0 1 17.25 8 1.25 1.25 0 0 1 16 6.75a1.25 1.25 0 0 1 1.25-1.25M12 7a5 5 0 0 1 5 5 5 5 0 0 1-5 5 5 5 0 0 1-5-5 5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3z'/%3E%3C/svg%3E" size="24px" />
              </a>
            </div>
            <div class="col-auto">
              <a :href="socialLinks.linkedin" target="_blank" class="social-link">
                <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z'/%3E%3C/svg%3E" size="24px" />
              </a>
            </div>
            <div class="col-auto">
              <a :href="socialLinks.youtube" target="_blank" class="social-link">
                <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M10 15l5.19-3L10 9v6m11.56-7.83c.13.47.22 1.1.28 1.9.07.8.1 1.49.1 2.09L22 12c0 2.19-.16 3.8-.44 4.83-.25.9-.83 1.48-1.73 1.73-.47.13-1.33.22-2.65.28-1.3.07-2.49.1-3.59.1L12 19c-4.19 0-6.8-.16-7.83-.44-.9-.25-1.48-.83-1.73-1.73-.13-.47-.22-1.1-.28-1.9-.07-.8-.1-1.49-.1-2.09L2 12c0-2.19.16-3.8.44-4.83.25-.9.83-1.48 1.73-1.73.47-.13 1.33-.22 2.65-.28 1.3-.07 2.49-.1 3.59-.1L12 5c4.19 0 6.8.16 7.83.44.9.25 1.48.83 1.73 1.73z'/%3E%3C/svg%3E" size="24px" />
              </a>
            </div>
            <div class="col-auto">
              <a :href="socialLinks.tiktok" target="_blank" class="social-link">
                <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M16.6 5.82s.51.5 0 0A4.278 4.278 0 0 1 15.54 3h-3.09v12.4a2.592 2.592 0 0 1-2.59 2.5c-1.42 0-2.6-1.16-2.6-2.6c0-1.72 1.66-3.01 3.37-2.48V9.66c-3.45-.46-6.47 2.22-6.47 5.64c0 3.33 2.76 5.7 5.69 5.7c3.14 0 5.69-2.55 5.69-5.7V9.01a7.35 7.35 0 0 0 4.3 1.38V7.3s-1.88.09-3.24-1.48z'/%3E%3C/svg%3E" size="24px" />
              </a>
            </div>
            <div class="col-auto">
              <a :href="socialLinks.whatsapp" target="_blank" class="social-link">
                <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M12.04 2c-5.46 0-9.91 4.45-9.91 9.91c0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21c5.46 0 9.91-4.45 9.91-9.91c0-2.65-1.03-5.14-2.9-7.01A9.816 9.816 0 0 0 12.04 2m.01 1.67c2.2 0 4.26.86 5.82 2.42a8.225 8.225 0 0 1 2.41 5.83c0 4.54-3.7 8.23-8.24 8.23c-1.48 0-2.93-.39-4.19-1.15l-.3-.17l-3.12.82l.83-3.04l-.2-.32a8.188 8.188 0 0 1-1.26-4.38c.01-4.54 3.7-8.24 8.25-8.24M8.53 7.33c-.16 0-.43.06-.66.31c-.22.25-.87.86-.87 2.07c0 1.22.89 2.39 1 2.56c.14.17 1.76 2.67 4.25 3.73c.59.27 1.05.42 1.41.53c.59.19 1.13.16 1.56.1c.48-.07 1.46-.6 1.67-1.18c.21-.58.21-1.07.15-1.18c-.07-.1-.23-.16-.48-.27c-.25-.14-1.47-.74-1.69-.82c-.23-.08-.37-.12-.56.12c-.16.25-.64.81-.78.97c-.15.17-.29.19-.53.07c-.26-.13-1.06-.39-2-1.23c-.74-.66-1.23-1.47-1.38-1.72c-.12-.24-.01-.39.11-.5c.11-.11.27-.29.37-.44c.13-.14.17-.25.25-.41c.08-.17.04-.31-.02-.43c-.06-.11-.56-1.35-.77-1.84c-.2-.48-.4-.42-.56-.43c-.14 0-.3-.01-.47-.01z'/%3E%3C/svg%3E" size="24px" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<style scoped>
.bg-menu {
  background-color: #dfefe6;
}

.container {
  max-width: 1400px;
}

.contact-list {
  list-style: none;
  padding: 0;
}

.contact-item {
  margin-bottom: 12px;
}

.contact-item a {
  text-decoration: none;
  transition: color 0.3s ease;
}

.social-links {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(13, 138, 62, 0.1);
  transition: all 0.3s ease;
}

.social-link:hover {
  background: rgba(13, 138, 62, 0.2);
  transform: translateY(-2px);
}

@media (max-width: 767px) {
  .social-links {
    justify-content: center;
  }
}
</style>
