<template>
  <div class="profile-completion">
    <q-card class="profile-completion-card">
      <q-card-section>
        <h2 class="text-h5 q-mb-md">Complete Your Profile</h2>
          <ProfileCompletionIndicator
          :profile-data="profileTypeData"
          :loading="saving"
        />

        <ProfileStepper :active-step="step" class="q-mt-lg">
          <!-- Step 1: Personal Details -->
          <q-step
            :name="1"
            title="Personal Details"
            :done="step > 1"
            :header-nav="step > 1"
          >
            <q-form @submit="savePersonalDetails" class="q-gutter-md">
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <q-input
                    v-model="personalDetails.first_name"
                    label="First Name"
                    :rules="[(val: string) => !!val || 'First name is required']"
              />
            </div>
            <div class="col-12 col-md-6">
              <q-input
                    v-model="personalDetails.last_name"
                    label="Last Name"
                    :rules="[(val: string) => !!val || 'Last name is required']"
              />
            </div>
              </div>

              <q-input
                v-model="personalDetails.email"
                label="Email"
                type="email"
                :rules="[
                  (val: string) => !!val || 'Email is required',
                  (val: string) => /^[^@]+@[^@]+\.[^@]+$/.test(val) || 'Please enter a valid email'
                ]"
              />

              <phone-number-input
                v-model="phoneModel"
                @update:model-value="updatePhone"
              />

                <q-select
                v-model="personalDetails.gender"
                :options="genderOptions"
                label="Gender"
                outlined
                emit-value
                map-options
              />

              <q-input
                v-model="personalDetails.bio"
                label="Bio (Personal Details)"
                type="textarea"
                rows="4"
                hint="Share information about yourself that will appear in your personal details"
              />

              <div class="q-mt-md">
                <q-btn
                  type="submit"
                  color="primary"
                  label="Save & Continue"
                  :loading="saving"
                />
              </div>
            </q-form>
          </q-step>

          <!-- Step 2: Category Selection -->
          <q-step
            :name="2"
            title="Select Category"
            :done="step > 2"
            :header-nav="step > 2"
          >
            <div class="q-gutter-y-md">
              <q-radio
                v-model="selectedProfileType"
                val="innovator"
                label="Innovator"
              />
              <q-radio
                v-model="selectedProfileType"
                val="investor"
                label="Investor"
              />
              <q-radio
                v-model="selectedProfileType"
                val="mentor"
                label="Mentor"
              />
            </div>

            <div class="q-mt-md">
              <q-btn
                color="primary"
                label="Confirm Selection"
                @click="confirmProfileType"
                :loading="saving"
              />
        </div>
      </q-step>

          <!-- Step 3: Category-Specific Questions -->
      <q-step
        :name="3"
            title="Additional Information"
            :done="step > 3"
            :header-nav="step > 3"
          >
            <dynamic-profile-form
              v-if="selectedProfileType"
              :profile-type="selectedProfileType"
              v-model="profileTypeData"
              @save="handleProfileTypeComplete"
            />
      </q-step>
        </ProfileStepper>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useNotificationStore } from '@/stores/notifications'
import { useProfileStore, type BaseProfile } from '@/stores/profile'
import { useRouter, useRoute } from 'vue-router'
import { Quasar } from 'quasar'
import { supabase } from '@/lib/supabase'
import { useGlobalServicesStore } from '@/stores/globalServices'
import { useProfileQuestions } from '@/services/profileQuestions'
import ProfileCompletionIndicator from '../../components/profile/ProfileCompletionIndicator.vue'
import ProfileStepper from '../../components/profile/ProfileStepper.vue'
import PhoneNumberInput from '@/components/ui/PhoneNumberInput.vue'
import DynamicProfileForm from '../../components/profile/DynamicProfileForm.vue'
// ProfileTypeSummary has been removed

const authStore = useAuthStore()
const notifications = useNotificationStore()
const profileStore = useProfileStore()
const router = useRouter()
const route = useRoute()
const $q = Quasar
const globalServices = useGlobalServicesStore()

const { calculateCompletion } = globalServices.profileCompletionService
const profileQuestions = useProfileQuestions()

const step = ref(1)
const saving = ref(false)
const personalInfoForm = ref<any>(null)
const stepper = ref<any>(null)
const selectedProfileType = ref<BaseProfile['profile_type']>(null)

// Profile completion tracking
const completedSteps = ref(0)
const totalSteps = ref(3)
const completionPercentage = computed(() => {
  // Use the centralized profile completion service
  if (profileStore.currentProfile) {
    return calculateCompletion(profileStore.currentProfile, profileTypeData.value)
  }
  // Fallback to local calculation
  return (completedSteps.value / totalSteps.value) * 100
})

// Form data
interface PersonalDetailsForm {
  first_name: string
  last_name: string
  email: string
  phone_country_code: string
  phone_number: string
  gender: string
  bio: string
}

const personalDetails = ref<PersonalDetailsForm>({
  first_name: '',
  last_name: '',
  email: '',
  phone_country_code: '',
  phone_number: '',
  gender: '',
  bio: ''
})

const phoneModel = ref({
  countryCode: '',
  number: ''
})

const profileTypeData = ref<Record<string, any>>({})

// Options for select fields
const genderOptions = [
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' }
]

// Get component for selected profile type
const getProfileTypeComponent = computed(() => {
  return DynamicProfileForm
})

// Load user data on mount
onMounted(async () => {
  if (!authStore.user) {
    router.push('/sign-in')
    return
  }

  // Get profile ID from route params
  const profileId = route.params.id as string
  if (profileId) {
    // Initialize profile store if needed
    if (!profileStore.currentProfile) {
      await profileStore.initialize()
    }

    // Set current profile based on route param
    if (profileStore.currentProfile?.user_id !== profileId) {
      await profileStore.setCurrentProfile(profileId)
    }
  }

  // Load profile data if available
  if (profileStore.currentProfile) {
    personalDetails.value.first_name = profileStore.currentProfile.first_name || ''
    personalDetails.value.last_name = profileStore.currentProfile.last_name || ''
    personalDetails.value.email = profileStore.currentProfile.email || ''
    personalDetails.value.phone_country_code = profileStore.currentProfile.phone_country_code || ''
    personalDetails.value.phone_number = profileStore.currentProfile.phone_number || ''
    personalDetails.value.gender = profileStore.currentProfile.gender || ''
    personalDetails.value.bio = profileStore.currentProfile.bio || ''

    // Set profile type from current profile
    selectedProfileType.value = profileStore.currentProfile.profile_type

    // Load specialized profile data
    if (profileStore.currentProfile.profile_type) {
      await loadProfileTypeData(profileStore.currentProfile.profile_type)
    }

    phoneModel.value = {
      countryCode: profileStore.currentProfile.phone_country_code || '',
      number: profileStore.currentProfile.phone_number || ''
    }

    console.log('ProfileCompletion: Loaded phone model:', phoneModel.value)
  } else {
    // Set email from auth user
    personalDetails.value.email = authStore.user.email || ''
  }
})

// Helper functions
function getCategoryFromProfileType(profileType: string | null): string {
  if (!profileType) return ''

  const mapping: Record<string, string> = {
    'innovator': 'Innovator',
    'investor': 'Business Investor',
    'mentor': 'Mentor',
    'professional': 'Professional',
    'industry_expert': 'Industry Expert',
    'academic_student': 'Academic Student',
    'academic_institution': 'Academic Institution',
    'organisation': 'Organisation'
  }

  return mapping[profileType] || ''
}

function getProfileTypeKey(): string {
  const mapping: Record<string, string> = {
    'Innovator': 'innovator',
    'Business Investor': 'investor',
    'Mentor': 'mentor',
    'Professional': 'professional',
    'Industry Expert': 'industry_expert',
    'Academic Student': 'academic_student',
    'Academic Institution': 'academic_institution',
    'Organisation': 'organisation'
  }

  return mapping[personalDetails.value.category] || ''
}

function getProfileTypeTitle(): string {
  return personalDetails.value.category || 'Profile'
}

// Load profile type specific data
async function loadProfileTypeData(profileType: string) {
  try {
    const tableName = `${profileType}_profiles`
    console.log(`Loading profile type data from ${tableName} for user ${profileStore.currentProfile?.user_id}`)
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .eq('user_id', profileStore.currentProfile?.user_id)
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error(`Error loading ${profileType} profile data:`, error)
      return
    }

    if (data) {
      profileTypeData.value = { ...data }
      delete profileTypeData.value.id
      delete profileTypeData.value.profile_id
      delete profileTypeData.value.created_at
      delete profileTypeData.value.updated_at
    }
  } catch (err) {
    console.error('Error loading profile type data:', err)
  }
}

// Save profile type specific data
async function saveProfileTypeSpecificData(profileType: string, profileTypeId: string) {
  try {
    const tableName = `${profileType}_profiles`
    console.log(`Saving profile type data to ${tableName} for user ${profileStore.currentProfile?.user_id}`)

    // Check if record exists
    const { data: existingData, error: checkError } = await supabase
      .from(tableName)
      .select('id')
      .eq('user_id', profileStore.currentProfile?.user_id)
      .maybeSingle()

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError
    }

    const profileData = {
      ...profileTypeData.value,
      user_id: profileStore.currentProfile?.user_id,
      profile_type_id: profileTypeId
    }

    if (existingData?.id) {
      // Update existing record
      const { error: updateError } = await supabase
        .from(tableName)
        .update(profileData)
        .eq('id', existingData.id)

      if (updateError) throw updateError
    } else {
      // Insert new record
      const { error: insertError } = await supabase
        .from(tableName)
        .insert(profileData)

      if (insertError) throw insertError
    }
  } catch (err: any) {
    console.error(`Error saving ${profileType} profile data:`, err)
    throw new Error(`Failed to save ${profileType} profile data: ${err.message}`)
  }
}

// Watch for changes in profile type data to update completion steps
watch(profileTypeData, () => {
  updateCompletionSteps()
}, { deep: true })

// Update completion steps based on form data
function updateCompletionSteps() {
  let completed = 0

  // Check if personal info is complete
  if (
    personalDetails.value.first_name &&
    personalDetails.value.last_name &&
    personalDetails.value.email &&
    personalDetails.value.phone_country_code &&
    personalDetails.value.phone_number &&
    personalDetails.value.gender &&
    personalDetails.value.bio.length > 0
  ) {
    completed++
  }

  // Check if profile type data is complete
  if (Object.keys(profileTypeData.value).length > 0) {
    // This is a simplified check - each profile type form should implement its own validation
    completed++
  }

  // Set completed steps
  completedSteps.value = completed
}

// Save personal info and move to next step
async function savePersonalDetails() {
  try {
    const success = await profileStore.updateProfile({
      ...personalDetails.value
    })

    if (success) {
      notifications.success('Personal details saved successfully')
      step.value = 2
    }
  } catch (error) {
    notifications.error('Failed to save personal details')
  }
}

// Confirm profile type selection
async function confirmProfileType() {
  if (!selectedProfileType.value) {
    notifications.error('Please select a profile type')
    return
  }

  try {
    saving.value = true
    await profileStore.updateProfile({
      profile_type: selectedProfileType.value
    })

    step.value = 3
    notifications.success('Profile type saved successfully')
  } catch (error: any) {
    notifications.error('Failed to save profile type: ' + error.message)
  } finally {
    saving.value = false
  }
}

// Handle profile type form completion
async function handleProfileTypeComplete(data: any) {
  try {
    saving.value = true
    await profileStore.updateProfileData(data)
    await router.push('/dashboard')
    notifications.success('Profile completed successfully!')
  } catch (error: any) {
    notifications.error('Failed to complete profile: ' + error.message)
  } finally {
    saving.value = false
  }
}

function updatePhone(value: { countryCode: string, number: string }) {
  personalDetails.value.phone_country_code = value.countryCode || ''
  personalDetails.value.phone_number = value.number || ''

  // Update the phone model to ensure it's always in sync
  phoneModel.value = {
    countryCode: value.countryCode || '',
    number: value.number || ''
  }

  console.log('ProfileCompletion: Phone updated', phoneModel.value)
}
</script>
