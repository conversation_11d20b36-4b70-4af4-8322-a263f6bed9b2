<template>
  <q-card
    class="featured-event-card cursor-pointer"
    :class="{ 'featured-event-card--hover': !loading }"
    @click="handleClick"
  >
    <!-- Timeline Date Section -->
    <div class="event-timeline-date">
      <div class="date-circle" :class="{ 'upcoming': isUpcoming, 'past': !isUpcoming }">
        <div class="date-month">{{ eventMonth }}</div>
        <div class="date-day">{{ eventDay }}</div>
      </div>
      <div class="timeline-connector"></div>
    </div>

    <!-- Event Content -->
    <div class="event-content">
      <!-- Header with Image -->
      <div class="event-header">
        <q-img
          :src="eventImage"
          :ratio="3/2"
          class="event-image"
          loading="lazy"
          @error="handleImageError"
        >
          <template v-slot:error>
            <div class="absolute-full flex flex-center bg-gradient-to-br from-orange-4 to-orange-6">
              <q-icon name="event" size="2em" color="white" />
            </div>
          </template>

          <!-- Event Type Badge -->
          <div class="absolute-top-left q-ma-sm">
            <q-chip
              color="green"
              text-color="white"
              icon="event"
              size="sm"
              class="type-badge"
            >
              {{ eventType }}
            </q-chip>
          </div>

          <!-- Removed time badge as requested -->
        </q-img>
      </div>

      <!-- Event Details -->
      <div class="event-details q-pa-md">
        <!-- Title -->
        <h4 class="event-title q-mb-xs">
          {{ eventTitle }}
        </h4>

        <!-- Description -->
        <p class="event-description text-grey-7 q-mb-sm">
          {{ eventDescription }}
        </p>

        <!-- Meta Information -->
        <div class="event-meta">
          <div class="meta-item">
            <q-icon name="location_on" size="sm" color="orange" class="q-mr-xs" />
            <span class="text-caption">{{ eventLocation }}</span>
          </div>
          <div class="meta-item">
            <q-icon name="people" size="sm" color="orange" class="q-mr-xs" />
            <span class="text-caption">{{ attendeeCount }} attending</span>
          </div>
        </div>

        <!-- Action Button -->
        <q-btn
          color="green"
          outline
          size="sm"
          :label="isUpcoming ? 'Register' : 'View Details'"
          class="full-width q-mt-sm"
          @click.stop="handleClick"
        />
      </div>
    </div>

    <!-- Hover Effect Overlay -->
    <div class="hover-overlay absolute-full flex flex-center">
      <q-btn
        round
        color="green"
        text-color="white"
        icon="arrow_forward"
        size="md"
        class="hover-button"
      />
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

// Props
interface Props {
  event: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  click: [event: any];
}>();

// State
const loading = ref(false);
const imageError = ref(false);

// Computed
const eventImage = computed(() => {
  if (imageError.value) {
    return generateFallbackImage();
  }

  return props.event.featured_image || 
         props.event.media_urls?.banner || 
         props.event.image || 
         generateFallbackImage();
});

const eventTitle = computed(() => {
  return props.event.event_title || 
         props.event.title || 
         'Featured Event';
});

const eventDescription = computed(() => {
  return props.event.content?.substring(0, 120) + '...' || 
         props.event.description?.substring(0, 120) + '...' || 
         'Join this exciting event';
});

const eventType = computed(() => {
  return props.event.event_type || 
         props.event.type || 
         'Event';
});

const eventLocation = computed(() => {
  return props.event.event_location || 
         props.event.location || 
         'TBA';
});

const eventDate = computed(() => {
  // Try multiple date field variations to get the actual event date
  const dateValue = props.event.event_start_datetime ||
                   props.event.eventStartDatetime ||
                   props.event.event_date ||
                   props.event.date ||
                   props.event.created_at;

  if (!dateValue) {
    console.warn('No date found for event:', props.event.id, props.event);
    return new Date(); // Only fallback to current date if no date fields exist
  }

  return new Date(dateValue);
});

const isUpcoming = computed(() => {
  return eventDate.value > new Date();
});

const eventMonth = computed(() => {
  return eventDate.value.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
});

const eventDay = computed(() => {
  return eventDate.value.getDate().toString();
});

const eventTime = computed(() => {
  return eventDate.value.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
});

const eventDateLabel = computed(() => {
  const date = eventDate.value;
  const now = new Date();
  const diffTime = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 0) {
    return 'Past';
  } else if (diffDays === 0) {
    return 'Today';
  } else if (diffDays === 1) {
    return 'Tomorrow';
  } else if (diffDays <= 7) {
    return `${diffDays} days`;
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  }
});

const attendeeCount = computed(() => {
  return props.event.current_participants ||
         props.event.attendees?.length ||
         0; // No mock data - show actual count or 0
});

// Methods
function generateFallbackImage(): string {
  const eventId = props.event.id || 'default';
  const hash = Math.abs(hashCode(eventId));
  const color = ['FF6B35', 'F7931E', 'FFD23F', 'EE4B2B'][hash % 4];
  return `https://via.placeholder.com/400x250/${color}/FFFFFF?text=${encodeURIComponent('Event')}`;
}

function hashCode(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash;
}

function handleImageError() {
  imageError.value = true;
}

function handleClick() {
  emit('click', props.event);
}
</script>

<style scoped>
.featured-event-card {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1);
  height: 380px;
  display: flex;
  background: white;
  border: 1px solid rgba(76, 175, 80, 0.1);
}

.featured-event-card--hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(76, 175, 80, 0.2);
}

/* Timeline Date Section */
.event-timeline-date {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  background: rgba(76, 175, 80, 0.9);
  backdrop-filter: blur(8px);
  min-width: 80px;
  border-radius: 12px 0 0 12px; /* Square top-right and bottom-right corners */
}

.date-circle {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 2;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.date-circle.upcoming {
  border: 2px solid #4CAF50;
  background: rgba(255, 255, 255, 0.98);
}

.date-circle.past {
  border: 2px solid #9E9E9E;
  background: rgba(255, 255, 255, 0.85);
  opacity: 0.8;
}

.date-month {
  font-size: 0.7rem;
  font-weight: 600;
  color: #4CAF50;
  line-height: 1;
}

.date-day {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1;
}

.timeline-connector {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 20px;
  background: rgba(255, 255, 255, 0.5);
}

/* Event Content */
.event-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.event-header {
  position: relative;
  height: 140px;
}

.event-image {
  height: 100%;
  border-radius: 0;
}

.type-badge {
  backdrop-filter: blur(8px);
  background: rgba(76, 175, 80, 0.9) !important;
}

.time-badge {
  backdrop-filter: blur(8px);
}

.event-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.event-title {
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
  color: #1a1a1a;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-description {
  font-size: 0.875rem;
  line-height: 1.3;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.event-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: #666;
}

.meta-item .q-icon {
  color: #4CAF50;
}

.hover-overlay {
  background: rgba(76, 175, 80, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(2px);
}

.featured-event-card--hover:hover .hover-overlay {
  opacity: 1;
}

.hover-button {
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.featured-event-card--hover:hover .hover-button {
  transform: scale(1);
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .featured-event-card {
    height: 340px;
  }

  .event-timeline-date {
    min-width: 70px;
    padding: 12px 8px;
  }

  .date-circle {
    width: 48px;
    height: 48px;
  }

  .date-month {
    font-size: 0.65rem;
  }

  .date-day {
    font-size: 1rem;
  }

  .event-header {
    height: 120px;
  }

  .event-title {
    font-size: 1rem;
  }

  .event-description {
    font-size: 0.8rem;
  }
}
</style>
