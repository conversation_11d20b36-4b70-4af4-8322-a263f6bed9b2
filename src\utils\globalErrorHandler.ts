/**
 * Global Error Handler
 * 
 * Comprehensive error handling system that integrates with service coordination
 * to provide intelligent error recovery and user-friendly error reporting.
 */

import { App } from 'vue';
import { useGlobalServicesStore } from '../stores/globalServices';
import { useNotificationStore } from '../stores/notifications';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  timestamp: Date;
  userAgent: string;
  url: string;
  serviceHealth?: any;
}

export interface ErrorReport {
  id: string;
  type: 'service' | 'component' | 'network' | 'validation' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  stack?: string;
  context: ErrorContext;
  recovered: boolean;
  recoveryAttempts: number;
}

export class GlobalErrorHandler {
  private errorReports: ErrorReport[] = [];
  private maxReports = 100;
  private recoveryAttempts = new Map<string, number>();

  constructor() {
    this.setupErrorListeners();
  }

  /**
   * Set up global error listeners
   */
  private setupErrorListeners(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason, {
        component: 'Promise',
        action: 'unhandled_rejection'
      });
    });

    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError(event.error || new Error(event.message), {
        component: 'JavaScript',
        action: 'runtime_error'
      });
    });

    // Handle resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target && event.target !== window) {
        this.handleError(new Error(`Resource failed to load: ${(event.target as any).src || (event.target as any).href}`), {
          component: 'Resource',
          action: 'load_error'
        });
      }
    }, true);
  }

  /**
   * Handle an error with intelligent recovery
   */
  async handleError(error: any, context: Partial<ErrorContext> = {}): Promise<void> {
    const errorId = this.generateErrorId();
    const errorType = this.classifyError(error);
    const severity = this.determineSeverity(error, errorType);

    const fullContext: ErrorContext = {
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...context
    };

    // Get service health if available
    try {
      const globalServices = useGlobalServicesStore();
      fullContext.serviceHealth = globalServices.serviceHealth;
    } catch (e) {
      // Service store might not be available yet
    }

    const errorReport: ErrorReport = {
      id: errorId,
      type: errorType,
      severity,
      message: error.message || String(error),
      stack: error.stack,
      context: fullContext,
      recovered: false,
      recoveryAttempts: 0
    };

    // Store the error report
    this.addErrorReport(errorReport);

    // Log the error
    this.logError(errorReport);

    // Attempt recovery
    const recovered = await this.attemptRecovery(errorReport);
    errorReport.recovered = recovered;

    // Show user notification if necessary
    this.showUserNotification(errorReport);

    // Report to external services in production
    if (import.meta.env.MODE === 'production') {
      this.reportToExternalService(errorReport);
    }
  }

  /**
   * Classify error type
   */
  private classifyError(error: any): ErrorReport['type'] {
    const message = error.message || String(error);

    if (message.includes('service') || message.includes('Service')) {
      return 'service';
    }
    if (message.includes('network') || message.includes('fetch') || message.includes('NetworkError')) {
      return 'network';
    }
    if (message.includes('validation') || message.includes('required') || message.includes('invalid')) {
      return 'validation';
    }
    if (error.name === 'ChunkLoadError' || message.includes('Loading chunk')) {
      return 'network';
    }
    if (error.componentName || message.includes('component')) {
      return 'component';
    }

    return 'unknown';
  }

  /**
   * Determine error severity
   */
  private determineSeverity(error: any, type: ErrorReport['type']): ErrorReport['severity'] {
    const message = error.message || String(error);

    // Critical errors
    if (type === 'service' && message.includes('initialization')) {
      return 'critical';
    }
    if (message.includes('authentication') || message.includes('unauthorized')) {
      return 'critical';
    }

    // High severity errors
    if (type === 'service') {
      return 'high';
    }
    if (message.includes('database') || message.includes('storage')) {
      return 'high';
    }

    // Medium severity errors
    if (type === 'network') {
      return 'medium';
    }
    if (type === 'component') {
      return 'medium';
    }

    // Low severity errors
    if (type === 'validation') {
      return 'low';
    }

    return 'medium';
  }

  /**
   * Attempt intelligent error recovery
   */
  private async attemptRecovery(errorReport: ErrorReport): Promise<boolean> {
    const { type, message, id } = errorReport;
    const currentAttempts = this.recoveryAttempts.get(id) || 0;

    if (currentAttempts >= 3) {
      console.warn('Max recovery attempts reached for error:', id);
      return false;
    }

    this.recoveryAttempts.set(id, currentAttempts + 1);
    errorReport.recoveryAttempts = currentAttempts + 1;

    try {
      switch (type) {
        case 'service':
          return await this.recoverServiceError(message);
        
        case 'network':
          return await this.recoverNetworkError(message);
        
        case 'component':
          return await this.recoverComponentError(message);
        
        default:
          return false;
      }
    } catch (recoveryError) {
      console.error('Recovery attempt failed:', recoveryError);
      return false;
    }
  }

  /**
   * Recover from service errors
   */
  private async recoverServiceError(message: string): Promise<boolean> {
    try {
      const globalServices = useGlobalServicesStore();
      
      if (message.includes('initialization')) {
        console.log('Attempting service reinitialization...');
        await globalServices.initializeAllServices();
        return true;
      }
      
      if (message.includes('cache') || message.includes('Cache')) {
        console.log('Attempting cache service recovery...');
        await globalServices.initializeCacheService();
        return true;
      }
      
      if (message.includes('realtime') || message.includes('Realtime')) {
        console.log('Attempting realtime service recovery...');
        await globalServices.initializeRealtimeService();
        return true;
      }
      
      // General service recovery
      console.log('Attempting general service recovery...');
      await globalServices.recoverFailedServices();
      return true;
    } catch (error) {
      console.error('Service recovery failed:', error);
      return false;
    }
  }

  /**
   * Recover from network errors
   */
  private async recoverNetworkError(message: string): Promise<boolean> {
    // For chunk loading errors, reload the page
    if (message.includes('Loading chunk') || message.includes('ChunkLoadError')) {
      console.log('Chunk loading error detected, reloading page...');
      setTimeout(() => window.location.reload(), 1000);
      return true;
    }

    // For network connectivity issues, wait and retry
    if (message.includes('NetworkError') || message.includes('fetch')) {
      console.log('Network error detected, waiting before retry...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      return true;
    }

    return false;
  }

  /**
   * Recover from component errors
   */
  private async recoverComponentError(message: string): Promise<boolean> {
    // Component errors usually require user action or page refresh
    console.log('Component error detected, recovery limited');
    return false;
  }

  /**
   * Show user-friendly notification
   */
  private showUserNotification(errorReport: ErrorReport): void {
    try {
      const notifications = useNotificationStore();
      
      switch (errorReport.severity) {
        case 'critical':
          notifications.error(
            errorReport.recovered 
              ? 'A critical issue was detected and resolved automatically.'
              : 'A critical issue occurred. Please refresh the page if problems persist.'
          );
          break;
        
        case 'high':
          if (!errorReport.recovered) {
            notifications.warning(
              'Some features may be temporarily unavailable. We\'re working to resolve this.'
            );
          }
          break;
        
        case 'medium':
          if (!errorReport.recovered && errorReport.type === 'network') {
            notifications.info('Connection issue detected. Please check your internet connection.');
          }
          break;
        
        // Don't show notifications for low severity errors
        case 'low':
        default:
          break;
      }
    } catch (error) {
      // Notification store might not be available
      console.warn('Could not show user notification:', error);
    }
  }

  /**
   * Log error with appropriate level
   */
  private logError(errorReport: ErrorReport): void {
    const logMessage = `[${errorReport.severity.toUpperCase()}] ${errorReport.type} error: ${errorReport.message}`;
    
    switch (errorReport.severity) {
      case 'critical':
      case 'high':
        console.error(logMessage, errorReport);
        break;
      case 'medium':
        console.warn(logMessage, errorReport);
        break;
      case 'low':
        console.info(logMessage, errorReport);
        break;
    }
  }

  /**
   * Report to external error tracking service
   */
  private reportToExternalService(errorReport: ErrorReport): void {
    // In a real application, you would send this to services like:
    // - Sentry
    // - LogRocket
    // - Bugsnag
    // - Custom error tracking endpoint
    
    console.log('Would report to external service:', errorReport);
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Add error report to storage
   */
  private addErrorReport(errorReport: ErrorReport): void {
    this.errorReports.unshift(errorReport);
    
    // Keep only the most recent reports
    if (this.errorReports.length > this.maxReports) {
      this.errorReports = this.errorReports.slice(0, this.maxReports);
    }
  }

  /**
   * Get recent error reports
   */
  getRecentErrors(limit: number = 10): ErrorReport[] {
    return this.errorReports.slice(0, limit);
  }

  /**
   * Get error statistics
   */
  getErrorStatistics(): {
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    recoveryRate: number;
  } {
    const total = this.errorReports.length;
    const byType: Record<string, number> = {};
    const bySeverity: Record<string, number> = {};
    let recovered = 0;

    this.errorReports.forEach(report => {
      byType[report.type] = (byType[report.type] || 0) + 1;
      bySeverity[report.severity] = (bySeverity[report.severity] || 0) + 1;
      if (report.recovered) recovered++;
    });

    return {
      total,
      byType,
      bySeverity,
      recoveryRate: total > 0 ? recovered / total : 0
    };
  }
}

/**
 * Install global error handler as Vue plugin
 */
export function createGlobalErrorHandler() {
  const errorHandler = new GlobalErrorHandler();

  return {
    install(app: App) {
      // Set Vue's global error handler
      app.config.errorHandler = (error: any, instance: any, info: string) => {
        errorHandler.handleError(error, {
          component: instance?.$options.name || 'Unknown',
          action: info
        });
      };

      // Provide error handler instance
      app.provide('errorHandler', errorHandler);
      
      // Make available globally for debugging
      if (import.meta.env.MODE === 'development') {
        (window as any).__ERROR_HANDLER__ = errorHandler;
      }
    },
    errorHandler
  };
}
