/**
 * Mentorship Qualification Service
 * 
 * Evaluates whether a user qualifies to be a mentor based on specific criteria
 */

import { supabase } from '../lib/supabase'

export interface MentorQualificationCriteria {
  minYearsExperience: number
  requiredExpertiseAreas: number
  minEducationLevel?: string
  requiresCurrentRole: boolean
  requiresCompany: boolean
  requiresPreviousMentoring?: boolean
  minProfileCompletion: number
}

export interface QualificationResult {
  qualified: boolean
  score: number
  maxScore: number
  criteria: {
    experience: { passed: boolean; score: number; details: string }
    expertise: { passed: boolean; score: number; details: string }
    education: { passed: boolean; score: number; details: string }
    professional: { passed: boolean; score: number; details: string }
    completion: { passed: boolean; score: number; details: string }
  }
  recommendations: string[]
  missingRequirements: string[]
}

// Default qualification criteria
const DEFAULT_CRITERIA: MentorQualificationCriteria = {
  minYearsExperience: 3,
  requiredExpertiseAreas: 2,
  requiresCurrentRole: true,
  requiresCompany: false,
  requiresPreviousMentoring: false,
  minProfileCompletion: 70
}

/**
 * Evaluate if a user qualifies to be a mentor
 */
export async function evaluateMentorQualification(
  userId: string,
  criteria: Partial<MentorQualificationCriteria> = {}
): Promise<QualificationResult> {
  const finalCriteria = { ...DEFAULT_CRITERIA, ...criteria }
  
  try {
    // Get user's personal details and mentor profile
    const [personalDetails, mentorProfile] = await Promise.all([
      getUserPersonalDetails(userId),
      getMentorProfile(userId)
    ])

    if (!personalDetails) {
      throw new Error('User personal details not found')
    }

    // Initialize result
    const result: QualificationResult = {
      qualified: false,
      score: 0,
      maxScore: 100,
      criteria: {
        experience: { passed: false, score: 0, details: '' },
        expertise: { passed: false, score: 0, details: '' },
        education: { passed: false, score: 0, details: '' },
        professional: { passed: false, score: 0, details: '' },
        completion: { passed: false, score: 0, details: '' }
      },
      recommendations: [],
      missingRequirements: []
    }

    // Evaluate each criterion
    evaluateExperience(mentorProfile, finalCriteria, result)
    evaluateExpertise(mentorProfile, finalCriteria, result)
    evaluateEducation(mentorProfile, finalCriteria, result)
    evaluateProfessionalBackground(mentorProfile, finalCriteria, result)
    evaluateProfileCompletion(personalDetails, mentorProfile, finalCriteria, result)

    // Calculate total score and qualification status
    result.score = Object.values(result.criteria).reduce((sum, criterion) => sum + criterion.score, 0)
    result.qualified = result.score >= 70 && result.criteria.completion.passed

    // Generate recommendations
    generateRecommendations(result, finalCriteria)

    return result
  } catch (error) {
    console.error('Error evaluating mentor qualification:', error)
    throw error
  }
}

/**
 * Get user's personal details
 */
async function getUserPersonalDetails(userId: string) {
  const { data, error } = await supabase
    .from('personal_details')
    .select('*')
    .eq('user_id', userId)
    .single()

  if (error && error.code !== 'PGRST116') {
    throw error
  }

  return data
}

/**
 * Get user's mentor profile
 */
async function getMentorProfile(userId: string) {
  const { data, error } = await supabase
    .from('mentor_profiles')
    .select('*')
    .eq('user_id', userId)
    .single()

  if (error && error.code !== 'PGRST116') {
    throw error
  }

  return data
}

/**
 * Evaluate years of experience
 */
function evaluateExperience(
  mentorProfile: any,
  criteria: MentorQualificationCriteria,
  result: QualificationResult
) {
  const yearsExperience = mentorProfile?.years_of_experience || 0
  const maxScore = 25

  if (yearsExperience >= criteria.minYearsExperience) {
    result.criteria.experience.passed = true
    result.criteria.experience.score = Math.min(maxScore, (yearsExperience / criteria.minYearsExperience) * maxScore)
    result.criteria.experience.details = `${yearsExperience} years of experience (required: ${criteria.minYearsExperience})`
  } else {
    result.criteria.experience.passed = false
    result.criteria.experience.score = (yearsExperience / criteria.minYearsExperience) * maxScore
    result.criteria.experience.details = `${yearsExperience} years of experience (required: ${criteria.minYearsExperience})`
    result.missingRequirements.push(`At least ${criteria.minYearsExperience} years of experience`)
  }
}

/**
 * Evaluate areas of expertise
 */
function evaluateExpertise(
  mentorProfile: any,
  criteria: MentorQualificationCriteria,
  result: QualificationResult
) {
  const expertiseAreas = mentorProfile?.expertise_areas || []
  const expertiseCount = Array.isArray(expertiseAreas) ? expertiseAreas.length : 0
  const maxScore = 25

  if (expertiseCount >= criteria.requiredExpertiseAreas) {
    result.criteria.expertise.passed = true
    result.criteria.expertise.score = Math.min(maxScore, (expertiseCount / criteria.requiredExpertiseAreas) * maxScore)
    result.criteria.expertise.details = `${expertiseCount} areas of expertise (required: ${criteria.requiredExpertiseAreas})`
  } else {
    result.criteria.expertise.passed = false
    result.criteria.expertise.score = (expertiseCount / criteria.requiredExpertiseAreas) * maxScore
    result.criteria.expertise.details = `${expertiseCount} areas of expertise (required: ${criteria.requiredExpertiseAreas})`
    result.missingRequirements.push(`At least ${criteria.requiredExpertiseAreas} areas of expertise`)
  }
}

/**
 * Evaluate education level
 */
function evaluateEducation(
  mentorProfile: any,
  criteria: MentorQualificationCriteria,
  result: QualificationResult
) {
  const education = mentorProfile?.education || []
  const hasEducation = Array.isArray(education) ? education.length > 0 : false
  const maxScore = 15

  if (hasEducation) {
    result.criteria.education.passed = true
    result.criteria.education.score = maxScore
    result.criteria.education.details = `Education provided: ${Array.isArray(education) ? education.join(', ') : education}`
  } else {
    result.criteria.education.passed = false
    result.criteria.education.score = 0
    result.criteria.education.details = 'No education information provided'
    result.recommendations.push('Add your education background to strengthen your profile')
  }
}

/**
 * Evaluate professional background
 */
function evaluateProfessionalBackground(
  mentorProfile: any,
  criteria: MentorQualificationCriteria,
  result: QualificationResult
) {
  const hasCurrentRole = !!(mentorProfile?.mentor_current_role || mentorProfile?.current_role)
  const hasCompany = !!(mentorProfile?.company)
  const maxScore = 20

  let score = 0
  let details = []

  if (criteria.requiresCurrentRole && hasCurrentRole) {
    score += maxScore * 0.6
    details.push('Current role provided')
  } else if (criteria.requiresCurrentRole && !hasCurrentRole) {
    result.missingRequirements.push('Current professional role')
  }

  if (criteria.requiresCompany && hasCompany) {
    score += maxScore * 0.4
    details.push('Company information provided')
  } else if (criteria.requiresCompany && !hasCompany) {
    result.missingRequirements.push('Company information')
  }

  if (!criteria.requiresCurrentRole && !criteria.requiresCompany) {
    score = maxScore // If neither is required, give full points
    details.push('Professional background requirements met')
  }

  result.criteria.professional.passed = score >= maxScore * 0.5
  result.criteria.professional.score = score
  result.criteria.professional.details = details.join(', ') || 'Professional background incomplete'
}

/**
 * Evaluate profile completion
 */
function evaluateProfileCompletion(
  personalDetails: any,
  mentorProfile: any,
  criteria: MentorQualificationCriteria,
  result: QualificationResult
) {
  const completion = personalDetails?.profile_completion || 0
  const maxScore = 15

  if (completion >= criteria.minProfileCompletion) {
    result.criteria.completion.passed = true
    result.criteria.completion.score = maxScore
    result.criteria.completion.details = `Profile ${completion}% complete (required: ${criteria.minProfileCompletion}%)`
  } else {
    result.criteria.completion.passed = false
    result.criteria.completion.score = (completion / criteria.minProfileCompletion) * maxScore
    result.criteria.completion.details = `Profile ${completion}% complete (required: ${criteria.minProfileCompletion}%)`
    result.missingRequirements.push(`Complete profile to at least ${criteria.minProfileCompletion}%`)
  }
}

/**
 * Generate recommendations for improvement
 */
function generateRecommendations(
  result: QualificationResult,
  criteria: MentorQualificationCriteria
) {
  if (!result.criteria.experience.passed) {
    result.recommendations.push(`Gain more professional experience (currently ${result.criteria.experience.details})`)
  }

  if (!result.criteria.expertise.passed) {
    result.recommendations.push(`Add more areas of expertise to your profile (currently ${result.criteria.expertise.details})`)
  }

  if (!result.criteria.professional.passed) {
    result.recommendations.push('Complete your professional background information')
  }

  if (!result.criteria.completion.passed) {
    result.recommendations.push('Complete your profile to meet the minimum completion requirement')
  }

  if (result.qualified) {
    result.recommendations.push('Congratulations! You meet the requirements to be a mentor.')
  } else {
    result.recommendations.push('Focus on addressing the missing requirements to qualify as a mentor.')
  }
}

/**
 * Get mentor qualification status for display
 */
export async function getMentorQualificationStatus(userId: string): Promise<{
  qualified: boolean
  score: number
  nextSteps: string[]
}> {
  const result = await evaluateMentorQualification(userId)
  
  return {
    qualified: result.qualified,
    score: result.score,
    nextSteps: result.qualified ? ['You can now offer mentorship!'] : result.recommendations
  }
}
