# Virtual Community — Design Language (Concept 1: Glass Shelf Social)

This document defines tokens, components, and patterns to modernize the virtual community UI while preserving existing functionality and technical architecture. It follows your platform strategy, mentorship rules, AI-first search, and featured discovery model.

## 1. Foundations

### 1.1 Color Tokens
Use CSS variables (see shared.css). Adjust values to your exact palette.

- --color-bg: base page background
- --color-surface: card surface
- --color-glass: translucent surface on top of bg (alpha)
- --color-border: subtle hairlines
- --color-text: primary text
- --color-text-muted: secondary text
- --color-accent: primary accent (buttons/links)
- --color-info | --color-success | --color-warning | --color-danger
- Category colors (profiles): --category-innovator, --category-student, --category-mentor, etc.
- Events palette: --event-date-bg, --event-date-text

Guidelines
- Use glass surfaces sparingly (nav, shelves) with backdrop blur
- Rely on white/near-white cards with soft shadows for readability

### 1.2 Typography
- Body: 16/24
- Small: 14/20
- Title: 20/28
- Section: 24/32
- Display: 32/40
- Use system font stack or your brand fonts

### 1.3 Spacing & Radius
- Spacing scale: 4, 8, 12, 16, 20, 24, 32, 40
- Radii: 6, 10, 14, 18, 24 (large cards use 24)
- Elevation: e1/e2/e3 soft shadows (see shared.css)

### 1.4 Iconography
- Outline/line icons only. Recommended: Lucide (https://lucide.dev) or Heroicons Outline.
- Sizes: 16, 20, 24. Active state can use 2px stroke or subtle filled background pill.
- All icons must have aria-label on clickable containers.

### 1.5 Motion
- Hover: 120ms ease
- Open/close: 200ms ease
- Use subtle scale/translate for shelves; no large parallax

### 1.6 Accessibility
- Minimum contrast 4.5:1 for body text; 3:1 for large text
- Focus rings visible on all interactive elements, not hidden by glass

## 2. Layout Patterns

### 2.1 Global Shell
- Top navigation (glass) — search (AI/text2sql), Create, notifications, profile
- 3‑pane body: left sidebar (nav), center content, right sidebar (insights)
- No bottom nav on desktop
- Logo click navigates to landing page

### 2.2 Featured Discovery
- Horizontal “Featured” shelf at the top of each tab
- Feed tab order: latest featured [Blog, Event, Marketplace, Profile] when available
- Events tab has a “Refresh Featured” control
- Never fabricate content; show explicit empty states

### 2.3 AI Triggers
- Replace classic filters with AI chips and a prompt input
- Chips reflect context (tab category, tags)
- Quick replies shown at the bottom of chat or near the composer

## 3. Content Components

Each component exists as an HTML template with data-* hooks. Cards never hardcode data; the app binds Supabase rows into them.

### 3.1 ShelfCard (generic)
- Visual: compact, 16px radius, image or color background, white text with gradient overlay when necessary
- Variants: blog, event, marketplace, profile

### 3.2 Post Cards (timeline)
- General Post: avatar, name, time, text/media, actions row (like/comment/share optional)
- Blog Card: full-image header, gradient overlay, tags at bottom of image; remove share/bookmark icons; tags visible
- Event Card: date badge overlay; badge has rounded left edges and square right edges; uses real event date
- Marketplace Card: product image left, details right or grid variant; price and status chips
- Profile Card: category-colored header bar; compact bio

### 3.3 Date Badge (events)
- Format: [MON] [DD]
- Style: pill with left corners rounded (top-left/bottom-left), right corners square
- Colors: use events palette; ensure contrast on images

## 4. Navigation

### 4.1 Top Bar (glass)
- Left: hamburger (opens side menu), Community button with pulse badge, logo (clickable)
- Center: AI Search input with command-hint (e.g., “Search posts, events…”) and quick chips
- Right: Create, notifications, profile menu

### 4.2 Sidebar (left)
- Outline icon list; active item has subtle gradient pill
- Dynamic entries: Mentorship hub appears for mentors/mentees only
- Coming Soon: Funding Hub/Events/Programs/Marketplace entries appear disabled with “Soon” label when required

### 4.3 Right Panel
- Suggestions/people to follow, Announcements, Calendar (events), Mentorship panel

## 5. States & Empty States
- Show “Nothing here yet” with a CTA to explore community when lists are empty
- Maintain strict data integrity: render only what Supabase returns

## 6. Implementation Notes
- Templates are framework-agnostic HTML with data-* hooks and ARIA labels
- Outline icons via Lucide (client-side) or your existing icon pipeline
- Featured rails are horizontal scrollers with snap; use buttons for keyboard scroll
- Do not change routes; modernize markup and classes only
- Gate mentorship sidebar via role checks; never show to other roles

## 7. Mapping to Tabs
- Feed: featured shelf (1 of each: blog, event, marketplace, profile) → composer with AI triggers → mixed timeline
- Profiles: featured profiles rail → responsive grid by category color → filters replaced by AI chips
- Events: featured timeline rail + refresh button → list of events (not all featured) with accurate dates
- Blog: main featured story (left) + list (right); gradient overlay; tags at bottom; no share/bookmark icons
- Marketplace: showcase grid with product emphasis; featured shelf on top

## 8. Test & QA
- Playwright: smoke-render pages, verify ARIA labels, verify shelf order on Feed
- Visual snapshots for date badge and blog overlay
- Performance: scroll at 60fps; prefer CSS containment on shelves

## 9. Icon Set
- Default: Lucide outline; fallback: Heroicons outline
- Naming matches data-icon attributes; replace dynamically during render

## 10. Change Management
- Ship behind feature flag (?newUI=1). Migrate tab-by-tab.
- Remove conflicting legacy UI incrementally to avoid data display issues.

