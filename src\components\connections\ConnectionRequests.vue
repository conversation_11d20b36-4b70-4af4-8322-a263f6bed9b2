<template>
  <div class="connection-requests">
    <div v-if="title" class="text-h6 q-mb-md">{{ title }}</div>

    <div v-if="loading" class="text-center q-pa-md">
      <q-spinner color="primary" size="2em" />
      <p>Loading connection requests...</p>
    </div>

    <div v-else-if="localRequests.length === 0" class="text-center q-pa-md text-grey">
      <q-icon name="info" size="2em" />
      <p>No connection requests to display</p>
    </div>

    <div v-else>
      <div class="row q-col-gutter-md">
        <div
          v-for="request in localRequests"
          :key="request.id"
          class="col-12"
        >
          <q-card class="request-card">
            <q-item>
              <q-item-section avatar>
                <q-avatar>
                  <img
                    v-if="request.user?.avatar_url"
                    :src="request.user.avatar_url"
                  />
                  <div v-else class="bg-primary text-white flex flex-center full-height">
                    {{ getInitials(request.user) }}
                  </div>
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <q-item-label>
                  {{ getUserName(request.user) }}
                </q-item-label>
                <q-item-label caption>
                  {{ request.connection_type }}
                </q-item-label>
                <q-item-label caption>
                  Requested {{ formatDate(request.created_at) }}
                </q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-btn
                  flat
                  round
                  color="primary"
                  icon="person"
                  :to="{ name: 'user-profile', params: { id: request.user_id } }"
                >
                  <q-tooltip>View Profile</q-tooltip>
                </q-btn>

                <q-btn
                  flat
                  round
                  color="positive"
                  icon="check"
                  @click="handleAccept(request.id)"
                  :loading="acceptingId === request.id"
                >
                  <q-tooltip>Accept</q-tooltip>
                </q-btn>

                <q-btn
                  flat
                  round
                  color="negative"
                  icon="close"
                  @click="handleDecline(request.id)"
                  :loading="decliningId === request.id"
                >
                  <q-tooltip>Decline</q-tooltip>
                </q-btn>
              </q-item-section>
            </q-item>
          </q-card>
        </div>
      </div>

      <div v-if="hasMore" class="text-center q-mt-md">
        <q-btn
          outline
          color="primary"
          label="Load More"
          :loading="loadingMore"
          @click="loadMore"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { date, Notify } from 'quasar';
import { useConnectionsStore } from '../../stores/connections';
import { useActivityNotificationsStore } from '../../stores/activityNotifications';
import { getUniversalUsername } from '../../utils/userUtils';
import { supabase } from '../../lib/supabase';
import { useConnectionService } from '../../services/connectionService';

const props = defineProps({
  title: {
    type: String,
    default: 'Connection Requests'
  },
  limit: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits(['connectionAccepted']);

const connectionsStore = useConnectionsStore();
const connectionService = useConnectionService();
const currentPage = ref(1);
const hasMore = ref(false);
const acceptingId = ref(null);
const decliningId = ref(null);
const loadingMore = ref(false);
const localRequests = ref([]);
const loading = ref(true);

// Also use the store's requests as a fallback
const requests = computed(() => {
  // If we have local requests, use those
  if (localRequests.value.length > 0) {
    return localRequests.value;
  }
  // Otherwise, use the store's requests
  return connectionsStore.connectionRequests;
});

onMounted(async () => {
  await loadRequests();
});

async function loadRequests() {
  try {
    loading.value = true;
    console.log('Loading connection requests...');

    // Debug: Check if the user is authenticated
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('User not authenticated when loading connection requests');
      return;
    }

    console.log('Current user ID:', user.id);

    // Use connection service instead of direct Supabase calls
    const requests = await connectionService.getConnectionRequests(5, 1);

    console.log('Found connection requests:', requests?.length || 0);

    if (!requests || requests.length === 0) {
      console.log('No connection requests found');
      localRequests.value = [];
      connectionsStore.connectionRequests = [];
      return;
    }

    // Log the connection requests from service
    console.log('Connection requests from service:', requests);

    // Map service response to component format
    const enhancedRequests = requests.map(req => ({
      ...req,
      user: {
        id: req.user_id,
        email: req.requester_email,
        first_name: req.requester_name?.split(' ')[0] || '',
        last_name: req.requester_name?.split(' ').slice(1).join(' ') || '',
        profile_name: req.requester_profile_name
      }
    }));

    console.log('Enhanced requests:', enhancedRequests);

    // Update both the local state and the store
    localRequests.value = enhancedRequests;
    connectionsStore.connectionRequests = enhancedRequests;

    // Also try the store method for consistency
    await connectionsStore.fetchConnectionRequests(
      props.limit,
      currentPage.value
    );

    hasMore.value = enhancedRequests.length === props.limit;
  } catch (error) {
    console.error('Error loading connection requests:', error);
  } finally {
    loading.value = false;
  }
}

async function loadMore() {
  try {
    loadingMore.value = true;
    currentPage.value++;

    const result = await connectionsStore.fetchConnectionRequests(
      props.limit,
      currentPage.value
    );

    // Don't update the requests directly, as they're computed from the store
    hasMore.value = result.length === props.limit;
  } catch (error) {
    console.error('Error loading more connection requests:', error);
    currentPage.value = Math.max(1, currentPage.value - 1);
  } finally {
    loadingMore.value = false;
  }
}

async function handleAccept(requestId) {
  try {
    acceptingId.value = requestId;
    const success = await connectionsStore.acceptConnection(requestId);

    if (success) {
      // Remove the request from both local state and store
      localRequests.value = localRequests.value.filter(req => req.id !== requestId);
      connectionsStore.connectionRequests = connectionsStore.connectionRequests.filter(req => req.id !== requestId);

      // Update the connection requests count in the activity notifications store
      const activityNotificationsStore = useActivityNotificationsStore();
      await activityNotificationsStore.fetchConnectionRequestsCount();

      // Refresh the connections list to show the new connection
      await connectionsStore.fetchUserConnections();

      // Emit event to parent to refresh connections list
      emit('connectionAccepted');

      Notify.create({
        color: 'positive',
        message: 'Connection request accepted',
        icon: 'check_circle',
        position: 'top',
        timeout: 2000
      });
    } else {
      throw new Error('Failed to accept connection request');
    }
  } catch (error) {
    console.error('Error accepting connection request:', error);

    Notify.create({
      color: 'negative',
      message: 'Failed to accept connection request',
      icon: 'error',
      position: 'top',
      timeout: 2000
    });
  } finally {
    acceptingId.value = null;
  }
}

async function handleDecline(requestId) {
  try {
    decliningId.value = requestId;

    // Use connection service instead of direct Supabase calls
    await connectionService.declineConnectionRequest(requestId);

    // Remove from both local state and store
    localRequests.value = localRequests.value.filter(req => req.id !== requestId);
    connectionsStore.connectionRequests = connectionsStore.connectionRequests.filter(req => req.id !== requestId);

    // Update the connection requests count in the activity notifications store
    const activityNotificationsStore = useActivityNotificationsStore();
    await activityNotificationsStore.fetchConnectionRequestsCount();

    Notify.create({
      color: 'info',
      message: 'Connection request declined',
      icon: 'info',
      position: 'top',
      timeout: 2000
    });
  } catch (error) {
    console.error('Error declining connection request:', error);

    Notify.create({
      color: 'negative',
      message: 'Failed to decline connection request',
      icon: 'error',
      position: 'top',
      timeout: 2000
    });
  } finally {
    decliningId.value = null;
  }
}

function getInitials(user) {
  if (!user) return '?';

  if (user.first_name && user.last_name) {
    return `${user.first_name[0]}${user.last_name[0]}`.toUpperCase();
  }

  if (user.email) {
    return user.email[0].toUpperCase();
  }

  return '?';
}

function getUserName(user) {
  return getUniversalUsername(user);
}

function formatDate(dateString) {
  return date.formatDate(dateString, 'MMM D, YYYY');
}

// Expose the loadRequests function to the parent component
defineExpose({
  loadRequests
});
</script>

<style scoped>
.connection-requests {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.request-card {
  margin-bottom: 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.request-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .connection-requests {
    padding: 12px;
  }
}
</style>
