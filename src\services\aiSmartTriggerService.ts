/**
 * AI Smart Trigger Service
 * 
 * Provides context-aware AI triggers throughout the platform with intelligent
 * prefilled messages and contextual assistance.
 */

import { aiChatService } from './aiChatService'
import { useAuthStore } from '@/stores/auth'

export interface AITriggerContext {
  page: string
  section?: string
  user_action?: string
  profile_type?: string
  profile_completion?: number
  current_data?: any
  intent?: 'help' | 'discovery' | 'completion' | 'networking' | 'optimization'
}

export interface AITriggerConfig {
  id: string
  label: string
  icon: string
  color: string
  tooltip: string
  prefilled_message: string
  context: AITriggerContext
  show_conditions?: {
    auth_required?: boolean
    min_profile_completion?: number
    profile_types?: string[]
    pages?: string[]
  }
}

export class AISmartTriggerService {

  /**
   * Get contextual AI triggers for a specific page/section
   */
  getContextualTriggers(
    page: string, 
    section?: string, 
    additionalContext?: any
  ): AITriggerConfig[] {
    const authStore = useAuthStore()
    const user = authStore.user
    const profile = authStore.profile

    const baseContext: AITriggerContext = {
      page,
      section,
      profile_type: profile?.profile_type,
      profile_completion: profile?.profile_completion || 0
    }

    // Get page-specific triggers
    const triggers = this.getPageSpecificTriggers(page, baseContext, additionalContext)
    
    // Filter based on show conditions
    return triggers.filter(trigger => this.shouldShowTrigger(trigger, user, profile))
  }

  /**
   * Get page-specific AI triggers
   */
  private getPageSpecificTriggers(
    page: string, 
    context: AITriggerContext,
    additionalContext?: any
  ): AITriggerConfig[] {
    switch (page) {
      case 'profile':
        return this.getProfilePageTriggers(context, additionalContext)
      case 'discover':
        return this.getDiscoverPageTriggers(context, additionalContext)
      case 'posts':
        return this.getPostsPageTriggers(context, additionalContext)
      case 'dashboard':
        return this.getDashboardTriggers(context, additionalContext)
      case 'community':
        return this.getCommunityTriggers(context, additionalContext)
      case 'virtual-community':
        return this.getVirtualCommunityTriggers(context, additionalContext)
      default:
        return this.getGenericTriggers(context)
    }
  }

  /**
   * Profile page AI triggers
   */
  private getProfilePageTriggers(context: AITriggerContext, additionalContext?: any): AITriggerConfig[] {
    const triggers: AITriggerConfig[] = []
    const isOwnProfile = additionalContext?.isCurrentUser
    const profileCompletion = context.profile_completion || 0

    if (isOwnProfile) {
      // Own profile triggers
      if (profileCompletion < 70) {
        triggers.push({
          id: 'profile-completion-help',
          label: 'Complete Profile',
          icon: 'auto_fix_high',
          color: 'primary',
          tooltip: 'Get AI help to complete your profile',
          prefilled_message: `Help me complete my ${context.profile_type || 'professional'} profile. I'm currently at ${profileCompletion}% completion. What should I focus on next?`,
          context: { ...context, intent: 'completion' }
        })
      }

      triggers.push({
        id: 'profile-optimization',
        label: 'Optimize Profile',
        icon: 'tune',
        color: 'purple',
        tooltip: 'Get suggestions to improve your profile',
        prefilled_message: `Review my ${context.profile_type || 'professional'} profile and suggest improvements to make it more attractive to potential connections.`,
        context: { ...context, intent: 'optimization' }
      })

      if (profileCompletion >= 50) {
        triggers.push({
          id: 'networking-suggestions',
          label: 'Find Connections',
          icon: 'people',
          color: 'blue',
          tooltip: 'Get personalized networking suggestions',
          prefilled_message: `Based on my ${context.profile_type || 'professional'} profile, who should I connect with? Help me find relevant people to network with.`,
          context: { ...context, intent: 'networking' }
        })
      }
    } else {
      // Viewing someone else's profile
      const viewedProfileType = additionalContext?.profileType || 'professional'
      
      triggers.push({
        id: 'connection-advice',
        label: 'Connection Advice',
        icon: 'psychology',
        color: 'green',
        tooltip: 'Get advice on connecting with this person',
        prefilled_message: `I'm viewing a ${viewedProfileType} profile. How should I approach them for a connection? What would be a good conversation starter?`,
        context: { ...context, intent: 'networking' }
      })

      triggers.push({
        id: 'collaboration-ideas',
        label: 'Collaboration Ideas',
        icon: 'handshake',
        color: 'orange',
        tooltip: 'Explore collaboration opportunities',
        prefilled_message: `Based on this ${viewedProfileType}'s profile and my ${context.profile_type || 'professional'} background, what collaboration opportunities might exist between us?`,
        context: { ...context, intent: 'discovery' }
      })
    }

    return triggers
  }

  /**
   * Discover page AI triggers
   */
  private getDiscoverPageTriggers(context: AITriggerContext, additionalContext?: any): AITriggerConfig[] {
    return [
      {
        id: 'smart-discovery',
        label: 'Smart Discovery',
        icon: 'explore',
        color: 'primary',
        tooltip: 'Get personalized discovery recommendations',
        prefilled_message: `Help me discover relevant ${context.profile_type || 'professional'}s and content based on my interests and goals. What should I be looking for?`,
        context: { ...context, intent: 'discovery' }
      },
      {
        id: 'filter-assistance',
        label: 'Filter Help',
        icon: 'filter_list',
        color: 'blue',
        tooltip: 'Get help with search filters',
        prefilled_message: 'Help me set up the right filters to find the most relevant profiles and content for my needs.',
        context: { ...context, intent: 'help' }
      },
      {
        id: 'networking-strategy',
        label: 'Networking Strategy',
        icon: 'psychology',
        color: 'purple',
        tooltip: 'Get networking strategy advice',
        prefilled_message: `As a ${context.profile_type || 'professional'}, what networking strategy should I use on this platform? Who should I prioritize connecting with?`,
        context: { ...context, intent: 'networking' }
      }
    ]
  }

  /**
   * Posts/Content page AI triggers
   */
  private getPostsPageTriggers(context: AITriggerContext, additionalContext?: any): AITriggerConfig[] {
    const triggers: AITriggerConfig[] = [
      {
        id: 'content-ideas',
        label: 'Content Ideas',
        icon: 'lightbulb',
        color: 'yellow-8',
        tooltip: 'Get content creation ideas',
        prefilled_message: `Suggest content ideas that would be valuable for my ${context.profile_type || 'professional'} audience. What topics should I write about?`,
        context: { ...context, intent: 'help' }
      },
      {
        id: 'engagement-tips',
        label: 'Engagement Tips',
        icon: 'thumb_up',
        color: 'green',
        tooltip: 'Learn how to engage effectively',
        prefilled_message: 'Give me tips on how to engage meaningfully with posts and build relationships through content interaction.',
        context: { ...context, intent: 'help' }
      }
    ]

    // Add content creation trigger if profile is complete enough
    if ((context.profile_completion || 0) >= 50) {
      triggers.push({
        id: 'create-post-help',
        label: 'Create Post',
        icon: 'edit',
        color: 'primary',
        tooltip: 'Get help creating a post',
        prefilled_message: 'Help me create an engaging post that would be valuable to my network. What should I write about?',
        context: { ...context, intent: 'help' }
      })
    }

    return triggers
  }

  /**
   * Dashboard AI triggers
   */
  private getDashboardTriggers(context: AITriggerContext, additionalContext?: any): AITriggerConfig[] {
    const triggers: AITriggerConfig[] = [
      {
        id: 'platform-guidance',
        label: 'Platform Guide',
        icon: 'map',
        color: 'primary',
        tooltip: 'Get guidance on using the platform',
        prefilled_message: `I'm on my dashboard. Help me understand how to make the most of this platform as a ${context.profile_type || 'user'}. What should I do next?`,
        context: { ...context, intent: 'help' }
      }
    ]

    // Add profile completion trigger if needed
    if ((context.profile_completion || 0) < 70) {
      triggers.push({
        id: 'complete-profile-dashboard',
        label: 'Complete Profile',
        icon: 'person_add',
        color: 'orange',
        tooltip: 'Get help completing your profile',
        prefilled_message: `My profile is ${context.profile_completion || 0}% complete. Guide me through completing the most important sections to improve my visibility.`,
        context: { ...context, intent: 'completion' }
      })
    }

    return triggers
  }

  /**
   * Community page AI triggers
   */
  private getCommunityTriggers(context: AITriggerContext, additionalContext?: any): AITriggerConfig[] {
    return [
      {
        id: 'community-navigation',
        label: 'Community Guide',
        icon: 'groups',
        color: 'blue',
        tooltip: 'Get help navigating the community',
        prefilled_message: 'Help me understand how to engage with the community effectively. What are the best practices for participation?',
        context: { ...context, intent: 'help' }
      },
      {
        id: 'find-discussions',
        label: 'Find Discussions',
        icon: 'forum',
        color: 'green',
        tooltip: 'Find relevant discussions',
        prefilled_message: `Help me find discussions and conversations that would be relevant to my interests as a ${context.profile_type || 'professional'}.`,
        context: { ...context, intent: 'discovery' }
      }
    ]
  }

  /**
   * Virtual Community AI triggers
   */
  private getVirtualCommunityTriggers(context: AITriggerContext, additionalContext?: any): AITriggerConfig[] {
    const baseContext = { ...context, intent: 'search' }

    // Get tab-specific triggers based on section
    const section = context.section || 'feed'

    const commonTriggers = [
      {
        id: 'ai_search',
        label: 'AI Search',
        icon: 'search',
        color: 'primary',
        tooltip: 'Use AI to search with natural language',
        prefilled_message: 'I want to use AI-powered search to find specific content. Help me search through posts, profiles, events, and other content using natural language queries.',
        context: { ...baseContext, intent: 'search' }
      },
      {
        id: 'recent_content',
        label: 'Recent Content',
        icon: 'schedule',
        color: 'blue',
        tooltip: 'Find recent posts and updates',
        prefilled_message: 'Show me recent posts and content from the last few days. I want to stay updated with the latest discussions.',
        context: { ...baseContext, intent: 'recent' }
      }
    ]

    // Add section-specific triggers
    switch (section) {
      case 'profiles':
        return [
          ...commonTriggers,
          {
            id: 'find_mentors',
            label: 'Find Mentors',
            icon: 'school',
            color: 'blue',
            tooltip: 'Find experienced mentors',
            prefilled_message: 'Help me find experienced mentors in my field who can provide guidance and support.',
            context: { ...baseContext, intent: 'mentors' }
          }
        ]

      case 'events':
        return [
          ...commonTriggers,
          {
            id: 'find_workshops',
            label: 'Find Workshops',
            icon: 'build',
            color: 'blue',
            tooltip: 'Find hands-on workshops',
            prefilled_message: 'Find hands-on workshops and training sessions where I can learn new skills.',
            context: { ...baseContext, intent: 'workshops' }
          }
        ]

      default: // feed and others
        return [
          ...commonTriggers,
          {
            id: 'innovation_content',
            label: 'Innovation',
            icon: 'lightbulb',
            color: 'amber',
            tooltip: 'Find innovation content',
            prefilled_message: 'Find content related to innovation, startups, and entrepreneurship.',
            context: { ...baseContext, intent: 'innovation' }
          }
        ]
    }
  }

  /**
   * Generic AI triggers for any page
   */
  private getGenericTriggers(context: AITriggerContext): AITriggerConfig[] {
    return [
      {
        id: 'general-help',
        label: 'Get Help',
        icon: 'help',
        color: 'blue',
        tooltip: 'Get general assistance',
        prefilled_message: 'I need help with using this platform. Can you guide me?',
        context: { ...context, intent: 'help' }
      }
    ]
  }

  /**
   * Check if a trigger should be shown based on conditions
   */
  private shouldShowTrigger(
    trigger: AITriggerConfig, 
    user: any, 
    profile: any
  ): boolean {
    const conditions = trigger.show_conditions
    if (!conditions) return true

    // Check auth requirement
    if (conditions.auth_required && !user) {
      return false
    }

    // Check profile completion
    if (conditions.min_profile_completion && 
        (profile?.profile_completion || 0) < conditions.min_profile_completion) {
      return false
    }

    // Check profile types
    if (conditions.profile_types && 
        !conditions.profile_types.includes(profile?.profile_type)) {
      return false
    }

    return true
  }

  /**
   * Trigger AI chat with context
   */
  async triggerAIChat(triggerId: string, context: AITriggerContext): Promise<void> {
    try {
      // Find the trigger configuration
      const triggers = this.getContextualTriggers(context.page, context.section)
      const trigger = triggers.find(t => t.id === triggerId)
      
      if (!trigger) {
        console.warn('Trigger not found:', triggerId)
        return
      }

      // Open AI chat with prefilled message
      await this.openAIChatWithMessage(trigger.prefilled_message, context)
      
    } catch (error) {
      console.error('Error triggering AI chat:', error)
    }
  }

  /**
   * Open AI chat with a prefilled message
   */
  private async openAIChatWithMessage(message: string, context: AITriggerContext): Promise<void> {
    // This will be implemented to integrate with the existing AIChatAssistant component
    // For now, we'll emit an event that the chat component can listen to
    
    const event = new CustomEvent('ai-trigger-chat', {
      detail: {
        message,
        context,
        timestamp: Date.now()
      }
    })
    
    window.dispatchEvent(event)
  }
}

// Export singleton instance
export const aiSmartTriggerService = new AISmartTriggerService()
