/**
 * Unified Profile Manager
 * 
 * This service consolidates all profile loading logic to eliminate duplicate implementations
 * and provide a single source of truth for profile data with intelligent caching.
 */

import { supabase } from '../lib/supabase'
import type { BaseProfile } from './profileService'
import { useUnifiedCache } from './unifiedCacheService'

export interface ProfileLoadOptions {
  context?: 'public' | 'private'
  forceRefresh?: boolean
  includeSpecialized?: boolean
}

export interface ProfileCacheEntry {
  data: BaseProfile
  timestamp: number
  context: string
}

export class ProfileManager {
  private static instance: ProfileManager
  private cache = useUnifiedCache()
  private loading = new Set<string>()
  
  static getInstance(): ProfileManager {
    if (!ProfileManager.instance) {
      ProfileManager.instance = new ProfileManager()
    }
    return ProfileManager.instance
  }
  
  /**
   * Get a profile by user ID with intelligent caching and deduplication
   */
  async getProfile(userId: string, options: ProfileLoadOptions = {}): Promise<BaseProfile | null> {
    const { context = 'private', forceRefresh = false, includeSpecialized = false } = options
    const cacheKey = `profile:${userId}:${context}`

    // Check unified cache first (unless force refresh requested)
    if (!forceRefresh) {
      const cached = this.cache.get<BaseProfile>(cacheKey)
      if (cached) {
        console.log(`ProfileManager: Cache hit for ${userId} (${context})`)
        return cached
      }
    }
    
    // Prevent duplicate requests for the same profile
    if (this.loading.has(cacheKey)) {
      console.log(`ProfileManager: Waiting for existing request ${userId} (${context})`)
      while (this.loading.has(cacheKey)) {
        await new Promise(resolve => setTimeout(resolve, 50))
      }
      // Return cached result after waiting
      return this.cache.get<BaseProfile>(cacheKey) || null
    }
    
    // Load from database
    this.loading.add(cacheKey)
    try {
      console.log(`ProfileManager: Loading ${userId} from database (${context})`)
      const profile = await this.loadFromDatabase(userId, options)
      
      if (profile) {
        // Cache with unified cache service
        this.cache.set(cacheKey, profile, {
          ttl: 5 * 60 * 1000, // 5 minutes
          storage: 'memory',
          invalidationPatterns: [`profile:${userId}:*`, `user:${userId}:*`]
        })
        console.log(`ProfileManager: Cached profile for ${userId} (${context})`)
      }
      
      return profile
    } catch (error) {
      console.error(`ProfileManager: Error loading profile ${userId}:`, error)
      return null
    } finally {
      this.loading.delete(cacheKey)
    }
  }
  
  /**
   * Load multiple profiles efficiently
   */
  async getProfiles(userIds: string[], options: ProfileLoadOptions = {}): Promise<BaseProfile[]> {
    const profiles = await Promise.allSettled(
      userIds.map(userId => this.getProfile(userId, options))
    )
    
    return profiles
      .filter((result): result is PromiseFulfilledResult<BaseProfile> => 
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value)
  }
  
  /**
   * Invalidate cached profile data for a user
   */
  invalidateProfile(userId: string, context?: string): void {
    if (context) {
      const cacheKey = `profile:${userId}:${context}`
      this.cache.delete(cacheKey)
    } else {
      // Invalidate all cached profiles for this user
      this.cache.triggerInvalidation('profile', userId)
    }
    console.log(`ProfileManager: Invalidated cache for user ${userId}`)
  }

  /**
   * Load profile data from database
   */
  private async loadFromDatabase(userId: string, options: ProfileLoadOptions): Promise<BaseProfile | null> {
    try {
      const { data, error } = await supabase
        .from('personal_details')
        .select('*')
        .eq('user_id', userId)
        .single()
      
      if (error) {
        if (error.code === 'PGRST116') {
          // No rows found - not an error, just no profile
          console.log(`ProfileManager: No profile found for user ${userId}`)
          return null
        }
        throw error
      }
      
      if (!data) {
        return null
      }
      
      // Convert database record to BaseProfile format
      const profile: BaseProfile = {
        user_id: data.user_id,
        email: data.email,
        first_name: data.first_name,
        last_name: data.last_name,
        profile_name: data.profile_name,
        profile_type: data.profile_type,
        profile_state: data.profile_state,
        profile_visibility: data.profile_visibility || 'public',
        profile_completion: data.profile_completion || 0,
        role: data.role,
        is_verified: data.is_verified || false,
        bio: data.bio,
        avatar_url: data.avatar_url,
        created_at: data.created_at,
        updated_at: data.updated_at
      }
      
      return this.sanitizeProfileData(profile)
    } catch (error) {
      console.error('ProfileManager: Database error:', error)
      throw error
    }
  }
  
  /**
   * Sanitize profile data to ensure consistency
   */
  private sanitizeProfileData(profile: any): BaseProfile {
    return {
      user_id: profile.user_id,
      email: profile.email || '',
      first_name: profile.first_name || '',
      last_name: profile.last_name || '',
      profile_name: profile.profile_name || '',
      profile_type: profile.profile_type || '',
      profile_state: profile.profile_state || 'DRAFT',
      profile_visibility: profile.profile_visibility || 'public',
      profile_completion: Math.max(0, Math.min(100, profile.profile_completion || 0)),
      role: profile.role || 'user',
      is_verified: Boolean(profile.is_verified),
      bio: profile.bio || '',
      avatar_url: profile.avatar_url || '',
      created_at: profile.created_at || new Date().toISOString(),
      updated_at: profile.updated_at || new Date().toISOString()
    }
  }
  
  /**
   * Invalidate cache for specific user or all users
   */
  invalidateCache(userId?: string): void {
    if (userId) {
      // Remove all cache entries for this user
      for (const key of this.cache.keys()) {
        if (key.startsWith(userId)) {
          this.cache.delete(key)
        }
      }
      console.log(`ProfileManager: Invalidated cache for user ${userId}`)
    } else {
      // Clear entire cache
      this.cache.clear()
      console.log('ProfileManager: Cleared entire cache')
    }
  }
  
  /**
   * Update profile and invalidate cache
   */
  async updateProfile(userId: string, updates: Partial<BaseProfile>): Promise<BaseProfile | null> {
    try {
      const { data, error } = await supabase
        .from('personal_details')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single()
      
      if (error) throw error
      
      // Invalidate cache for this user
      this.invalidateCache(userId)
      
      // Return updated profile
      return data ? this.sanitizeProfileData(data) : null
    } catch (error) {
      console.error('ProfileManager: Update error:', error)
      throw error
    }
  }
  
  /**
   * Get cache statistics for monitoring
   */
  getCacheStats() {
    const stats = this.cache.getStats()

    return {
      totalEntries: stats.totalEntries,
      validEntries: stats.totalEntries, // Unified cache automatically handles expiration
      expiredEntries: 0, // Unified cache cleans up expired entries automatically
      loadingRequests: this.loading.size,
      cacheHitRate: stats.hitRate,
      hits: stats.hits,
      misses: stats.misses,
      memoryUsage: stats.memoryUsage
    }
  }
  
  /**
   * Clean up expired cache entries
   */
  cleanupExpiredCache(): void {
    // Use the unified cache's built-in cleanup method
    this.cache.cleanup()
  }
}

// Export singleton instance
export const profileManager = ProfileManager.getInstance()

// Set up periodic cache cleanup - Reduced frequency
if (typeof window !== 'undefined') {
  setInterval(() => {
    profileManager.cleanupExpiredCache()
  }, 600000) // Clean up every 10 minutes instead of 1 minute
}
