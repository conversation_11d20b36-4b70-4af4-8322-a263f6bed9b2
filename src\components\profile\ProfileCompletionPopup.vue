<template>
  <q-dialog
    :model-value="show"
    @update:model-value="updateModelValue"
    persistent
    transition-show="scale"
    transition-hide="scale"
    :maximized="isSmallScreen"
  >
    <q-card class="profile-completion-popup">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Complete Your Profile</div>
        <q-space />
        <q-btn
          flat
          round
          dense
          v-close-popup
          @click="closePopup"
        >
          <unified-icon name="close" />
        </q-btn>
      </q-card-section>

      <q-card-section>
        <div v-if="isInitial" class="text-center">
          <img src="/logo.png" alt="ZbInnovation Logo" class="q-mb-md" style="width: 80px; height: 80px;" />

          <div class="text-h6 q-mb-md">Welcome to ZbInnovation!</div>
          <p class="q-mb-md">
            Complete your profile to get the most out of our platform and connect with other members.
          </p>

          <div class="text-subtitle1 q-mb-sm">Your profile is incomplete</div>
          <q-linear-progress
            :value="profileCompletion / 100"
            color="primary"
            class="q-mb-lg"
            size="10px"
          />

          <q-btn
            color="primary"
            label="Complete Your Profile"
            class="q-mb-md full-width cta-button"
            size="md"
            rounded
            @click="goToProfileCompletion"
          />

          <q-btn
            flat
            color="grey-7"
            label="Remind Me Later"
            class="full-width"
            @click="remindLater"
          />
        </div>

        <div v-else class="text-center">
          <img src="/logo.png" alt="ZbInnovation Logo" class="q-mb-md" style="width: 80px; height: 80px;" />

          <div class="text-h6 q-mb-md">Your Profile Needs Attention</div>
          <p class="q-mb-md">
            Your profile is incomplete. Completing your profile will help you get the most out of the ZbInnovation.
          </p>

          <div class="text-subtitle1 q-mb-sm">Your profile is incomplete</div>
          <q-linear-progress
            :value="profileCompletion / 100"
            color="primary"
            class="q-mb-lg"
            size="10px"
          />

          <q-btn
            color="primary"
            label="Continue Profile Setup"
            class="q-mb-md full-width cta-button"
            size="md"
            rounded
            @click="goToProfileCompletion"
          />

          <q-btn
            flat
            color="grey-7"
            label="Remind Me Later"
            class="full-width"
            @click="remindLater"
          />
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useProfileStore } from '@/stores/profile';
// Import UnifiedIcon component used in the template
import UnifiedIcon from '../ui/UnifiedIcon.vue';
import { useQuasar } from 'quasar';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  isInitial: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue', 'remind-later']);

// Use screen size from Quasar
const $q = useQuasar();
const router = useRouter();
const profileStore = useProfileStore();

// Create a ref for the v-model
const show = computed(() => props.modelValue);

// Determine if screen is small
const isSmallScreen = ref(false);

// Check screen size on component creation
try {
  if ($q.screen && $q.screen.lt && $q.screen.lt.sm) {
    isSmallScreen.value = $q.screen.lt.sm;
  }
} catch (err) {
  console.warn('Error checking screen size:', err);
  // Default to false if there's an error
  isSmallScreen.value = false;
}

// Watch for changes to the show value
function updateModelValue(value: boolean) {
  emit('update:modelValue', value);
}

// Use the stored profile_completion value for consistency
const profileCompletion = computed(() => {
  if (!profileStore.currentProfile) return 0;
  return profileStore.currentProfile.profile_completion || 0;
});

// Go to profile completion page
function goToProfileCompletion() {
  updateModelValue(false);

  if (profileStore.currentProfile && profileStore.currentProfile.user_id) {
    // If we have a current profile, navigate to the profile edit route with the profile ID
    router.push({
      name: 'profile-edit',
      params: { id: profileStore.currentProfile.user_id }
    });
  } else {
    // If no profile exists, navigate to the profile create route
    router.push({ name: 'profile-create' });
  }
}

// Remind later (hide for 24 hours)
function remindLater() {
  updateModelValue(false);
  emit('remind-later');

  // Store the timestamp when the user chose to be reminded later
  localStorage.setItem('profileCompletionRemindLater', Date.now().toString());
}

// Close popup without any action
function closePopup() {
  updateModelValue(false);
}
</script>

<style scoped>
.profile-completion-popup {
  width: 100%;
  max-width: 500px;
  border-radius: 8px;
}

.cta-button {
  border-radius: 25px !important;
  min-width: 160px;
  height: 40px;
  font-weight: 600;
}

@media (max-width: 599px) {
  .profile-completion-popup {
    max-width: none;
  }
}
</style>
