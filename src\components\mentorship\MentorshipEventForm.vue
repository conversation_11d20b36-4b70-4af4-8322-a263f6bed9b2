<template>
  <q-card class="mentorship-event-form">
    <q-card-section class="bg-primary text-white">
      <div class="text-h6">
        <unified-icon name="event" class="q-mr-sm" />
        {{ isEditing ? 'Edit Event' : 'Create Mentorship Event' }}
      </div>
      <div class="text-subtitle2">
        {{ isEditing ? 'Update your mentorship event details' : 'Share your knowledge with the community' }}
      </div>
    </q-card-section>

    <q-form @submit="handleSubmit" class="q-pa-md">
      <!-- Basic Information -->
      <div class="row q-gutter-md">
        <div class="col-12 col-md-7">
          <q-input
            v-model="form.title"
            label="Event Title *"
            outlined
            :rules="[val => !!val || 'Title is required']"
            hint="Give your event a compelling title"
          />
        </div>
        <div class="col-12 col-md-4">
          <q-select
            v-model="form.event_type"
            :options="eventTypeOptions"
            label="Event Type *"
            outlined
            emit-value
            map-options
            :rules="[val => !!val || 'Event type is required']"
          />
        </div>
      </div>

      <q-input
        v-model="form.description"
        label="Description *"
        type="textarea"
        outlined
        rows="4"
        :rules="[val => !!val || 'Description is required']"
        hint="Describe what participants will learn and what to expect"
        class="q-mt-md"
      />

      <!-- Event Details -->
      <div class="row q-gutter-md q-mt-md">
        <div class="col-12 col-md-5">
          <q-input
            v-model="form.category"
            label="Category"
            outlined
            hint="e.g., Technology, Business, Career Development"
          />
        </div>
        <div class="col-12 col-md-3">
          <q-input
            v-model.number="form.max_participants"
            label="Max Participants"
            type="number"
            outlined
            :rules="[val => val > 0 || 'Must be greater than 0']"
            hint="Maximum number of attendees"
          />
        </div>
        <div class="col-12 col-md-3">
          <q-input
            v-model.number="form.price"
            label="Price (USD)"
            type="number"
            step="0.01"
            outlined
            :disable="form.is_free"
            hint="Leave 0 for free events"
          />
        </div>
      </div>

      <!-- Scheduling -->
      <div class="row q-gutter-md q-mt-md">
        <div class="col-12 col-md-5">
          <q-input
            v-model="form.scheduled_start_time"
            label="Start Date & Time *"
            type="datetime-local"
            outlined
            :rules="[val => !!val || 'Start time is required']"
          />
        </div>
        <div class="col-12 col-md-5">
          <q-input
            v-model="form.scheduled_end_time"
            label="End Date & Time *"
            type="datetime-local"
            outlined
            :rules="[val => !!val || 'End time is required']"
          />
        </div>
      </div>

      <!-- Meeting Details -->
      <div class="row q-gutter-md q-mt-md">
        <div class="col-12 col-md-5">
          <q-select
            v-model="form.meeting_platform"
            :options="platformOptions"
            label="Meeting Platform"
            outlined
            emit-value
            map-options
          />
        </div>
        <div class="col-12 col-md-6">
          <q-input
            v-model="form.meeting_link"
            label="Meeting Link"
            outlined
            hint="Zoom, Google Meet, or other platform link"
          />
        </div>
      </div>

      <!-- Options -->
      <div class="q-mt-md">
        <q-checkbox
          v-model="form.is_public"
          label="Make this event public"
          class="q-mr-md"
        />
        <q-checkbox
          v-model="form.is_free"
          label="Free event"
          @update:model-value="onFreeToggle"
        />
      </div>

      <!-- Learning Objectives -->
      <div class="q-mt-md">
        <q-input
          v-model="learningObjectiveInput"
          label="Learning Objectives"
          outlined
          hint="Press Enter to add each objective"
          @keyup.enter="addLearningObjective"
        />
        <div v-if="form.learning_objectives.length > 0" class="q-mt-sm">
          <q-chip
            v-for="(objective, index) in form.learning_objectives"
            :key="index"
            removable
            @remove="removeLearningObjective(index)"
            color="primary"
            text-color="white"
            class="q-mr-sm q-mb-sm"
          >
            {{ objective }}
          </q-chip>
        </div>
      </div>

      <!-- Prerequisites -->
      <div class="q-mt-md">
        <q-input
          v-model="prerequisiteInput"
          label="Prerequisites"
          outlined
          hint="Press Enter to add each prerequisite"
          @keyup.enter="addPrerequisite"
        />
        <div v-if="form.prerequisites.length > 0" class="q-mt-sm">
          <q-chip
            v-for="(prerequisite, index) in form.prerequisites"
            :key="index"
            removable
            @remove="removePrerequisite(index)"
            color="secondary"
            text-color="white"
            class="q-mr-sm q-mb-sm"
          >
            {{ prerequisite }}
          </q-chip>
        </div>
      </div>

      <!-- Actions -->
      <q-card-actions align="right" class="q-mt-lg">
        <q-btn
          flat
          label="Cancel"
          @click="$emit('cancel')"
          class="q-mr-sm"
        />
        <q-btn
          type="submit"
          color="primary"
          :label="isEditing ? 'Update Event' : 'Create Event'"
          :loading="loading"
          :disable="!isFormValid"
        />
      </q-card-actions>
    </q-form>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useNotificationStore } from '../../stores/notifications'
import UnifiedIcon from '../ui/UnifiedIcon.vue'

// Props
const props = defineProps<{
  event?: any
  loading?: boolean
}>()

// Emits
const emit = defineEmits<{
  submit: [eventData: any]
  cancel: []
}>()

// Stores
const notifications = useNotificationStore()

// State
const isEditing = computed(() => !!props.event)
const learningObjectiveInput = ref('')
const prerequisiteInput = ref('')

// Form data
const form = ref({
  title: '',
  description: '',
  event_type: 'workshop',
  category: '',
  max_participants: 20,
  is_public: true,
  is_free: true,
  price: 0,
  scheduled_start_time: '',
  scheduled_end_time: '',
  meeting_platform: 'zoom',
  meeting_link: '',
  learning_objectives: [] as string[],
  prerequisites: [] as string[]
})

// Options
const eventTypeOptions = [
  { label: 'Workshop', value: 'workshop' },
  { label: 'Webinar', value: 'webinar' },
  { label: 'Group Session', value: 'group-session' },
  { label: 'Networking', value: 'networking' },
  { label: 'Panel Discussion', value: 'panel' },
  { label: 'Masterclass', value: 'masterclass' },
  { label: 'Q&A Session', value: 'q-and-a' }
]

const platformOptions = [
  { label: 'Zoom', value: 'zoom' },
  { label: 'Google Meet', value: 'google-meet' },
  { label: 'Microsoft Teams', value: 'teams' },
  { label: 'In Person', value: 'in-person' },
  { label: 'Other', value: 'other' }
]

// Computed
const isFormValid = computed(() => {
  return form.value.title && 
         form.value.description && 
         form.value.scheduled_start_time && 
         form.value.scheduled_end_time
})

// Methods
function onFreeToggle(isFree: boolean) {
  if (isFree) {
    form.value.price = 0
  }
}

function addLearningObjective() {
  if (learningObjectiveInput.value.trim()) {
    form.value.learning_objectives.push(learningObjectiveInput.value.trim())
    learningObjectiveInput.value = ''
  }
}

function removeLearningObjective(index: number) {
  form.value.learning_objectives.splice(index, 1)
}

function addPrerequisite() {
  if (prerequisiteInput.value.trim()) {
    form.value.prerequisites.push(prerequisiteInput.value.trim())
    prerequisiteInput.value = ''
  }
}

function removePrerequisite(index: number) {
  form.value.prerequisites.splice(index, 1)
}

function handleSubmit() {
  if (!isFormValid.value) {
    notifications.error('Please fill in all required fields')
    return
  }

  // Validate dates
  const startTime = new Date(form.value.scheduled_start_time)
  const endTime = new Date(form.value.scheduled_end_time)
  
  if (startTime >= endTime) {
    notifications.error('End time must be after start time')
    return
  }

  if (startTime <= new Date()) {
    notifications.error('Start time must be in the future')
    return
  }

  emit('submit', { ...form.value })
}

// Initialize form with event data if editing
watch(() => props.event, (newEvent) => {
  if (newEvent) {
    form.value = {
      title: newEvent.title || '',
      description: newEvent.description || '',
      event_type: newEvent.event_type || 'workshop',
      category: newEvent.category || '',
      max_participants: newEvent.max_participants || 20,
      is_public: newEvent.is_public ?? true,
      is_free: newEvent.is_free ?? true,
      price: newEvent.price || 0,
      scheduled_start_time: newEvent.scheduled_start_time ? 
        new Date(newEvent.scheduled_start_time).toISOString().slice(0, 16) : '',
      scheduled_end_time: newEvent.scheduled_end_time ? 
        new Date(newEvent.scheduled_end_time).toISOString().slice(0, 16) : '',
      meeting_platform: newEvent.meeting_platform || 'zoom',
      meeting_link: newEvent.meeting_link || '',
      learning_objectives: newEvent.learning_objectives || [],
      prerequisites: newEvent.prerequisites || []
    }
  }
}, { immediate: true })
</script>

<style scoped>
.mentorship-event-form {
  max-width: 800px;
  margin: 0 auto;
}
</style>
