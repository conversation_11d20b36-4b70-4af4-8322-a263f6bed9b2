import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/lib/supabase';
import { useNotificationStore } from './notifications';
import { useProfileStore } from './profile';
import { getUniversalUsername } from '@/utils/userUtils';
import { useGlobalServicesStore } from './globalServices';

// Table existence will be cached using unified cache service

// Unified services handle retry logic automatically

export interface Message {
  id: string;
  sender_id: string;
  recipient_id: string;
  content: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
  sender?: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    avatar_url: string;
  };
  recipient?: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    avatar_url: string;
  };
}

export interface Conversation {
  id: string;
  sender_id: string;
  recipient_id: string;
  content: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
  other_user_id: string;
  other_user?: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    avatar_url: string;
  };
}

/**
 * Messaging Store
 *
 * This store centralizes all messaging-related state and operations.
 * It provides functions for sending and retrieving direct messages between users.
 */
export const useMessagingStore = defineStore('messaging', () => {
  // State
  const isLoading = ref(false);
  const unreadCount = ref(0);
  const conversations = ref<Conversation[]>([]);
  const messages = ref<Record<string, Message[]>>({});

  // Retry tracking for operations (moved inside store)
  const retryAttempts = new Map<string, number>();

  /**
   * Check if an operation should be retried
   * @param operationKey Unique key for the operation
   * @param maxRetries Maximum number of retries allowed
   * @returns true if operation should be retried, false otherwise
   */
  function shouldRetry(operationKey: string, maxRetries: number = 3): boolean {
    const attempts = retryAttempts.get(operationKey) || 0;
    if (attempts >= maxRetries) {
      // Reset the counter after max attempts reached
      retryAttempts.delete(operationKey);
      return false;
    }

    // Increment attempt counter
    retryAttempts.set(operationKey, attempts + 1);
    return true;
  }
  const error = ref<string | null>(null);

  // Services
  const globalServices = useGlobalServicesStore();
  const activityService = globalServices.activityService;
  const notifications = useNotificationStore();
  const profileStore = useProfileStore();
  const cache = globalServices.cacheService;
  const realtime = globalServices.realtimeService;

  // Unified real-time subscription management
  let messageSubscription: any = null;

  // Unified services handle retry logic automatically

  /**
   * Check if the user_messages table exists
   * Uses unified cache to avoid repeated database calls
   */
  async function checkTableExists(): Promise<boolean> {
    const cacheKey = 'messaging:tableExists';

    // Check unified cache first
    const cached = cache.get<boolean>(cacheKey);
    if (cached !== null) {
      return cached;
    }

    try {
      const { error: tableCheckError } = await supabase
        .from('user_messages')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      // If the table doesn't exist, return false
      if (tableCheckError &&
          (tableCheckError.code === 'PGRST200' ||
           tableCheckError.message?.includes("relation \"user_messages\" does not exist"))) {

        console.warn('The user_messages table does not exist yet. Please run the database fix from the admin panel.');

        // Cache the negative result for 1 hour
        cache.set(cacheKey, false, {
          ttl: 60 * 60 * 1000, // 1 hour
          storage: 'memory'
        });

        return false;
      }

      // Cache the positive result for 1 hour
      cache.set(cacheKey, true, {
        ttl: 60 * 60 * 1000, // 1 hour
        storage: 'memory'
      });

      return true;
    } catch (checkError) {
      console.error('Error checking user_messages table:', checkError);
      return false;
    }
  }

  // Function to initialize the messaging store
  async function initializeMessaging() {
    const { data: { user } } = await supabase.auth.getUser();

    if (user) {
      console.log('Initializing messaging for user:', user.id);

      // Check if the table exists first
      const tableExists = await checkTableExists();
      if (!tableExists) {
        console.log('Messaging table does not exist, skipping initialization');
        return;
      }

      // Get initial unread count
      try {
        const count = await getUnreadCount();
        console.log('Initial unread message count:', count);
        unreadCount.value = count;
      } catch (error) {
        console.error('Error getting initial unread count:', error);
      }

      // Clean up any existing subscription
      if (messageSubscription) {
        realtime.unsubscribe(messageSubscription);
      }

      // Subscribe to new messages using unified real-time service
      messageSubscription = realtime.subscribe(
        {
          table: 'user_messages',
          event: 'INSERT',
          filter: `or(recipient_id.eq.${user.id},sender_id.eq.${user.id})`
        },
        async (payload) => {
          console.log('New message activity detected:', payload);

          // Get the new message data
          const newMessage = payload.new as Message;

          // Determine if this is an incoming or outgoing message
          const isIncoming = newMessage.recipient_id === user.id;
          const isOutgoing = newMessage.sender_id === user.id;
          const otherUserId = isIncoming ? newMessage.sender_id : newMessage.recipient_id;

          try {
            // Get user details for the other party from personal_details table
            const { data: userData, error: userError } = await supabase
              .from('personal_details')
              .select('user_id, first_name, last_name, profile_name, email')
              .eq('user_id', otherUserId)
              .single();

            if (userError) {
              console.error('Error fetching user details for message:', userError);
              // Return null instead of fallback data - let UI handle the error state
              return null;
            }

            // Create user display name using universal username function
            const userName = getUniversalUsername(userData);

            // For incoming messages, show notification
            if (isIncoming) {
              // Show a notification with more details
              notifications.info({
                message: `New message from ${userName}`,
                caption: newMessage.content.length > 30 ?
                  newMessage.content.substring(0, 30) + '...' :
                  newMessage.content,
                icon: 'chat',
                color: 'red',
                timeout: 5000,
                actions: [
                  { label: 'View', color: 'white', handler: () => {
                    // Use window.location to navigate to messages
                    window.location.href = '/dashboard/messages';
                  }}
                ]
              });

              // Play notification sound (optional)
              try {
                const audio = new Audio('/notification.mp3');
                audio.play().catch(e => console.log('Could not play notification sound', e));
              } catch (e) {
                console.log('Audio notification not supported');
              }

              // Update unread count immediately
              unreadCount.value++;
            }

            // Update the messages in the current conversation if it's open
            if (messages.value[otherUserId]) {
              console.log(`Updating conversation with ${otherUserId} in real-time`);

              // Check if this message is already in our list (to avoid duplicates)
              const messageExists = messages.value[otherUserId].some(msg => msg.id === newMessage.id);

              if (!messageExists) {
                const currentMessages = [...messages.value[otherUserId]];

                // Get current user data from personal_details for consistency
                let currentUserData;
                try {
                  const { data: currentUserDetails, error: currentUserError } = await supabase
                    .from('personal_details')
                    .select('user_id, first_name, last_name, email, profile_name')
                    .eq('user_id', user.id)
                    .single();

                  if (currentUserError) {
                    console.error('Error fetching current user details:', currentUserError);
                    // Fallback to auth metadata
                    currentUserData = {
                      user_id: user.id,
                      email: user.email || '',
                      first_name: user.user_metadata?.first_name || '',
                      last_name: user.user_metadata?.last_name || '',
                      profile_name: user.user_metadata?.profile_name || ''
                    };
                  } else {
                    currentUserData = currentUserDetails;
                  }
                } catch (err) {
                  console.error('Exception fetching current user details:', err);
                  // Fallback to auth metadata
                  currentUserData = {
                    user_id: user.id,
                    email: user.email || '',
                    first_name: user.user_metadata?.first_name || '',
                    last_name: user.user_metadata?.last_name || '',
                    profile_name: user.user_metadata?.profile_name || ''
                  };
                }

                // Add the new message with user details
                currentMessages.push({
                  ...newMessage,
                  sender: isIncoming && userData ? {
                    id: userData.user_id,
                    email: userData.email || '',
                    first_name: userData.first_name || '',
                    last_name: userData.last_name || '',
                    profile_name: userData.profile_name || ''
                  } : isOutgoing ? {
                    id: currentUserData.user_id,
                    email: currentUserData.email || '',
                    first_name: currentUserData.first_name || '',
                    last_name: currentUserData.last_name || '',
                    profile_name: currentUserData.profile_name || ''
                  } : {
                    id: isIncoming ? newMessage.sender_id : user.id,
                    email: `user-${(isIncoming ? newMessage.sender_id : user.id).substring(0, 8)}@zbinnovation.com`,
                    first_name: 'ZB',
                    last_name: 'User',
                    profile_name: 'ZB User'
                  },
                  recipient: isOutgoing && userData ? {
                    id: userData.user_id,
                    email: userData.email || '',
                    first_name: userData.first_name || '',
                    last_name: userData.last_name || '',
                    profile_name: userData.profile_name || ''
                  } : isIncoming ? {
                    id: currentUserData.user_id,
                    email: currentUserData.email || '',
                    first_name: currentUserData.first_name || '',
                    last_name: currentUserData.last_name || '',
                    profile_name: currentUserData.profile_name || ''
                  } : {
                    id: isOutgoing ? newMessage.recipient_id : user.id,
                    email: `user-${(isOutgoing ? newMessage.recipient_id : user.id).substring(0, 8)}@zbinnovation.com`,
                    first_name: 'ZB',
                    last_name: 'User',
                    profile_name: 'ZB User'
                  }
                });

                // Update the messages state
                messages.value = {
                  ...messages.value,
                  [otherUserId]: currentMessages
                };
              }
            }

            // Update unread count without making a database call if possible
            if (isIncoming && !newMessage.is_read) {
              // Increment the unread count directly
              unreadCount.value++;
            }

            // We don't need to reload all conversations for every message
            // This was causing excessive database calls

            // Invalidate relevant cache entries
            cache.invalidate(`messaging:messages:${otherUserId}:*`);
            cache.invalidate('messaging:conversations:*');
            console.log('MessagingStore: Cache invalidated after real-time message');
          } catch (error) {
            console.error('Error processing real-time message update:', error);
          }
        },
        { deduplicate: true }
      );

      console.log('Messaging subscription initialized');

      // We'll let the component handle loading initial data
      // This prevents duplicate database calls when the component mounts
    }
  }

  // Function to clean up subscriptions
  function cleanupMessaging() {
    if (messageSubscription) {
      realtime.unsubscribe(messageSubscription);
      messageSubscription = null;
    }
  }

  /**
   * Send a message to another user
   *
   * @param recipientId The user ID to send the message to
   * @param content The message content
   * @returns Success status
   */
  async function sendMessage(
    recipientId: string,
    content: string
  ): Promise<boolean> {
    if (isLoading.value) return false;

    try {
      isLoading.value = true;
      error.value = null;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user || user.id === recipientId) {
        console.error('Cannot send message: Invalid user ID');
        return false;
      }

      console.log(`Sending message to user ${recipientId}`);

      // Check if the user_messages table exists using the cached function
      const tableExists = await checkTableExists();
      if (!tableExists) {
        notifications.warning('Messaging is not available yet. Please try again later.');
        return false;
      }

      // Create new message
      const { data: newMessageData, error: insertError } = await supabase
        .from('user_messages')
        .insert({
          sender_id: user.id,
          recipient_id: recipientId,
          content: content
        })
        .select('*')
        .single();

      if (insertError) {
        console.error('Error sending message:', insertError);
        error.value = insertError.message;
        notifications.error('Failed to send message: ' + insertError.message);
        return false;
      }

      // If we have the message data, add it to the local state
      if (newMessageData) {
        try {
          // Get the current messages for this conversation or initialize an empty array
          const currentMessages = messages.value[recipientId] ? [...messages.value[recipientId]] : [];

          // Check if this message is already in our list (to avoid duplicates)
          const messageExists = currentMessages.some(msg => msg.id === newMessageData.id);

          if (!messageExists) {
            // Get recipient details from personal_details table
            let recipientData;
            try {
              const { data, error } = await supabase
                .from('personal_details')
                .select('user_id, first_name, last_name, email, profile_name')
                .eq('user_id', recipientId)
                .single();

              if (error) {
                console.error('Error fetching recipient details:', error);
                // Set to null instead of fallback data
                recipientData = null;
              } else {
                recipientData = data;
              }
            } catch (err) {
              console.error('Exception fetching recipient details:', err);
              // Set to null instead of fallback data
              recipientData = null;
            }

            // Get sender data from personal_details table for consistency
            let senderData;
            try {
              const { data: currentUserData, error: senderError } = await supabase
                .from('personal_details')
                .select('user_id, first_name, last_name, email, profile_name')
                .eq('user_id', user.id)
                .single();

              if (senderError) {
                console.error('Error fetching sender details:', senderError);
                // Fallback to auth metadata if personal_details not available
                senderData = {
                  user_id: user.id,
                  email: user.email || '',
                  first_name: user.user_metadata?.first_name || '',
                  last_name: user.user_metadata?.last_name || '',
                  profile_name: user.user_metadata?.profile_name || ''
                };
              } else {
                senderData = currentUserData;
              }
            } catch (err) {
              console.error('Exception fetching sender details:', err);
              // Fallback to auth metadata
              senderData = {
                user_id: user.id,
                email: user.email || '',
                first_name: user.user_metadata?.first_name || '',
                last_name: user.user_metadata?.last_name || '',
                profile_name: user.user_metadata?.profile_name || ''
              };
            }

            // Add the new message with complete sender and recipient info
            currentMessages.push({
              ...newMessageData,
              sender: {
                id: senderData.user_id,
                email: senderData.email || '',
                first_name: senderData.first_name || '',
                last_name: senderData.last_name || '',
                profile_name: senderData.profile_name || ''
              },
              recipient: recipientData ? {
                id: recipientData.user_id,
                email: recipientData.email || '',
                first_name: recipientData.first_name || '',
                last_name: recipientData.last_name || '',
                profile_name: recipientData.profile_name || ''
              } : {
                id: recipientId,
                email: `user-${recipientId.substring(0, 8)}@zbinnovation.com`,
                first_name: 'ZB',
                last_name: 'User',
                profile_name: 'ZB User'
              }
            });

            // Update the messages state
            messages.value = {
              ...messages.value,
              [recipientId]: currentMessages
            };

            console.log('Added new message to local state:', newMessageData.id);
          } else {
            console.log('Message already exists in local state, skipping duplicate:', newMessageData.id);
          }
        } catch (localStateError) {
          console.error('Error updating local message state:', localStateError);
          // Even if local state update fails, the message was sent successfully to the database
        }
      }

      // Track the activity (don't await to avoid blocking if there's an issue)
      try {
        activityService.trackActivity('send_message', {
          recipient_id: recipientId
        }).catch(err => {
          console.warn('Failed to track message activity, but message was sent:', err);
        });
      } catch (activityError) {
        console.warn('Error tracking message activity, but message was sent:', activityError);
        // Continue even if activity tracking fails
      }

      // Don't reload all conversations after sending a message
      // This was causing circular database calls
      // Instead, update the local state directly if needed
      try {
        // If we have the conversation in local state, update it with the new message
        if (conversations.value.length > 0) {
          const existingConversation = conversations.value.find(
            conv => conv.other_user_id === recipientId
          );

          if (existingConversation) {
            // Update the existing conversation with the new message content
            existingConversation.content = content;
            existingConversation.updated_at = new Date().toISOString();

            // Move this conversation to the top of the list (most recent)
            const updatedConversations = [
              existingConversation,
              ...conversations.value.filter(conv => conv.other_user_id !== recipientId)
            ];

            conversations.value = updatedConversations;
            console.log('Updated local conversation state after sending message');
          } else {
            // This is a new conversation, but we don't need to reload all conversations
            // The real-time subscription will handle updating the UI
            console.log('New conversation started, will be updated via real-time subscription');
          }
        }
      } catch (updateError) {
        console.error('Error updating local conversation state:', updateError);
        // Continue even if local state update fails
      }

      // Invalidate relevant cache entries after successful message send
      cache.invalidate(`messaging:messages:${recipientId}:*`);
      cache.invalidate('messaging:conversations:*');
      console.log('MessagingStore: Cache invalidated after sending message');

      return true;
    } catch (error: any) {
      console.error('Error in sendMessage:', error);
      notifications.error('Failed to send message. Please try again.');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Get messages between the current user and another user
   *
   * @param userId The other user's ID
   * @param limit Maximum number of messages to return
   * @param page Page number for pagination
   * @returns Array of messages
   */
  async function loadMessages(
    userId: string,
    limit: number = 20,
    page: number = 1
  ): Promise<Message[]> {
    const cacheKey = `messaging:messages:${userId}:${limit}:${page}`;

    // Check unified cache first
    const cached = cache.get<Message[]>(cacheKey);
    if (cached) {
      console.log(`MessagingStore: Using cached messages for user ${userId} (page: ${page})`);
      return cached;
    }

    try {
      isLoading.value = true;
      error.value = null;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot get messages: User not authenticated');
        return [];
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      console.log(`Fetching messages with user ${userId} (page: ${page}, limit: ${limit})`);

      // Check if the user_messages table exists using the cached function
      const tableExists = await checkTableExists();
      if (!tableExists) {
        return [];
      }

      // Get messages between the current user and the other user
      const { data, error: messagesError } = await supabase
        .from('user_messages')
        .select('*')
        .or(
          `and(sender_id.eq.${user.id},recipient_id.eq.${userId}),` +
          `and(sender_id.eq.${userId},recipient_id.eq.${user.id})`
        )
        .order('created_at', { ascending: true }) // Changed to ascending for chronological order
        .range(from, to);

      // If successful, fetch user details separately
      if (data && !messagesError) {
        // Get unique user IDs from the messages
        const userIds = [...new Set([
          ...data.map(msg => msg.sender_id),
          ...data.map(msg => msg.recipient_id)
        ])];

        // Fetch user details from personal_details table
        let userData = [];
        try {
          const { data, error } = await supabase
            .from('personal_details')
            .select('user_id, email, first_name, last_name, profile_name')
            .in('user_id', userIds);

          if (error) {
            console.error('Error fetching user details for messages:', error);
            // Set to empty array instead of fallback data
            userData = [];
          } else {
            userData = data;
          }
        } catch (err) {
          console.error('Exception fetching user details for messages:', err);
          // Set to empty array instead of fallback data
          userData = [];
        }

        if (userData && userData.length > 0) {
          // Map user details to messages
          const messagesWithUsers = data.map(message => {
            const senderData = userData.find(u => u.user_id === message.sender_id);
            const recipientData = userData.find(u => u.user_id === message.recipient_id);

            return {
              ...message,
              sender: senderData ? {
                id: senderData.user_id,
                email: senderData.email,
                first_name: senderData.first_name,
                last_name: senderData.last_name,
                profile_name: senderData.profile_name
              } : {
                id: message.sender_id,
                email: `user-${message.sender_id.substring(0, 8)}@zbinnovation.com`,
                first_name: 'ZB',
                last_name: 'User',
                profile_name: 'ZB User'
              },
              recipient: recipientData ? {
                id: recipientData.user_id,
                email: recipientData.email,
                first_name: recipientData.first_name,
                last_name: recipientData.last_name,
                profile_name: recipientData.profile_name
              } : {
                id: message.recipient_id,
                email: `user-${message.recipient_id.substring(0, 8)}@zbinnovation.com`,
                first_name: 'ZB',
                last_name: 'User',
                profile_name: 'ZB User'
              }
            };
          });

          // Cache the messages with unified cache
          cache.set(cacheKey, messagesWithUsers, {
            ttl: 30 * 1000, // 30 seconds
            storage: 'memory'
          });

          // Store messages in state
          messages.value = {
            ...messages.value,
            [userId]: messagesWithUsers
          };

          console.log(`MessagingStore: Cached ${messagesWithUsers.length} messages for user ${userId}`);
          return messagesWithUsers;
        }
      }

      if (messagesError) {
        console.error('Error fetching messages:', messagesError);
        const errorMessage = messagesError.message;
        // Store the error message in the store's error state
        error.value = errorMessage;
        // Return empty array instead of throwing
        return [];
      }

      // If we reach here, we have data but no user details
      console.log('No user details found for messages');
      return data || [];
    } catch (error: any) {
      console.error('Error in loadMessages:', error);
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Load all conversations for the current user
   *
   * @param limit Maximum number of conversations to return
   * @param page Page number for pagination
   * @returns Array of conversations
   */
  async function loadConversations(
    limit: number = 10,
    page: number = 1
  ): Promise<Conversation[]> {
    const cacheKey = `messaging:conversations:${limit}:${page}`;

    // Check unified cache first
    const cached = cache.get<Conversation[]>(cacheKey);
    if (cached) {
      console.log(`MessagingStore: Using cached conversations (page: ${page})`);
      return cached;
    }

    try {
      isLoading.value = true;
      error.value = null;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot get conversations: User not authenticated');
        return [];
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      console.log(`Fetching conversations (page: ${page}, limit: ${limit})`);

      // Check if the user_messages table exists using the cached function
      const tableExists = await checkTableExists();
      if (!tableExists) {
        return [];
      }

      // Get the latest message for each conversation
      let latestMessages;
      let conversationsError;

      try {
        // Try to use the RPC function first
        const result = await supabase.rpc(
          'get_latest_messages_by_conversation',
          { current_user_id: user.id, msg_limit: limit, msg_offset: from }
        );

        latestMessages = result.data;
        conversationsError = result.error;

        // If there's an error with the RPC function, use the fallback query
        if (conversationsError) {
          console.log('RPC function error, using fallback query:', conversationsError.message);

          // Get all messages where the current user is either sender or recipient
          const { data, error } = await supabase
            .from('user_messages')
            .select('*')
            .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
            .order('created_at', { ascending: false });

          if (error) {
            console.error('Fallback query error:', error);
            conversationsError = error;
          } else if (!data || data.length === 0) {
            console.log('No messages found for user');
            latestMessages = [];
            conversationsError = null;
          } else {
            // Group messages by conversation (other user)
            const conversationMap = new Map();

            data.forEach(message => {
              const otherUserId = message.sender_id === user.id ? message.recipient_id : message.sender_id;

              // If we haven't seen this conversation yet, or this message is newer
              if (!conversationMap.has(otherUserId) ||
                  new Date(message.created_at) > new Date(conversationMap.get(otherUserId).created_at)) {
                conversationMap.set(otherUserId, {
                  ...message,
                  other_user_id: otherUserId
                });
              }
            });

            // Convert map to array and apply pagination
            latestMessages = Array.from(conversationMap.values())
              .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
              .slice(from, from + limit);

            conversationsError = null;
            console.log(`Found ${latestMessages.length} conversations using fallback query`);
          }
        } else {
          console.log(`Found ${latestMessages?.length || 0} conversations using RPC function`);
        }
      } catch (rpcError) {
        console.error('Error in conversation loading:', rpcError);
        latestMessages = [];
        conversationsError = rpcError;
      }

      if (conversationsError || !latestMessages) {
        console.error('Error fetching conversations:', conversationsError);
        const errorMessage = conversationsError?.message || 'Failed to load conversations';
        // Store the error message in the store's error state
        error.value = errorMessage;
        // Return empty array instead of throwing
        return [];
      }

      // Get unique user IDs from the conversations
      const otherUserIds = latestMessages.map(msg => msg.other_user_id);

      if (otherUserIds.length === 0) {
        conversations.value = [];
        return [];
      }

      // Fetch user details from personal_details table
      let userData = [];
      try {
        const { data, error } = await supabase
          .from('personal_details')
          .select('user_id, email, first_name, last_name, profile_name')
          .in('user_id', otherUserIds);

        if (error) {
          console.error('Error fetching user details for conversations:', error);
          // Set to empty array instead of fallback data
          userData = [];
        } else {
          userData = data;
        }
      } catch (err) {
        console.error('Exception fetching user details for conversations:', err);
        // Set to empty array instead of fallback data
        userData = [];
      }
      // Map user details to conversations
      const conversationsWithUsers = latestMessages.map(conversation => {
        const otherUserData = userData?.find(u => u.user_id === conversation.other_user_id);

        return {
          ...conversation,
          other_user: otherUserData ? {
            id: otherUserData.user_id,
            email: otherUserData.email,
            first_name: otherUserData.first_name,
            last_name: otherUserData.last_name,
            profile_name: otherUserData.profile_name
          } : {
            id: conversation.other_user_id,
            email: `user-${conversation.other_user_id.substring(0, 8)}@zbinnovation.com`,
            first_name: 'ZB',
            last_name: 'User',
            profile_name: 'ZB User'
          }
        };
      });

      // Cache the conversations with unified cache
      cache.set(cacheKey, conversationsWithUsers, {
        ttl: 60 * 1000, // 1 minute
        storage: 'memory'
      });

      // Store conversations in state
      conversations.value = conversationsWithUsers;

      console.log(`MessagingStore: Cached ${conversationsWithUsers.length} conversations`);
      return conversationsWithUsers;
    } catch (error: any) {
      console.error('Error in loadConversations:', error);
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Get unread message count for the current user
   *
   * @returns Number of unread messages
   */
  async function getUnreadCount(): Promise<number> {
    // Generate a unique operation key for this specific call
    const operationKey = `getUnreadCount`;

    // Check if we should retry this operation
    if (!shouldRetry(operationKey, 3)) {
      console.warn(`Skipping getUnreadCount due to too many failed attempts`);
      return 0;
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot get unread count: User not authenticated');
        return 0;
      }

      // Check if the user_messages table exists using the cached function
      const tableExists = await checkTableExists();
      if (!tableExists) {
        return 0;
      }

      // Try to use the RPC function first
      try {
        const { data, error } = await supabase.rpc(
          'get_unread_message_count',
          { user_id: user.id }
        );

        if (!error && data !== null) {
          return data;
        }
      } catch (rpcError) {
        console.warn('RPC function not found, using fallback query', rpcError);
      }

      // Fallback to direct query if RPC fails
      const { count, error } = await supabase
        .from('user_messages')
        .select('id', { count: 'exact', head: true })
        .eq('recipient_id', user.id)
        .eq('is_read', false);

      if (error) {
        console.error('Error fetching unread count:', error);
        throw error;
      }

      return count || 0;
    } catch (error) {
      console.error('Error in getUnreadCount:', error);
      return 0;
    }
  }

  /**
   * Mark a message as read
   *
   * @param messageId The ID of the message to mark as read
   * @returns Success status
   */
  async function markMessageAsRead(messageId: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot mark message as read: User not authenticated');
        return false;
      }

      // Update the message
      const { error } = await supabase
        .from('user_messages')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('id', messageId)
        .eq('recipient_id', user.id);

      if (error) {
        console.error('Error marking message as read:', error);
        return false;
      }

      // Update unread count
      getUnreadCount().then(count => {
        unreadCount.value = count;
      });

      return true;
    } catch (error) {
      console.error('Error in markMessageAsRead:', error);
      return false;
    }
  }

  /**
   * Mark all messages from a specific sender as read
   *
   * @param senderId The ID of the sender whose messages to mark as read
   * @returns Success status
   */
  async function markAllMessagesAsRead(senderId: string): Promise<boolean> {
    // Generate a unique operation key for this specific call
    const operationKey = `markAllMessagesAsRead_${senderId}`;

    // Check if we should retry this operation
    if (!shouldRetry(operationKey, 3)) {
      console.warn(`Skipping markAllMessagesAsRead for sender ${senderId} due to too many failed attempts`);
      return false;
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot mark messages as read: User not authenticated');
        return false;
      }

      console.log(`Marking all messages from ${senderId} as read`);

      // Update all unread messages from this sender
      const { data, error } = await supabase
        .from('user_messages')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('sender_id', senderId)
        .eq('recipient_id', user.id)
        .eq('is_read', false)
        .select('*');

      if (error) {
        console.error('Error marking messages as read:', error);
        return false;
      }

      console.log(`Marked ${data?.length || 0} messages as read`);

      // If we have the conversation in local state, update it
      if (messages.value[senderId]) {
        const updatedMessages = messages.value[senderId].map(msg => {
          if (msg.sender_id === senderId && msg.recipient_id === user.id && !msg.is_read) {
            return { ...msg, is_read: true };
          }
          return msg;
        });

        messages.value = {
          ...messages.value,
          [senderId]: updatedMessages
        };
      }

      // Update unread count directly without making another database call
      // If we know how many messages were marked as read, we can just subtract
      if (data && data.length > 0) {
        // If we have the current count, subtract the number of messages marked as read
        if (unreadCount.value >= data.length) {
          unreadCount.value -= data.length;
        } else {
          // If the count is off, just set it to 0
          unreadCount.value = 0;
        }
      }

      // We don't need to reload all conversations just to update read status
      // This was causing excessive database calls

      return true;
    } catch (error) {
      console.error('Error in markAllMessagesAsRead:', error);
      return false;
    }
  }

  // Return store methods and state
  return {
    // State
    isLoading,
    unreadCount,
    conversations,
    messages,
    error,

    // Lifecycle
    initializeMessaging,
    cleanupMessaging,

    // Actions
    sendMessage,
    loadMessages,
    loadConversations,
    getUnreadCount,
    markMessageAsRead,
    markAllMessagesAsRead
  };
});