export type ProfileType =
  | 'innovator'
  | 'investor'
  | 'mentor'
  | 'professional'
  | 'industry_expert'
  | 'academic_student'
  | 'academic_institution'
  | 'organisation'

export function getProfileTableName(profileType: ProfileType): string {
  return `${profileType}_profiles`
}

export function getProfilePKColumn(profileType: ProfileType): string {
  switch (profileType) {
    case 'innovator':
      return 'id'
    case 'investor':
      return 'investor_profile_id'
    case 'mentor':
      return 'mentor_profile_id'
    case 'professional':
      return 'professional_profile_id'
    case 'industry_expert':
      return 'industry_expert_profile_id'
    case 'academic_student':
      return 'academic_student_profile_id'
    case 'academic_institution':
      return 'academic_institution_profile_id'
    case 'organisation':
      return 'organisation_profile_id'
    default:
      return 'id'
  }
}

