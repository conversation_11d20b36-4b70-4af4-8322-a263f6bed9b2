/**
 * Mentorship request email template
 */

import { extractNameFromEmail } from '../../../shared/email-templates/template-utils.ts'

/**
 * Generates a mentorship request notification email
 * @param email The mentor's email
 * @param menteeName The name of the person requesting mentorship
 * @param requestTitle The title of the mentorship request
 * @param requestMessage The message from the mentee
 * @param requestUrl The URL to view the request
 * @param firstName Optional first name of the mentor
 * @returns HTML and subject for the email
 */
export function generateMentorshipRequestEmail(
  email: string,
  menteeName: string,
  requestTitle: string,
  requestMessage: string,
  requestUrl: string,
  firstName?: string
): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined;
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,';

  const subject = `New mentorship request from ${menteeName}`;

  // Truncate message if too long
  const displayMessage = requestMessage.length > 200 ? requestMessage.substring(0, 200) + '...' : requestMessage;

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="ZB Innovation Hub" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        You have received a new mentorship request from <strong>${menteeName}</strong> on ZB Innovation Hub!
      </p>

      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 24px 0;">
        <h3 style="color: #0D8A3E; margin: 0 0 12px 0; font-size: 18px;">${requestTitle}</h3>
        <p style="margin: 0; line-height: 1.5; color: #333;">
          "${displayMessage}"
        </p>
      </div>

      <div style="text-align: center; margin: 32px 0;">
        <a href="${requestUrl}" style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
          View Request & Respond
        </a>
      </div>

      <div style="background-color: #e8f5e8; padding: 16px; border-radius: 6px; margin: 24px 0;">
        <p style="margin: 0; font-size: 14px; color: #0D8A3E;">
          <strong>💡 Tip:</strong> Responding promptly helps build strong mentorship relationships and shows your commitment to supporting the innovation community.
        </p>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5; color: #666;">
        You can accept, decline, or request more information about this mentorship opportunity. 
        The request will automatically expire in 7 days if no response is provided.
      </p>

      <div style="border-top: 1px solid #eee; padding-top: 24px; margin-top: 32px;">
        <p style="margin-bottom: 8px; line-height: 1.5;">
          Best regards,<br>
          The ZB Innovation Hub Team
        </p>
      </div>

      <div style="margin-top: 24px; padding: 16px; background-color: #f8f9fa; border-radius: 6px; font-size: 12px; color: #666;">
        <p style="margin: 0; text-align: center;">
          © ${new Date().getFullYear()} ZB Innovation Hub. All rights reserved.<br>
          This email was sent to ${email}
        </p>
        <p style="margin: 8px 0 0 0; text-align: center;">
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Manage Email Preferences</a> | 
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Unsubscribe</a>
        </p>
      </div>
    </div>
  `;

  return { html, subject };
}
