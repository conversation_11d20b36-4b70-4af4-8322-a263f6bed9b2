/**
 * Mentorship event announcement email template
 */

import { extractNameFromEmail } from '../../../shared/email-templates/template-utils.ts'

/**
 * Generates a mentorship event announcement email
 * @param email The recipient's email
 * @param mentorName The name of the mentor hosting the event
 * @param eventTitle The title of the event
 * @param eventDescription The description of the event
 * @param eventDate The formatted date of the event
 * @param eventTime The formatted time of the event
 * @param eventUrl The URL to view and register for the event
 * @param maxParticipants The maximum number of participants
 * @param currentParticipants The current number of registered participants
 * @param firstName Optional first name of the recipient
 * @returns HTML and subject for the email
 */
export function generateMentorshipEventAnnouncementEmail(
  email: string,
  mentorName: string,
  eventTitle: string,
  eventDescription: string,
  eventDate: string,
  eventTime: string,
  eventUrl: string,
  maxParticipants: number,
  currentParticipants: number,
  firstName?: string
): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined;
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,';

  const subject = `New mentorship event: ${eventTitle}`;
  const spotsRemaining = maxParticipants - currentParticipants;
  const isAlmostFull = spotsRemaining <= 3;

  // Truncate description if too long
  const displayDescription = eventDescription.length > 300 ? eventDescription.substring(0, 300) + '...' : eventDescription;

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="ZB Innovation Hub" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <div style="text-align: center; margin: 24px 0;">
        <div style="background-color: #0D8A3E; color: white; padding: 16px; border-radius: 8px; display: inline-block;">
          <h2 style="margin: 0; font-size: 24px;">🎯 New Mentorship Event</h2>
        </div>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        <strong>${mentorName}</strong> is hosting a new mentorship event that might interest you!
      </p>

      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 24px 0;">
        <h3 style="color: #0D8A3E; margin: 0 0 16px 0; font-size: 20px;">${eventTitle}</h3>
        
        <p style="margin: 0 0 16px 0; line-height: 1.5; color: #333;">
          ${displayDescription}
        </p>
        
        <div style="margin-bottom: 12px;">
          <strong>👨‍🏫 Hosted by:</strong> ${mentorName}
        </div>
        
        <div style="margin-bottom: 12px;">
          <strong>📅 Date:</strong> ${eventDate}
        </div>
        
        <div style="margin-bottom: 12px;">
          <strong>🕐 Time:</strong> ${eventTime}
        </div>
        
        <div style="margin-bottom: 12px;">
          <strong>👥 Capacity:</strong> ${currentParticipants}/${maxParticipants} registered
          ${isAlmostFull ? ' <span style="color: #dc3545; font-weight: bold;">(Almost Full!)</span>' : ''}
        </div>
      </div>

      <div style="text-align: center; margin: 32px 0;">
        <a href="${eventUrl}" style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
          ${spotsRemaining > 0 ? 'Register Now' : 'Join Waitlist'}
        </a>
      </div>

      ${isAlmostFull ? `
        <div style="background-color: #fff3cd; padding: 16px; border-radius: 6px; margin: 24px 0;">
          <p style="margin: 0; font-size: 14px; color: #856404;">
            <strong>⚡ Limited Spots!</strong> Only ${spotsRemaining} spots remaining. Register quickly to secure your place in this valuable learning opportunity.
          </p>
        </div>
      ` : `
        <div style="background-color: #e8f5e8; padding: 16px; border-radius: 6px; margin: 24px 0;">
          <p style="margin: 0; font-size: 14px; color: #0D8A3E;">
            <strong>🌟 Why Attend?</strong> Mentorship events are great opportunities to learn from experienced professionals, network with peers, and accelerate your growth in the innovation ecosystem.
          </p>
        </div>
      `}

      <p style="margin-bottom: 16px; line-height: 1.5; color: #666;">
        Don't miss this opportunity to learn from ${mentorName} and connect with other ambitious individuals in our community.
      </p>

      <div style="border-top: 1px solid #eee; padding-top: 24px; margin-top: 32px;">
        <p style="margin-bottom: 8px; line-height: 1.5;">
          Best regards,<br>
          The ZB Innovation Hub Team
        </p>
      </div>

      <div style="margin-top: 24px; padding: 16px; background-color: #f8f9fa; border-radius: 6px; font-size: 12px; color: #666;">
        <p style="margin: 0; text-align: center;">
          © ${new Date().getFullYear()} ZB Innovation Hub. All rights reserved.<br>
          This email was sent to ${email}
        </p>
        <p style="margin: 8px 0 0 0; text-align: center;">
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Manage Email Preferences</a> | 
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Unsubscribe</a>
        </p>
      </div>
    </div>
  `;

  return { html, subject };
}
