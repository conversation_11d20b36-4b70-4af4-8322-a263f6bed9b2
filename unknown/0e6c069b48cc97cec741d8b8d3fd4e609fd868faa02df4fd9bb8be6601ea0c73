/**
 * Mentorship request response email template
 */

import { extractNameFromEmail } from '../../../shared/email-templates/template-utils.ts'

/**
 * Generates a mentorship request response notification email
 * @param email The mentee's email
 * @param mentorName The name of the mentor who responded
 * @param requestTitle The title of the original request
 * @param responseStatus The response status (accepted/declined)
 * @param mentorResponse The mentor's response message
 * @param dashboardUrl The URL to the mentee's dashboard
 * @param firstName Optional first name of the mentee
 * @returns HTML and subject for the email
 */
export function generateMentorshipRequestResponseEmail(
  email: string,
  mentorName: string,
  requestTitle: string,
  responseStatus: 'accepted' | 'declined',
  mentorResponse: string,
  dashboardUrl: string,
  firstName?: string
): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined;
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,';

  const isAccepted = responseStatus === 'accepted';
  const subject = `${mentorName} ${isAccepted ? 'accepted' : 'declined'} your mentorship request`;
  const statusColor = isAccepted ? '#0D8A3E' : '#dc3545';
  const statusIcon = isAccepted ? '✅' : '❌';
  const statusText = isAccepted ? 'Accepted' : 'Declined';

  // Truncate response if too long
  const displayResponse = mentorResponse.length > 200 ? mentorResponse.substring(0, 200) + '...' : mentorResponse;

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="ZB Innovation Hub" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <div style="text-align: center; margin: 24px 0;">
        <div style="background-color: ${statusColor}; color: white; padding: 16px; border-radius: 8px; display: inline-block;">
          <h2 style="margin: 0; font-size: 24px;">${statusIcon} Request ${statusText}</h2>
        </div>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        <strong>${mentorName}</strong> has ${responseStatus} your mentorship request for "<strong>${requestTitle}</strong>".
      </p>

      ${mentorResponse ? `
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 24px 0;">
          <h3 style="color: #333; margin: 0 0 12px 0; font-size: 16px;">Message from ${mentorName}:</h3>
          <p style="margin: 0; line-height: 1.5; color: #333;">
            "${displayResponse}"
          </p>
        </div>
      ` : ''}

      <div style="text-align: center; margin: 32px 0;">
        <a href="${dashboardUrl}" style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
          ${isAccepted ? 'Schedule Your Session' : 'View Dashboard'}
        </a>
      </div>

      ${isAccepted ? `
        <div style="background-color: #e8f5e8; padding: 16px; border-radius: 6px; margin: 24px 0;">
          <p style="margin: 0; font-size: 14px; color: #0D8A3E;">
            <strong>🎉 Congratulations!</strong> Your mentorship journey begins now. Check your dashboard to schedule your first session and start building a valuable mentoring relationship.
          </p>
        </div>
      ` : `
        <div style="background-color: #fff3cd; padding: 16px; border-radius: 6px; margin: 24px 0;">
          <p style="margin: 0; font-size: 14px; color: #856404;">
            <strong>💪 Don't give up!</strong> There are many other mentors in our community. Browse mentor profiles and find someone who's the right fit for your goals.
          </p>
        </div>
      `}

      <div style="border-top: 1px solid #eee; padding-top: 24px; margin-top: 32px;">
        <p style="margin-bottom: 8px; line-height: 1.5;">
          Best regards,<br>
          The ZB Innovation Hub Team
        </p>
      </div>

      <div style="margin-top: 24px; padding: 16px; background-color: #f8f9fa; border-radius: 6px; font-size: 12px; color: #666;">
        <p style="margin: 0; text-align: center;">
          © ${new Date().getFullYear()} ZB Innovation Hub. All rights reserved.<br>
          This email was sent to ${email}
        </p>
        <p style="margin: 8px 0 0 0; text-align: center;">
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Manage Email Preferences</a> | 
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Unsubscribe</a>
        </p>
      </div>
    </div>
  `;

  return { html, subject };
}
