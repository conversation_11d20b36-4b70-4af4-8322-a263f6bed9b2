/**
 * Post Types
 *
 * This file contains type definitions for the post system that match the Supabase database schema.
 */

// Base database types that match the Supabase schema
export interface PostBase {
  id: number;
  user_id?: string;
  created_by_admin?: boolean;
  post_type: string;
  sub_type: string;
  content?: string;
  media_urls?: any;
  tags?: string[];
  created_at?: string;
  updated_at?: string;
  status: string;
  title?: string;
  slug?: string;
  excerpt?: string;
  featured_image?: string;
  comments_count: number;
  likes_count: number;
}

// Extended type that includes author information from the posts_with_authors view
export interface PostWithAuthor extends PostBase {
  first_name?: string;
  last_name?: string;
  email?: string;
  avatar_url?: string;

  // Database fields from the posts table
  image_url?: string;
  sub_category?: string;
  category?: string;
  visibility?: string;
  is_featured?: boolean;
  is_automated?: boolean;

  // Blog-specific fields
  blog_title?: string;
  blog_category?: string;
  blog_full_content?: string;

  // Event-specific fields
  event_title?: string;
  event_type?: string;
  event_theme?: string;
  event_start_datetime?: string;
  event_end_datetime?: string;
  event_location?: string;
  event_registration_url?: string;
  event_organizer?: string;
  event_date?: string;

  // Announcement-specific fields
  announcement_priority?: number;
  is_pinned?: boolean;

  // Marketplace-specific fields
  price?: string;
  location?: string;

  // Opportunity-specific fields
  opportunity_type?: string;
  opportunity_deadline?: string;
  application_url?: string;

  // Automated post fields
  related_entity_id?: string;
  related_entity_type?: string;
  activity_type?: string;
}

// Frontend model with camelCase properties and UI-specific fields
export interface Post {
  id: number;
  userId?: string;
  createdByAdmin?: boolean;
  postType: string;
  subType: string;
  content?: string;
  mediaUrls?: any;
  tags?: string[];
  createdAt?: string;
  updatedAt?: string;
  status: string;
  title?: string;
  slug?: string;
  excerpt?: string;
  featuredImage?: string;
  image?: string; // Added for backward compatibility with form components
  commentsCount: number;
  likesCount: number;

  // Database post type (admin, platform, automated, user)
  post_type?: string;

  // Author information derived from posts_with_authors view
  author?: string;
  authorEmail?: string;
  authorId?: string;
  authorAvatar?: string;

  // Blog-specific fields
  blogTitle?: string;
  blogCategory?: string;
  blogFullContent?: string;

  // Event-specific fields
  eventTitle?: string;
  eventType?: string;
  eventTheme?: string;
  eventStartDatetime?: string;
  eventEndDatetime?: string;
  eventLocation?: string;
  eventRegistrationUrl?: string;
  eventOrganizer?: string;

  // Announcement-specific fields
  announcementPriority?: number;
  isPinned?: boolean;

  // Automated post fields
  relatedEntityId?: string;
  relatedEntityType?: string;
  activityType?: string;

  // Marketplace-specific fields
  price?: string | number;
  location?: string;
  category?: string;
  listingType?: string;
  priceType?: string;
  condition?: string;
  duration?: string;
  contactInfo?: string;

  // Opportunity-specific fields
  deadline?: string;
  applicationUrl?: string;
  opportunityType?: string;

  // UI-specific properties
  isLiked?: boolean;
  isFeatured?: boolean;
  visibility?: string;
}

// Specialized post types
export interface BlogPost extends Post {
  postType: 'BLOG';
  blogTitle: string;
  blogCategory: string;
  blogFullContent?: string;
}

export interface EventPost extends Post {
  postType: 'EVENT';
  eventTitle: string;
  eventType: string;
  eventTheme?: string;
  eventStartDatetime: string;
  eventEndDatetime?: string;
  eventLocation?: string;
  eventRegistrationUrl?: string;
  eventOrganizer?: string;
}

export interface AnnouncementPost extends Post {
  postType: 'ANNOUNCEMENT';
  announcementPriority?: number;
}

export interface AutomatedPost extends Post {
  postType: 'AUTOMATED';
  relatedEntityId: string;
  relatedEntityType: string;
}

export interface GeneralPost extends Post {
  postType: 'GENERAL';
}

// Filter interface for querying posts
export interface PostFilter {
  searchQuery?: string;
  postTypes?: string[];
  subTypes?: string[];
  dateRange?: 'all' | 'today' | 'week' | 'month';
  tags?: string[];
  includeInBlog?: boolean; // Flag to include posts in the blog section
  // Pagination parameters
  page?: number;
  limit?: number;
  cursor?: string | null;
  // Mode for pagination behavior
  paginationMode?: 'replace' | 'append'; // 'replace' for normal fetch, 'append' for infinite scroll
}

// Comment interface
export interface Comment {
  id: number;
  post_id: number;
  user_id: string;
  content: string;
  created_at: string;
  updated_at?: string;
  likes_count: number;

  // From comments_with_authors view
  first_name?: string;
  last_name?: string;
  email?: string;

  // Frontend properties
  author?: string;
  authorAvatar?: string;
  isLiked?: boolean;
}

// Like interface
export interface Like {
  id: number;
  post_id: number;
  user_id: string;
  created_at: string;
}

// Helper function to process image URLs to ensure they're properly formatted
function processImageUrl(url?: string): string {
  if (!url) return '';

  // Reduce console logging - only log when there are actual issues
  // if (process.env.NODE_ENV === 'development') {
  //   console.log('Processing image URL:', url);
  // }

  // If it's already a full URL (starts with http or https), return it as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    // If it's a Supabase URL but has the wrong format, fix it
    if (url.includes('dpicnvisvxpmgjtbeicf.supabase.co') &&
        url.includes('imagefiles/') &&
        !url.includes('storage/v1/object/public/imagefiles')) {

      // Extract the file path after 'imagefiles/'
      const match = url.match(/imagefiles\/(.+)$/);
      if (match && match[1]) {
        return `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${match[1]}`;
      }
    }
    return url;
  }

  // If it's a Supabase storage URL that doesn't have the full path
  if (url.includes('imagefiles/') && !url.includes('storage/v1/object/public')) {
    // Extract the file path after 'imagefiles/'
    const match = url.match(/imagefiles\/(.+)$/);
    if (match && match[1]) {
      return `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${match[1]}`;
    }
    // If no match, use the whole path
    return `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/${url}`;
  }

  // If it's a relative path without the bucket name
  if (!url.includes('imagefiles/') && !url.startsWith('/')) {
    // Assume it's in the imagefiles bucket
    return `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${url}`;
  }

  // For any other case, return the URL as is
  return url;
}

// Helper function to determine the author name based on post type
function determineAuthorName(post: PostWithAuthor): string {
  // Check if post_type exists and is not null/undefined
  if (!post.post_type) {
    // If post_type is missing, assume it's a user post
    console.log('Post type missing, assuming user post');
    return post.first_name && post.last_name
      ? `${post.first_name} ${post.last_name}`
      : post.email?.split('@')[0] || 'Anonymous';
  }

  // Check post_type to determine how to display the author
  switch (post.post_type.toLowerCase()) {
    case 'admin':
      return 'Admin';
    case 'automated':
      return 'System';
    case 'platform':
      // For platform posts, check if there's a user_id
      // If there is, it's a user-created post, so show the username
      if (post.user_id) {
        return post.first_name && post.last_name
          ? `${post.first_name} ${post.last_name}`
          : post.email?.split('@')[0] || 'Anonymous';
      }
      return 'Platform';
    default:
      // For any other post type, assume it's a user post
      console.log('Unknown post type:', post.post_type, 'assuming user post');
      return post.first_name && post.last_name
        ? `${post.first_name} ${post.last_name}`
        : post.email?.split('@')[0] || 'Anonymous';
  }
}

// Helper function to map from database model to frontend model
export function mapPostFromDatabase(post: PostWithAuthor): Post {
  // Determine the frontend post type based on specialized fields or sub_type
  let frontendPostType = (post.sub_type || 'general').toLowerCase();

  // Reduced logging - only log when there are mapping issues
  // if (process.env.NODE_ENV === 'development') {
  //   console.log('Mapping post from database:', post);
  // }

  // Determine the frontend post type based on multiple indicators

  // First check if post_type is explicitly set to a known type
  if (post.post_type) {
    const postTypeLC = post.post_type.toLowerCase();
    if (postTypeLC === 'event') {
      frontendPostType = 'event';
    } else if (postTypeLC === 'blog') {
      frontendPostType = 'blog';
    } else if (postTypeLC === 'marketplace') {
      frontendPostType = 'marketplace';
    }
  }

  // Then check sub_type if post_type didn't give us a clear answer
  if (frontendPostType === 'general' && post.sub_type) {
    const subTypeLC = post.sub_type.toLowerCase();
    if (subTypeLC === 'event') {
      frontendPostType = 'event';
    } else if (subTypeLC === 'blog') {
      frontendPostType = 'blog';
    } else if (subTypeLC === 'marketplace' || subTypeLC === 'market') {
      frontendPostType = 'marketplace';
    } else {
      // If sub_type is available, use it as the primary indicator
      frontendPostType = subTypeLC;
    }
  }

  // If still no clear type, check for specialized fields
  if (frontendPostType === 'general') {
    if (post.event_title || post.event_type || post.event_date || post.event_location || post.event_start_datetime) {
      frontendPostType = 'event';
    } else if (post.blog_title || post.blog_category) {
      frontendPostType = 'blog';
    } else if (post.related_entity_id) {
      frontendPostType = 'automated';
    } else if (post.announcement_priority !== undefined && post.announcement_priority !== null) {
      frontendPostType = 'announcement';
    } else if (post.price !== undefined || (post.location && post.price)) {
      frontendPostType = 'marketplace';
    } else if (post.opportunity_type || post.opportunity_deadline) {
      frontendPostType = 'opportunity';
    }
  }

  // Finally, check tags as a last resort
  if (frontendPostType === 'general' && post.tags && post.tags.length > 0) {
    for (const tag of post.tags) {
      const tagLC = tag.toLowerCase();
      if (tagLC === 'event') {
        frontendPostType = 'event';
        break;
      } else if (tagLC === 'blog') {
        frontendPostType = 'blog';
        break;
      } else if (tagLC === 'marketplace' || tagLC === 'market') {
        frontendPostType = 'marketplace';
        break;
      }
    }
  }

  // For blog posts, ensure consistent post_type
  if (frontendPostType === 'blog') {
    // Convert to uppercase for frontend consistency
    post.post_type = 'BLOG';
  }

  // Reduced logging - only log when there are type determination issues
  // if (process.env.NODE_ENV === 'development') {
  //   console.log('Determined frontend post type:', frontendPostType);
  // }

  // Create the base post object
  const mappedPost: Post = {
    id: post.id,
    userId: post.user_id,
    createdByAdmin: post.created_by_admin || post.post_type === 'admin',
    postType: frontendPostType === 'blog' ? 'BLOG' : frontendPostType.toUpperCase(),
    subType: post.sub_type || post.sub_category || 'general',
    content: post.content || '',
    mediaUrls: post.media_urls || null,
    tags: post.tags || [],
    createdAt: post.created_at,
    updatedAt: post.updated_at,
    status: post.status || 'published',
    title: post.title || '',
    slug: post.slug || '',
    excerpt: post.excerpt || '',
    // Process image URLs to ensure they're properly formatted (optimized to avoid duplicate processing)
    ...(() => {
      const processedImageUrl = processImageUrl(post.featured_image) || processImageUrl(post.image_url) || '';
      return {
        featuredImage: processedImageUrl,
        image: processedImageUrl // Set image property for backward compatibility
      };
    })(),
    commentsCount: post.comments_count || 0,
    likesCount: post.likes_count || 0,

    // Include the original database post_type
    post_type: post.post_type || (post.user_id ? 'platform' : undefined),

    // Author information based on post type
    author: determineAuthorName(post),
    authorEmail: post.email,
    authorId: post.user_id,
    authorAvatar: post.avatar_url || '',

    // UI-specific properties - use database value if available
    isLiked: post.user_has_liked || false
  };

  // Add specialized fields based on post type
  switch (frontendPostType) {
    case 'blog':
      mappedPost.blogTitle = post.blog_title || post.title || '';
      mappedPost.blogCategory = post.blog_category || post.category || '';
      mappedPost.blogFullContent = post.blog_full_content || post.content || '';
      break;
    case 'event':
      mappedPost.eventTitle = post.event_title || post.title || '';
      mappedPost.eventType = post.event_type || '';
      mappedPost.eventTheme = post.event_theme || '';
      mappedPost.eventStartDatetime = post.event_start_datetime || post.event_date || '';
      mappedPost.eventEndDatetime = post.event_end_datetime || '';
      mappedPost.eventLocation = post.event_location || post.location || '';
      mappedPost.eventRegistrationUrl = post.event_registration_url || post.application_url || '';
      mappedPost.eventOrganizer = post.event_organizer || '';

      // Try to parse event details from content if it's a JSON string
      if (typeof post.content === 'string' && (post.content.startsWith('{') || post.content.startsWith('['))) {
        try {
          const parsedContent = JSON.parse(post.content);
          if (parsedContent.description) {
            mappedPost.content = parsedContent.description;
          }
          if (parsedContent.eventDetails) {
            // Extract event details from the parsed content
            const eventDetails = parsedContent.eventDetails;
            if (!mappedPost.eventType && eventDetails.eventType) {
              mappedPost.eventType = eventDetails.eventType;
            }
            if (!mappedPost.eventTheme && eventDetails.eventTheme) {
              mappedPost.eventTheme = eventDetails.eventTheme;
            }
            if (!mappedPost.eventStartDatetime && eventDetails.eventDate) {
              mappedPost.eventStartDatetime = eventDetails.eventDate;
            }
            if (!mappedPost.eventLocation && eventDetails.location) {
              mappedPost.eventLocation = eventDetails.location;
            }
            if (!mappedPost.eventRegistrationUrl && eventDetails.registrationUrl) {
              mappedPost.eventRegistrationUrl = eventDetails.registrationUrl;
            }
          }
        } catch (e) {
          console.log('Failed to parse event JSON content:', e);
        }
      }
      break;
    case 'marketplace':
      mappedPost.price = post.price || '';
      mappedPost.location = post.location || '';
      mappedPost.category = post.category || '';

      // Try to parse marketplace details from content if it's a JSON string
      if (typeof post.content === 'string' && (post.content.startsWith('{') || post.content.startsWith('['))) {
        try {
          const parsedContent = JSON.parse(post.content);
          if (parsedContent.description) {
            mappedPost.content = parsedContent.description;
          }
          if (parsedContent.marketplaceDetails) {
            // Extract marketplace details from the parsed content
            const marketplaceDetails = parsedContent.marketplaceDetails;
            if (!mappedPost.price && marketplaceDetails.price !== undefined) {
              mappedPost.price = marketplaceDetails.price;
            }
            if (!mappedPost.location && marketplaceDetails.location) {
              mappedPost.location = marketplaceDetails.location;
            }
            // Add other marketplace fields
            mappedPost.listingType = marketplaceDetails.listingType || '';
            mappedPost.priceType = marketplaceDetails.priceType || '';
            mappedPost.condition = marketplaceDetails.condition || '';
            mappedPost.duration = marketplaceDetails.duration || '';
            mappedPost.contactInfo = marketplaceDetails.contactInfo || '';
          }
        } catch (e) {
          console.log('Failed to parse marketplace JSON content:', e);
        }
      }
      break;
    case 'opportunity':
      mappedPost.deadline = post.opportunity_deadline || '';
      mappedPost.applicationUrl = post.application_url || '';
      break;
    case 'automated':
      mappedPost.relatedEntityId = post.related_entity_id || '';
      mappedPost.relatedEntityType = post.related_entity_type || '';
      break;
    case 'announcement':
      mappedPost.announcementPriority = post.announcement_priority || 0;
      break;
  }

  return mappedPost;
}
