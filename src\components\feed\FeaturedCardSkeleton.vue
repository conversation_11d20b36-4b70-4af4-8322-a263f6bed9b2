<template>
  <q-card class="featured-card-skeleton">
    <div class="skeleton-container">
      <!-- Image Skeleton -->
      <div class="skeleton-image">
        <q-skeleton height="100%" />
        
        <!-- Badge Skeleton -->
        <div class="absolute-top-left q-ma-sm">
          <q-skeleton type="QChip" />
        </div>

        <!-- Content Skeleton -->
        <div class="absolute-bottom skeleton-overlay">
          <div class="skeleton-content q-pa-md">
            <!-- Title Skeleton -->
            <q-skeleton 
              type="text" 
              width="80%" 
              height="1.2rem"
              class="q-mb-xs"
            />
            
            <!-- Subtitle Skeleton -->
            <q-skeleton 
              type="text" 
              width="60%" 
              height="0.9rem"
              class="q-mb-sm"
            />
            
            <!-- Meta Skeleton -->
            <div class="row items-center">
              <q-skeleton 
                type="QIcon" 
                size="1rem"
                class="q-mr-xs"
              />
              <q-skeleton 
                type="text" 
                width="40%" 
                height="0.75rem"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
// No props or logic needed for skeleton
</script>

<style scoped>
.featured-card-skeleton {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  height: 200px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.skeleton-container {
  position: relative;
  height: 100%;
}

.skeleton-image {
  position: relative;
  height: 100%;
}

.skeleton-overlay {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.1) 0%, transparent 100%);
}

.skeleton-content {
  /* Ensure skeleton content is visible */
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .featured-card-skeleton {
    height: 180px;
  }
}
</style>
