<template>
  <q-card class="marketplace-preview-card">
    <q-card-section class="bg-secondary text-white">
      <div class="text-h6">Innovation Marketplace</div>
      <div class="text-subtitle2">Coming Soon</div>
    </q-card-section>

    <q-card-section>
      <p class="text-body1">
        Our Innovation Marketplace will connect innovators with investors, mentors, and resources.
        Be among the first to access this feature when it launches.
      </p>
      
      <div class="row q-col-gutter-md q-mt-md">
        <div class="col-12 col-md-4">
          <q-card class="feature-card bg-blue-1">
            <q-card-section class="text-center">
              <q-icon name="storefront" size="56px" color="blue-8" />
              <div class="text-h6 q-mt-sm">Project Showcase</div>
              <p class="text-body2">
                Showcase your innovative projects and gain visibility with potential investors and partners.
              </p>
            </q-card-section>
          </q-card>
        </div>
        
        <div class="col-12 col-md-4">
          <q-card class="feature-card bg-green-1">
            <q-card-section class="text-center">
              <q-icon name="handshake" size="56px" color="green-8" />
              <div class="text-h6 q-mt-sm">Smart Matching</div>
              <p class="text-body2">
                Our AI-powered matching system will connect you with the right partners for your needs.
              </p>
            </q-card-section>
          </q-card>
        </div>
        
        <div class="col-12 col-md-4">
          <q-card class="feature-card bg-orange-1">
            <q-card-section class="text-center">
              <q-icon name="insights" size="56px" color="orange-8" />
              <div class="text-h6 q-mt-sm">Market Analytics</div>
              <p class="text-body2">
                Gain insights into market trends and opportunities in your industry.
              </p>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </q-card-section>
    
    <q-card-section class="q-pt-none">
      <q-separator class="q-my-md" />
      
      <div class="text-h6 q-mb-md">Featured Projects</div>
      <p class="text-caption text-italic q-mb-md">Preview of projects that will be featured in the marketplace</p>
      
      <div class="row q-col-gutter-md">
        <div v-for="(project, index) in previewProjects" :key="index" class="col-12 col-md-6">
          <q-card class="project-card">
            <q-img :src="project.image" :ratio="16/9">
              <div class="absolute-bottom text-subtitle2 text-center bg-transparent">
                <q-badge color="secondary">{{ project.category }}</q-badge>
              </div>
            </q-img>
            <q-card-section>
              <div class="text-h6">{{ project.title }}</div>
              <div class="text-caption text-grey">{{ project.location }}</div>
              <p class="text-body2 q-mt-sm">{{ project.description }}</p>
            </q-card-section>
            <q-card-actions align="right">
              <q-btn flat color="primary" label="Coming Soon" disable />
            </q-card-actions>
          </q-card>
        </div>
      </div>
    </q-card-section>
    
    <q-card-actions align="center">
      <q-btn color="secondary" label="Join Waitlist" />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// Preview projects - will be loaded from database
const previewProjects = ref([
  // TODO: Load actual marketplace projects from database
]);
  {
    title: 'Renewable Energy Solution',
    category: 'CleanTech',
    location: 'Bulawayo, Zimbabwe',
    description: 'Affordable solar energy solutions designed for rural communities with limited access to the electrical grid.',
    image: 'https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1472&q=80'
  },
  {
    title: 'Mobile Health Platform',
    category: 'HealthTech',
    location: 'Mutare, Zimbabwe',
    description: 'Mobile application connecting patients in remote areas with healthcare professionals for telemedicine consultations.',
    image: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'
  },
  {
    title: 'Educational Technology',
    category: 'EdTech',
    location: 'Gweru, Zimbabwe',
    description: 'Digital learning platform providing quality education resources to students in underserved communities.',
    image: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1422&q=80'
  }
]);
</script>

<style scoped>
.marketplace-preview-card {
  height: 100%;
}

.feature-card, .project-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover, .project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
