<template>
  <div class="user-content-management">
    <q-card class="dashboard-card">
      <q-card-section :class="fullPage ? 'bg-primary text-white' : 'bg-grey-2'">
        <div class="row items-center justify-between">
          <div>
            <div :class="fullPage ? 'text-h6' : 'text-subtitle1 text-primary'">My Content</div>
            <div :class="fullPage ? 'text-subtitle2' : 'text-caption text-grey'">
              {{ fullPage ? 'Manage your posts, groups, and other content' : 'Recently created content' }}
            </div>
          </div>
          <div v-if="!fullPage">
            <q-btn
              flat
              color="primary"
              label="View All"
              icon-right="arrow_forward"
              to="/dashboard/content"
              size="sm"
            />
          </div>
        </div>
      </q-card-section>

      <q-tabs
        v-model="activeTab"
        dense
        class="text-grey"
        active-color="primary"
        indicator-color="primary"
        align="justify"
        narrow-indicator
      >
        <q-tab name="posts" label="Posts" icon="post_add" />
        <q-tab name="groups" label="Groups" icon="groups" />
        <q-tab name="marketplace" label="Marketplace" icon="store" />
        <q-tab v-if="fullPage" name="comments" label="Comments" icon="comment" />
      </q-tabs>

      <q-separator />

      <q-tab-panels v-model="activeTab" animated>
        <!-- Posts Tab -->
        <q-tab-panel name="posts">
          <div class="text-h6 q-mb-md">My Posts</div>

          <div v-if="loadingPosts" class="text-center q-pa-md">
            <q-spinner color="primary" size="3em" />
            <div class="q-mt-sm">Loading your posts...</div>
          </div>

          <div v-else-if="userPosts.length === 0" class="text-center q-pa-md">
            <q-icon name="post_add" size="3em" color="grey-5" />
            <div class="text-h6 q-mt-sm">No Posts Yet</div>
            <p class="text-grey-8">You haven't created any posts yet.</p>
            <q-btn
              color="primary"
              label="Create a Post"
              icon="add"
              @click="navigateToCreatePost"
            />
          </div>

          <div v-else>
            <div class="row q-col-gutter-md">
              <div v-for="post in limitedUserPosts" :key="post.id" class="col-12">
                <q-card bordered flat class="post-card">
                  <q-card-section>
                    <div class="row items-center no-wrap">
                      <div class="col">
                        <div class="text-h6">{{ post.title || 'Untitled Post' }}</div>
                        <div class="text-caption text-grey">
                          {{ formatDate(post.createdAt) }} · {{ post.postType }}
                        </div>
                      </div>
                      <div class="col-auto">
                        <q-btn flat round color="grey" icon="more_vert">
                          <q-menu>
                            <q-list style="min-width: 100px">
                              <q-item clickable v-close-popup @click="viewPost(post)">
                                <q-item-section>View</q-item-section>
                              </q-item>
                              <q-item clickable v-close-popup @click="editPost(post)">
                                <q-item-section>Edit</q-item-section>
                              </q-item>
                              <q-item clickable v-close-popup @click="confirmDeletePost(post)">
                                <q-item-section class="text-negative">Delete</q-item-section>
                              </q-item>
                            </q-list>
                          </q-menu>
                        </q-btn>
                      </div>
                    </div>
                  </q-card-section>

                  <q-card-section class="q-pt-none">
                    <div class="text-body2 ellipsis-3-lines">{{ extractContent(post.content) }}</div>
                  </q-card-section>

                  <q-card-section class="q-pt-none">
                    <div class="row items-center">
                      <q-chip v-if="post.subType" size="sm" outline color="primary">
                        {{ post.subType }}
                      </q-chip>
                      <q-space />
                      <div class="text-grey text-caption">
                        <q-icon name="thumb_up" size="xs" /> {{ post.likesCount || 0 }}
                        <q-icon name="chat_bubble_outline" size="xs" class="q-ml-sm" /> {{ post.commentsCount || 0 }}
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>

            <div v-if="!props.fullPage && userPosts.length > props.limit" class="text-center q-mt-md">
              <q-btn
                outline
                color="primary"
                label="View All Posts"
                size="sm"
                to="/dashboard/content?tab=posts"
              />
            </div>
          </div>
        </q-tab-panel>

        <!-- Groups Tab -->
        <q-tab-panel name="groups">
          <div class="text-h6 q-mb-md">My Groups</div>

          <div v-if="loadingGroups" class="text-center q-pa-md">
            <q-spinner color="primary" size="3em" />
            <div class="q-mt-sm">Loading your groups...</div>
          </div>

          <div v-else-if="userGroups.length === 0" class="text-center q-pa-md">
            <q-icon name="groups" size="3em" color="grey-5" />
            <div class="text-h6 q-mt-sm">No Groups Yet</div>
            <p class="text-grey-8">Group creation is currently disabled.</p>
            <q-btn
              color="grey"
              label="Group Creation Disabled"
              icon="lock"
              disable
            />
          </div>

          <div v-else>
            <div class="row q-col-gutter-md">
              <div v-for="group in limitedUserGroups" :key="group.id" class="col-12 col-md-6">
                <q-card bordered flat class="group-card">
                  <q-img
                    :src="group.image || 'https://placehold.co/600x200/e0e0e0/ffffff?text=Group'"
                    height="120px"
                  />
                  <q-card-section>
                    <div class="row items-center no-wrap">
                      <div class="col">
                        <div class="text-h6">{{ group.name || 'Untitled Group' }}</div>
                        <div class="text-caption text-grey">
                          {{ formatDate(group.createdAt) }} · {{ group.memberCount || 0 }} members
                        </div>
                      </div>
                      <div class="col-auto">
                        <q-btn flat round color="grey" icon="more_vert">
                          <q-menu>
                            <q-list style="min-width: 100px">
                              <q-item clickable v-close-popup @click="viewGroup(group)">
                                <q-item-section>View</q-item-section>
                              </q-item>
                              <q-item clickable v-close-popup @click="editGroup(group)" disable>
                                <q-item-section>Edit (Disabled)</q-item-section>
                              </q-item>
                              <q-item clickable v-close-popup @click="confirmDeleteGroup(group)">
                                <q-item-section class="text-negative">Delete</q-item-section>
                              </q-item>
                            </q-list>
                          </q-menu>
                        </q-btn>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>

            <div v-if="!props.fullPage && userGroups.length > props.limit" class="text-center q-mt-md">
              <q-btn
                outline
                color="primary"
                label="View All Groups"
                size="sm"
                to="/dashboard/content?tab=groups"
              />
            </div>
          </div>
        </q-tab-panel>

        <!-- Marketplace Tab -->
        <q-tab-panel name="marketplace">
          <div class="text-h6 q-mb-md">My Marketplace Listings</div>

          <div v-if="loadingMarketplace" class="text-center q-pa-md">
            <q-spinner color="primary" size="3em" />
            <div class="q-mt-sm">Loading your listings...</div>
          </div>

          <div v-else-if="userMarketplace.length === 0" class="text-center q-pa-md">
            <q-icon name="store" size="3em" color="grey-5" />
            <div class="text-h6 q-mt-sm">No Listings Yet</div>
            <p class="text-grey-8">You haven't created any marketplace listings yet.</p>
            <q-btn
              color="primary"
              label="Create a Listing"
              icon="add"
              @click="navigateToCreateMarketplace"
            />
          </div>

          <div v-else>
            <div class="row q-col-gutter-md">
              <div v-for="listing in limitedUserMarketplace" :key="listing.id" class="col-12 col-md-6 col-lg-4">
                <q-card bordered flat class="marketplace-card">
                  <q-img
                    :src="listing.image || 'https://placehold.co/600x400/e0e0e0/ffffff?text=Product'"
                    height="160px"
                  />
                  <q-card-section>
                    <div class="row items-center no-wrap">
                      <div class="col">
                        <div class="text-h6">{{ listing.title || 'Untitled Listing' }}</div>
                        <div class="text-subtitle1 text-weight-bold text-primary">
                          {{ listing.price || 'Price not specified' }}
                        </div>
                      </div>
                      <div class="col-auto">
                        <q-btn flat round color="grey" icon="more_vert">
                          <q-menu>
                            <q-list style="min-width: 100px">
                              <q-item clickable v-close-popup @click="viewMarketplaceListing(listing)">
                                <q-item-section>View</q-item-section>
                              </q-item>
                              <q-item clickable v-close-popup @click="editMarketplaceListing(listing)">
                                <q-item-section>Edit</q-item-section>
                              </q-item>
                              <q-item clickable v-close-popup @click="confirmDeleteMarketplaceListing(listing)">
                                <q-item-section class="text-negative">Delete</q-item-section>
                              </q-item>
                            </q-list>
                          </q-menu>
                        </q-btn>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>

            <div v-if="!props.fullPage && userMarketplace.length > props.limit" class="text-center q-mt-md">
              <q-btn
                outline
                color="primary"
                label="View All Listings"
                size="sm"
                to="/dashboard/content?tab=marketplace"
              />
            </div>
          </div>
        </q-tab-panel>

        <!-- Comments Tab -->
        <q-tab-panel name="comments">
          <div class="text-h6 q-mb-md">My Comments</div>

          <div v-if="loadingComments" class="text-center q-pa-md">
            <q-spinner color="primary" size="3em" />
            <div class="q-mt-sm">Loading your comments...</div>
          </div>

          <div v-else-if="userComments.length === 0" class="text-center q-pa-md">
            <q-icon name="comment" size="3em" color="grey-5" />
            <div class="text-h6 q-mt-sm">No Comments Yet</div>
            <p class="text-grey-8">You haven't made any comments yet.</p>
          </div>

          <div v-else>
            <q-list bordered separator>
              <q-item v-for="comment in userComments" :key="comment.id" clickable @click="viewCommentContext(comment)">
                <q-item-section>
                  <q-item-label lines="2">{{ comment.content }}</q-item-label>
                  <q-item-label caption>
                    On post: {{ comment.postTitle || 'Unknown post' }} · {{ formatDate(comment.createdAt) }}
                  </q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn flat round color="grey" icon="more_vert" @click.stop>
                    <q-menu>
                      <q-list style="min-width: 100px">
                        <q-item clickable v-close-popup @click.stop="editComment(comment)">
                          <q-item-section>Edit</q-item-section>
                        </q-item>
                        <q-item clickable v-close-popup @click.stop="confirmDeleteComment(comment)">
                          <q-item-section class="text-negative">Delete</q-item-section>
                        </q-item>
                      </q-list>
                    </q-menu>
                  </q-btn>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </q-card>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="showDeleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="delete" color="negative" text-color="white" />
          <span class="q-ml-sm">Are you sure you want to delete this {{ deleteItemType }}?</span>
        </q-card-section>
        <q-card-section v-if="deleteItemType === 'post'" class="q-pt-none">
          This will permanently delete the post and all associated comments.
        </q-card-section>
        <q-card-section v-else-if="deleteItemType === 'group'" class="q-pt-none">
          This will permanently delete the group and remove all members.
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Delete" color="negative" @click="confirmDelete" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore } from '../../stores/auth';
import { usePostsStore } from '../../stores/posts';
import { date } from 'quasar';
import { useCommentsService } from '../../services/commentsService';

const props = defineProps({
  fullPage: {
    type: Boolean,
    default: false
  },
  limit: {
    type: Number,
    default: 3
  },
  defaultTab: {
    type: String,
    default: 'posts'
  }
});

const router = useRouter();
const $q = useQuasar();
const authStore = useAuthStore();
const postsStore = usePostsStore();
const commentsService = useCommentsService();

// State
const activeTab = ref(props.defaultTab);
const userPosts = ref([]);
const userGroups = ref([]);
const userMarketplace = ref([]);
const userComments = ref([]);
const loadingPosts = ref(false);
const loadingGroups = ref(false);
const loadingMarketplace = ref(false);
const loadingComments = ref(false);
const showDeleteDialog = ref(false);
const deleteItemType = ref('');
const itemToDelete = ref(null);

// Computed properties for limited content
const limitedUserPosts = computed(() => {
  return props.fullPage ? userPosts.value : userPosts.value.slice(0, props.limit);
});

const limitedUserGroups = computed(() => {
  return props.fullPage ? userGroups.value : userGroups.value.slice(0, props.limit);
});

const limitedUserMarketplace = computed(() => {
  return props.fullPage ? userMarketplace.value : userMarketplace.value.slice(0, props.limit);
});

// Fetch user content on mount
onMounted(async () => {
  await fetchUserPosts();
  await fetchUserGroups();
  await fetchUserMarketplace();

  // Only fetch comments in full page mode
  if (props.fullPage) {
    await fetchUserComments();
  }
});

// Format date helper
function formatDate(dateString) {
  if (!dateString) return 'Unknown date';
  return date.formatDate(dateString, 'MMM D, YYYY');
}

// Extract content from potentially JSON string
function extractContent(content) {
  if (!content) return 'No content';

  // Check if content is a JSON string
  if (typeof content === 'string' && (content.startsWith('{') || content.startsWith('['))) {
    try {
      const parsedContent = JSON.parse(content);

      // Handle different JSON structures
      if (parsedContent.description) {
        return parsedContent.description;
      } else if (parsedContent.eventDetails) {
        return parsedContent.eventDetails;
      } else if (parsedContent.content) {
        return parsedContent.content;
      } else if (typeof parsedContent === 'object') {
        // If it's an object but doesn't have expected fields, return a stringified version
        return JSON.stringify(parsedContent).substring(0, 250) + '...';
      }
    } catch (e) {
      // If parsing fails, it's not valid JSON
      console.log('Failed to parse content as JSON:', e);
    }
  }

  // If it's not JSON or parsing failed, return the original content
  return content;
}

// Posts methods
async function fetchUserPosts() {
  loadingPosts.value = true;
  try {
    const userId = authStore.currentUser?.id;
    if (!userId) throw new Error('User not authenticated');

    await postsStore.fetchUserPosts({ userId });
    userPosts.value = postsStore.userPosts;
  } catch (error) {
    console.error('Error fetching user posts:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load your posts',
      icon: 'error'
    });
  } finally {
    loadingPosts.value = false;
  }
}

function viewPost(post) {
  router.push({ name: 'post', params: { id: post.id } });
}

function editPost(post) {
  router.push({ name: 'edit-post', params: { id: post.id } });
}

function confirmDeletePost(post) {
  deleteItemType.value = 'post';
  itemToDelete.value = post;
  showDeleteDialog.value = true;
}

// Groups methods
async function fetchUserGroups() {
  loadingGroups.value = true;
  try {
    const userId = authStore.currentUser?.id;
    if (!userId) throw new Error('User not authenticated');

    // Fetch posts with type 'group' that belong to the user
    await postsStore.fetchUserPosts({
      userId,
      postType: 'GROUP'
    });

    // Map posts to group format
    userGroups.value = postsStore.userPosts
      .filter(post => post.postType?.toLowerCase() === 'group')
      .map(post => ({
        id: post.id,
        name: post.title || '',
        description: extractContent(post.content),
        image: post.featuredImage || '',
        memberCount: post.memberCount || 0,
        createdAt: post.createdAt || '',
        updatedAt: post.updatedAt || ''
      }));
  } catch (error) {
    console.error('Error fetching user groups:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load your groups',
      icon: 'error'
    });
  } finally {
    loadingGroups.value = false;
  }
}

function viewGroup(group) {
  router.push({ name: 'group', params: { id: group.id } });
}

function editGroup(group) {
  // Show notification that group editing is disabled
  $q.notify({
    color: 'warning',
    message: 'Group editing is currently disabled',
    icon: 'lock',
    position: 'top',
    timeout: 3000
  });

  // No navigation - group editing is disabled
}

function confirmDeleteGroup(group) {
  deleteItemType.value = 'group';
  itemToDelete.value = group;
  showDeleteDialog.value = true;
}

// Marketplace methods
async function fetchUserMarketplace() {
  loadingMarketplace.value = true;
  try {
    const userId = authStore.currentUser?.id;
    if (!userId) throw new Error('User not authenticated');

    console.log('Fetching marketplace listings for user');

    // Fetch posts with sub_type 'marketplace' that belong to the user
    await postsStore.fetchUserPosts({
      userId,
      postType: 'MARKETPLACE' // This will be translated to sub_type='marketplace' in the store
    });

    console.log('Fetched user posts:', postsStore.userPosts);

    // Map posts to marketplace format - no need to filter as the query already did that
    userMarketplace.value = postsStore.userPosts.map(post => ({
      id: post.id,
      title: post.title || '',
      description: extractContent(post.content),
      image: post.featuredImage || '',
      price: formatPrice(post.price),
      category: post.category || '',
      location: post.location || '',
      createdAt: post.createdAt || '',
      updatedAt: post.updatedAt || ''
    }));

    console.log('Mapped marketplace listings:', userMarketplace.value);
  } catch (error) {
    console.error('Error fetching user marketplace listings:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load your marketplace listings',
      icon: 'error'
    });
  } finally {
    loadingMarketplace.value = false;
  }
}

function formatPrice(price: string | number | null | undefined): string {
  if (!price && price !== 0) return '';

  // If it's already a formatted string with currency symbol, return as is
  if (typeof price === 'string' && (price.includes('$') || price.includes('USD') || price.includes('ZWL'))) {
    return price;
  }

  // Convert to number if it's a string
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;

  // If it's 0 or free, return "Free"
  if (numPrice === 0) return 'Free';

  // If it's not a valid number, return the original value as string
  if (isNaN(numPrice)) return String(price);

  // Format as currency (assuming USD for now, could be made configurable)
  return `$${numPrice.toLocaleString()}`;
}

function viewMarketplaceListing(listing) {
  router.push({ name: 'post-details', params: { id: listing.id } });
}

function editMarketplaceListing(listing) {
  router.push({
    path: '/virtual-community',
    query: {
      tab: 'marketplace',
      action: 'edit',
      id: listing.id
    }
  });
}

function confirmDeleteMarketplaceListing(listing) {
  deleteItemType.value = 'marketplace listing';
  itemToDelete.value = listing;
  showDeleteDialog.value = true;
}

// Comments methods
async function fetchUserComments() {
  loadingComments.value = true;
  try {
    const userId = authStore.currentUser?.id;
    if (!userId) throw new Error('User not authenticated');

    console.log('Fetching comments for user:', userId);

    // Use comments service instead of direct Supabase calls
    const comments = await commentsService.getUserComments(userId);

    console.log('Fetched user comments:', comments);

    // Map the service response to our frontend model
    userComments.value = comments.map(comment => ({
      id: comment.id,
      postId: comment.post_id,
      postTitle: comment.post_title || 'Unknown post',
      content: comment.content,
      createdAt: comment.created_at,
      updatedAt: comment.updated_at
    }));

    console.log('Mapped user comments:', userComments.value);
  } catch (error) {
    console.error('Error fetching user comments:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load your comments',
      icon: 'error'
    });
  } finally {
    loadingComments.value = false;
  }
}

function viewCommentContext(comment) {
  router.push({ name: 'post', params: { id: comment.postId } });
}

async function editComment(comment) {
  // Show a dialog to edit the comment
  $q.dialog({
    title: 'Edit Comment',
    message: 'Update your comment:',
    prompt: {
      model: comment.content,
      type: 'textarea'
    },
    cancel: true,
    persistent: true
  }).onOk(async (newContent) => {
    try {
      // Update the comment in the database
      const { error } = await supabase
        .from('comments')
        .update({ content: newContent })
        .eq('id', comment.id);

      if (error) throw error;

      // Update the local state
      const index = userComments.value.findIndex(c => c.id === comment.id);
      if (index !== -1) {
        userComments.value[index].content = newContent;
      }

      $q.notify({
        color: 'positive',
        message: 'Comment updated successfully',
        icon: 'check'
      });
    } catch (error) {
      console.error('Error updating comment:', error);
      $q.notify({
        color: 'negative',
        message: 'Failed to update comment',
        icon: 'error'
      });
    }
  });
}

function confirmDeleteComment(comment) {
  deleteItemType.value = 'comment';
  itemToDelete.value = comment;
  showDeleteDialog.value = true;
}

// Navigation methods
function navigateToCreatePost() {
  router.push({
    path: '/virtual-community',
    query: {
      tab: 'feed',
      action: 'create'
    }
  });
}

function navigateToCreateGroup() {
  // Show notification that group creation is disabled
  $q.notify({
    color: 'warning',
    message: 'Group creation is currently disabled',
    icon: 'lock',
    position: 'top',
    timeout: 3000
  });

  // No navigation - group creation is disabled
}

function navigateToCreateMarketplace() {
  router.push({
    path: '/virtual-community',
    query: {
      tab: 'marketplace',
      action: 'create'
    }
  });
}

// Delete confirmation
async function confirmDelete() {
  try {
    if (!itemToDelete.value) return;

    const id = itemToDelete.value.id;

    switch (deleteItemType.value) {
      case 'post':
        await postsStore.deletePost(id);
        await fetchUserPosts();
        break;
      case 'group':
        await postsStore.deletePost(id);
        await fetchUserGroups();
        break;
      case 'marketplace listing':
        await postsStore.deletePost(id);
        await fetchUserMarketplace();
        break;
      case 'comment':
        // Delete the comment using comments service
        await commentsService.deleteComment(id);

        // Refresh comments
        await fetchUserComments();
        break;
    }

    $q.notify({
      color: 'positive',
      message: `${deleteItemType.value} deleted successfully`,
      icon: 'check'
    });
  } catch (error) {
    console.error(`Error deleting ${deleteItemType.value}:`, error);
    $q.notify({
      color: 'negative',
      message: `Failed to delete ${deleteItemType.value}`,
      icon: 'error'
    });
  }
}
</script>

<style scoped>
.ellipsis-3-lines {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-card, .group-card, .marketplace-card {
  transition: all 0.2s ease;
}

.post-card:hover, .group-card:hover, .marketplace-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}
</style>
