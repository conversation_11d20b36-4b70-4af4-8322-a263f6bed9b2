<template>
  <section class="features-section q-py-xl">
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <div class="text-center">
            <h2 class="text-h3 text-weight-light q-mb-md">Platform Features</h2>
            <p class="text-body1 q-mb-xl text-center">
              Explore the powerful tools and features that make our innovation ecosystem unique and effective.
            </p>
          </div>
          
          <div class="features-grid">
            <div
              v-for="(feature, index) in features"
              :key="index"
              class="feature-card scroll-item"
              :style="{ animationDelay: `${index * 0.1}s` }"
            >
              <div class="feature-icon-container">
                <q-icon :name="feature.icon" size="3rem" color="primary" class="feature-icon" />
                <div class="icon-bg"></div>
              </div>
              <h3 class="text-h6 q-mt-md q-mb-sm">{{ feature.title }}</h3>
              <p class="text-body2">{{ feature.description }}</p>
              <div class="feature-details">
                <div class="feature-detail-item" v-for="(detail, detailIndex) in feature.details" :key="detailIndex">
                  <q-icon name="check_circle" color="green" size="xs" class="q-mr-xs" />
                  <span>{{ detail }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="text-center q-mt-xl">
            <q-btn
              color="primary"
              label="Explore All Features"
              no-caps
              class="q-px-xl"
              unelevated
              rounded
              @click="scrollToSignup"
            />
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>
    
    <!-- Animated Background Elements -->
    <div class="animated-bg-element element-1"></div>
    <div class="animated-bg-element element-2"></div>
    <div class="animated-bg-element element-3"></div>
  </section>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// Removed activeFeature state for hover effects

// Initialize scroll animations for feature cards
onMounted(() => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate')
          observer.unobserve(entry.target)
        }
      })
    },
    {
      threshold: 0.2,
      rootMargin: '0px 0px -50px 0px'
    }
  )

  // Observe all feature cards
  const featureCards = document.querySelectorAll('.scroll-item')
  featureCards.forEach((card) => {
    observer.observe(card)
  })
});

const features = ref([
  {
    icon: 'people',
    title: 'Community Networking',
    description: 'Connect with entrepreneurs, investors, mentors, and industry experts in a vibrant community.',
    details: [
      'Advanced profile matching',
      'Direct messaging system',
      'Interest-based groups',
      'Virtual networking events'
    ]
  },
  {
    icon: 'school',
    title: 'Learning Resources',
    description: 'Access a wealth of educational content and training opportunities to grow your skills.',
    details: [
      'Expert-led workshops',
      'On-demand video courses',
      'Downloadable resources',
      'Personalized learning paths'
    ]
  },
  {
    icon: 'handshake',
    title: 'Collaboration Tools',
    description: 'Find partners and collaborate effectively on innovative projects and initiatives.',
    details: [
      'Project management tools',
      'Shared document workspaces',
      'Virtual meeting rooms',
      'Co-creation boards'
    ]
  },
  {
    icon: 'trending_up',
    title: 'Growth Opportunities',
    description: 'Discover funding, mentorship, and resources to accelerate your innovation journey.',
    details: [
      'Investor matching',
      'Grant application support',
      'Pitch deck reviews',
      'Growth strategy consulting'
    ]
  },
  {
    icon: 'event',
    title: 'Events & Programs',
    description: 'Participate in events, workshops, and programs designed to foster innovation.',
    details: [
      'Hackathons & challenges',
      'Networking mixers',
      'Industry showcases',
      'Accelerator programs'
    ]
  },
  {
    icon: 'business',
    title: 'Physical Hub Access',
    description: 'Utilize our state-of-the-art physical facilities for meetings, work, and events.',
    details: [
      'Co-working spaces',
      'Meeting rooms',
      'Event venues',
      'Innovation labs'
    ]
  }
]);

const scrollToSignup = () => {
  const signupSection = document.getElementById('signup-section');
  if (signupSection) {
    signupSection.scrollIntoView({ behavior: 'smooth' });
  }
};
</script>

<style scoped>
.features-section {
  background-color: white;
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.container {
  width: 100%;
  max-width: 1400px;
  position: relative;
  z-index: 2;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin: 40px 0;
}

.feature-card {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 100%;
  z-index: 1;
}

/* Scroll animation styles for feature cards */
.scroll-item {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-item.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Removed hover active state */

.feature-icon-container {
  position: relative;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon {
  position: relative;
  z-index: 2;
}

.icon-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(13, 138, 62, 0.1);
  border-radius: 50%;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

/* Removed hover active icon background */

.feature-details {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px dashed rgba(13, 138, 62, 0.2);
  animation: fadeIn 0.3s ease;
}

.feature-detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.animated-bg-element {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(13, 138, 62, 0.1) 0%, rgba(116, 181, 36, 0.1) 100%);
  z-index: 1;
}

.element-1 {
  width: 300px;
  height: 300px;
  top: -100px;
  right: -100px;
  animation: float 15s infinite ease-in-out;
}

.element-2 {
  width: 200px;
  height: 200px;
  bottom: -50px;
  left: -50px;
  animation: float 12s infinite ease-in-out reverse;
}

.element-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  right: 10%;
  animation: float 10s infinite ease-in-out 2s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(10deg);
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@media (max-width: 1023px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 599px) {
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-card {
    padding: 20px;
  }
}
</style>
