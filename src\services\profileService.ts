/**
 * Unified Profile Service
 *
 * This service centralizes all profile operations and provides a clean separation
 * between handling new and existing profiles. It handles the different table naming
 * conventions (singular vs plural) and provides a consistent API for all components.
 */

import { supabase } from '../lib/supabase'
import { ref } from 'vue'

// Define the base profile interface
export interface BaseProfile {
  id?: string
  user_id: string
  email: string
  first_name: string | null
  last_name: string | null
  profile_name: string | null
  profile_state: 'DRAFT' | 'IN_PROGRESS' | 'PENDING_APPROVAL' | 'ACTIVE' | 'DISABLED' | 'DECLINED'
  profile_type: string | null
  profile_visibility: 'public' // All profiles are now public when they reach 50% completion
  role: string
  is_verified: boolean
  profile_completion: number
  created_at: string
  updated_at: string
  last_login_at: string | null
  phone_country_code: string | null
  phone_number: string | null
  gender: string | null
  bio: string | null
  hear_about_us: string[] | null
  tags?: string[] | null
  is_public?: boolean
  completion_percentage?: number
}

// Define the personal details interface
export interface PersonalDetails {
  first_name: string | null
  last_name: string | null
  email: string
  phone_country_code?: string | null
  phone_number?: string | null
  gender?: string | null
  bio?: string | null
}

// Define the specialized profile interface
export interface SpecializedProfile {
  user_id: string
  profile_name: string
  is_public: boolean
  completion_percentage: number
  bio?: string
  [key: string]: any
}

// Map of profile types to their table names (all plural for consistency)
const PROFILE_TABLE_MAP = {
  'innovator': 'innovator_profiles',
  'academic_student': 'academic_student_profiles',
  'academic_institution': 'academic_institution_profiles',
  'organisation': 'organisation_profiles',
  'mentor': 'mentor_profiles',
  'investor': 'investor_profiles',
  'professional': 'professional_profiles',
  'industry_expert': 'industry_expert_profiles'
}

/**
 * Get the correct table name for a profile type
 *
 * @param profileType The profile type
 * @returns The correct table name (always plural)
 */
function getTableName(profileType: string): string {
  if (!profileType) {
    console.error('getTableName called with empty profile type');
    return 'unknown_profiles';
  }

  // Normalize the profile type (trim and lowercase)
  const normalizedType = profileType.trim().toLowerCase();

  // First check if we have a specific mapping for this profile type
  if (PROFILE_TABLE_MAP[normalizedType]) {
    return PROFILE_TABLE_MAP[normalizedType];
  }

  // For unknown profile types, use plural naming convention
  return normalizedType.endsWith('_profiles') ? normalizedType : `${normalizedType}_profiles`;
}

/**
 * Sanitize profile data to handle NULL values
 *
 * @param profile The profile data to sanitize
 * @returns The sanitized profile data
 */
function sanitizeProfileData(profile: any): any {
  if (!profile) return {}

  // Create a copy of the profile to avoid modifying the original
  const sanitized = { ...profile }

  // Convert null values to empty strings for string fields
  const stringFields = [
    'first_name', 'last_name', 'profile_name', 'phone_country_code',
    'phone_number', 'gender', 'bio'
  ]

  stringFields.forEach(field => {
    if (sanitized[field] === null) {
      sanitized[field] = ''
    }
  })

  // Ensure numeric fields are numbers
  const numericFields = ['profile_completion', 'completion_percentage']
  numericFields.forEach(field => {
    if (sanitized[field] === null || sanitized[field] === undefined) {
      sanitized[field] = 0
    } else if (typeof sanitized[field] !== 'number') {
      sanitized[field] = parseFloat(sanitized[field]) || 0
    }
  })

  // Ensure boolean fields are booleans
  const booleanFields = ['is_verified', 'is_public']
  booleanFields.forEach(field => {
    if (sanitized[field] === null || sanitized[field] === undefined) {
      sanitized[field] = false
    } else if (typeof sanitized[field] !== 'boolean') {
      sanitized[field] = sanitized[field] === 'true' || sanitized[field] === true
    }
  })

  return sanitized
}

/**
 * Create a new profile service instance
 */
export function useProfileService() {
  // State
  const loading = ref(false)
  const error = ref<string | null>(null)

  /**
   * Load a user's base profile from the personal_details table
   *
   * @param userId The user ID
   * @returns The base profile data
   */
  async function loadBaseProfile(userId: string): Promise<BaseProfile | null> {
    if (!userId) {
      console.error('loadBaseProfile: No user ID provided')
      return null
    }

    try {
      console.log(`Loading base profile for user ${userId}`)
      const { data, error: fetchError } = await supabase
        .from('personal_details')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (fetchError) {
        console.error('Error loading base profile:', fetchError)
        throw fetchError
      }

      if (!data) {
        console.log(`No base profile found for user ${userId}`)
        return null
      }

      console.log(`Base profile loaded for user ${userId}:`, data)
      return sanitizeProfileData(data) as BaseProfile
    } catch (err) {
      console.error('Error in loadBaseProfile:', err)
      throw err
    }
  }

  /**
   * Load all profiles for a user
   *
   * @param userId The user ID
   * @returns An array of base profiles
   */
  async function loadUserProfiles(userId: string): Promise<BaseProfile[]> {
    if (!userId) {
      console.error('loadUserProfiles: No user ID provided')
      return []
    }

    try {
      console.log(`Loading all profiles for user ${userId}`)
      const { data, error: fetchError } = await supabase
        .from('personal_details')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (fetchError) {
        console.error('Error loading user profiles:', fetchError)
        throw fetchError
      }

      console.log(`Loaded ${data?.length || 0} profiles for user ${userId}`)
      return (data || []).map(profile => sanitizeProfileData(profile)) as BaseProfile[]
    } catch (err) {
      console.error('Error in loadUserProfiles:', err)
      throw err
    }
  }



  /**
   * Load specialized profile data for a user
   *
   * @param userId The user ID
   * @param profileType The profile type
   * @returns The specialized profile data
   */
  async function loadSpecializedProfile(userId: string, profileType: string): Promise<SpecializedProfile | null> {
    if (!userId || !profileType) {
      console.error('loadSpecializedProfile: Missing required parameters', { userId, profileType })
      return null
    }

    try {
      let tableName = getTableName(profileType)
      console.log(`Loading specialized profile from ${tableName} for user ${userId}`)

      const { data, error: fetchError } = await supabase
        .from(tableName)
        .select('*')
        .eq('user_id', userId)
        .single()

      if (fetchError) {
        // If the error is "not found", return null
        if (fetchError.code === 'PGRST116') {
          console.log(`No specialized profile found in ${tableName} for user ${userId}`)
          return null
        }

        console.error(`Error loading specialized profile from ${tableName}:`, fetchError)
        throw fetchError
      }

      if (!data) {
        console.log(`No specialized profile found in ${tableName} for user ${userId}`)
        return null
      }

      console.log(`Specialized profile loaded from ${tableName} for user ${userId}:`, data)
      return sanitizeProfileData(data) as SpecializedProfile
    } catch (err) {
      console.error('Error in loadSpecializedProfile:', err)
      throw err
    }
  }

  /**
   * Create a new base profile
   *
   * @param userId The user ID
   * @param email The user's email
   * @param profileType The profile type
   * @param profileName The profile name
   * @returns The created base profile
   */
  async function createBaseProfile(
    userId: string,
    email: string,
    profileType: string | null = null,
    profileName: string = 'My Profile'
  ): Promise<BaseProfile | null> {
    if (!userId || !email) {
      console.error('createBaseProfile: Missing required parameters', { userId, email })
      return null
    }

    try {
      console.log(`Creating new base profile for user ${userId}`)

      // Check if a profile already exists
      const existingProfile = await loadBaseProfile(userId)

      if (existingProfile) {
        console.log(`Base profile already exists for user ${userId}, updating instead`)

        // Update the existing profile
        const { data, error: updateError } = await supabase
          .from('personal_details')
          .update({
            profile_name: profileName,
            profile_type: profileType,
            profile_state: 'IN_PROGRESS',
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId)
          .select()

        if (updateError) {
          console.error('Error updating existing base profile:', updateError)
          throw updateError
        }

        console.log(`Base profile updated for user ${userId}:`, data?.[0])
        return data?.[0] ? sanitizeProfileData(data[0]) as BaseProfile : existingProfile
      }

      // Create a new profile
      const newProfile = {
        user_id: userId,
        email,
        profile_name: profileName,
        profile_type: profileType,
        profile_state: 'IN_PROGRESS',
        profile_visibility: 'public', // All profiles are now public when they reach 50% completion
        role: 'early_access',
        is_verified: false,
        profile_completion: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      console.log(`Creating new base profile:`, newProfile)
      const { data, error: insertError } = await supabase
        .from('personal_details')
        .insert(newProfile)
        .select()

      if (insertError) {
        console.error('Error creating new base profile:', insertError)
        throw insertError
      }

      console.log(`New base profile created for user ${userId}:`, data?.[0])
      return data?.[0] ? sanitizeProfileData(data[0]) as BaseProfile : null
    } catch (err) {
      console.error('Error in createBaseProfile:', err)
      throw err
    }
  }

  /**
   * Create a specialized profile
   *
   * @param userId The user ID
   * @param profileType The profile type
   * @param profileName The profile name
   * @param additionalData Additional data for the specialized profile
   * @returns The created specialized profile
   */
  async function createSpecializedProfile(
    userId: string,
    profileType: string,
    profileName: string = 'My Profile',
    additionalData: Record<string, any> = {}
  ): Promise<SpecializedProfile | null> {
    if (!userId || !profileType) {
      console.error('createSpecializedProfile: Missing required parameters', { userId, profileType })
      return null
    }

    try {
      // Ensure we're using the correct table name format
      let tableName = getTableName(profileType)
      console.log(`Creating specialized profile in ${tableName} for user ${userId}`)

      // Check if a specialized profile already exists
      const existingProfile = await loadSpecializedProfile(userId, profileType)

      if (existingProfile) {
        console.log(`Specialized profile already exists in ${tableName} for user ${userId}, updating instead`)

        // Update the existing profile with additional data
        const updateData = {
          ...additionalData,
          profile_name: profileName,
          updated_at: new Date().toISOString()
        }

        const { data, error: updateError } = await supabase
          .from(tableName)
          .update(updateData)
          .eq('user_id', userId)
          .select()

        if (updateError) {
          console.error(`Error updating existing specialized profile in ${tableName}:`, updateError)
          throw updateError
        }

        console.log(`Specialized profile updated in ${tableName} for user ${userId}:`, data?.[0])
        return data?.[0] ? sanitizeProfileData(data[0]) as SpecializedProfile : existingProfile
      }

      // Create a new specialized profile
      const newProfile = {
        user_id: userId,
        profile_name: profileName,
        is_public: false,
        completion_percentage: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ...additionalData
      }

      // Special handling for JSON/JSONB fields
      const jsonFields = [
        'expertise_areas', 'areas_of_expertise', 'target_markets', 'innovation_focus',
        'collaboration_types', 'preferred_partners', 'resources_offered',
        'short_term_goals', 'long_term_goals', 'current_challenges',
        'looking_for', 'collaboration_interests', 'sdg_alignment',
        'investment_focus', 'investment_geography', 'certifications',
        'skills', 'languages', 'preferred_stages', 'preferred_locations',
        // Academic institution specific JSON fields
        'research_areas', 'other_research_areas', 'research_centers',
        'academic_programs', 'industry_partnerships',
        // Location fields that might be JSON
        'preferred_locations',
        // Goals fields that might be JSON
        'short_term_goals', 'long_term_goals', 'current_challenges',
        'looking_for', 'collaboration_interests', 'sdg_alignment'
      ]

      // Log all JSON fields for debugging
      console.log('DEBUG: JSON fields to process for new profile:', jsonFields)

      // Process all JSON fields
      jsonFields.forEach(field => {
        if (newProfile[field] !== undefined) {
          console.log(`Processing JSON field ${field} for new profile:`, newProfile[field])

          // If it's a string, try to parse it as JSON
          if (typeof newProfile[field] === 'string') {
            try {
              newProfile[field] = JSON.parse(newProfile[field])
            } catch (e) {
              // If parsing fails, treat it as a single-item array
              newProfile[field] = [newProfile[field]]
            }
          }

          // Ensure it's an array if null or undefined
          if (newProfile[field] === null || newProfile[field] === undefined) {
            newProfile[field] = []
          }

          // If it's still not an array or object, convert it to an array
          if (!Array.isArray(newProfile[field]) && typeof newProfile[field] !== 'object') {
            newProfile[field] = [newProfile[field]]
          }

          // For academic institution profile, ensure research fields are properly formatted for database
          if (profileType === 'academic_institution' &&
              ['research_areas', 'other_research_areas', 'research_centers'].includes(field)) {
            // Store the original array for debugging
            const originalArray = [...newProfile[field]];

            // Convert to JSON string for database compatibility
            if (Array.isArray(newProfile[field])) {
              newProfile[field] = JSON.stringify(newProfile[field]);
            }

            console.log(`ProfileService: Converted ${field} for new profile:`, {
              original: originalArray,
              stringified: newProfile[field]
            });
          }

          console.log(`Processed JSON field ${field} for new profile:`, newProfile[field])
        }
      })

      console.log(`Creating new specialized profile in ${tableName}:`, newProfile)
      const { data, error: insertError } = await supabase
        .from(tableName)
        .insert(newProfile)
        .select()

      if (insertError) {
        // Check if the error is about the table not existing
        if (insertError.message && insertError.message.includes('does not exist')) {
          console.error(`Table ${tableName} does not exist. Cannot create specialized profile.`)
          throw new Error(`Profile type '${profileType}' is not fully supported yet. Your base profile was created successfully.`)
        }

        console.error(`Error creating new specialized profile in ${tableName}:`, insertError)
        throw insertError
      }

      console.log(`New specialized profile created in ${tableName} for user ${userId}:`, data?.[0])
      return data?.[0] ? sanitizeProfileData(data[0]) as SpecializedProfile : null
    } catch (err) {
      console.error('Error in createSpecializedProfile:', err)
      throw err
    }
  }

  /**
   * Update a base profile
   *
   * @param userId The user ID
   * @param profileData The profile data to update
   * @returns The updated base profile
   */
  async function updateBaseProfile(
    userId: string,
    profileData: Partial<BaseProfile>
  ): Promise<BaseProfile | null> {
    if (!userId) {
      console.error('updateBaseProfile: No user ID provided')
      return null
    }

    // Check authentication context
    const { data: { session } } = await supabase.auth.getSession()
    if (!session || !session.user) {
      console.error('updateBaseProfile: No authenticated user session found')
      throw new Error('Authentication required. Please sign in again.')
    }

    if (session.user.id !== userId) {
      console.error('updateBaseProfile: User ID mismatch', {
        sessionUserId: session.user.id,
        requestedUserId: userId
      })
      throw new Error('Unauthorized: User ID mismatch')
    }

    try {
      console.log(`Updating base profile for user ${userId}:`, profileData)

      // Add updated_at timestamp
      const updateData = {
        ...profileData,
        updated_at: new Date().toISOString()
      }

      const { data, error: updateError } = await supabase
        .from('personal_details')
        .update(updateData)
        .eq('user_id', userId)
        .select()

      if (updateError) {
        console.error('Error updating base profile:', updateError)

        // Enhanced error handling with user-friendly messages
        if (updateError.code === 'PGRST116') {
          throw new Error('You do not have permission to update this profile. Please sign in again.')
        }

        if (updateError.code === '23505') {
          throw new Error('A profile with this email already exists. Please use a different email.')
        }

        if (updateError.code === '23503') {
          throw new Error('Invalid reference data. Please refresh the page and try again.')
        }

        const friendlyMessage = updateError.message.includes('permission denied') || updateError.message.includes('RLS')
          ? 'You do not have permission to update this profile. Please sign in again.'
          : `Failed to update profile: ${updateError.message}`

        throw new Error(friendlyMessage)
      }

      console.log(`Base profile updated for user ${userId}:`, data?.[0])
      return data?.[0] ? sanitizeProfileData(data[0]) as BaseProfile : null
    } catch (err) {
      console.error('Error in updateBaseProfile:', err)

      // Ensure we always throw user-friendly errors
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('An unexpected error occurred while updating your profile. Please try again.')
      }
    }
  }

  /**
   * Update a specialized profile
   *
   * @param userId The user ID
   * @param profileType The profile type
   * @param profileData The profile data to update
   * @returns The updated specialized profile
   */
  async function updateSpecializedProfile(
    userId: string,
    profileType: string,
    profileData: Partial<SpecializedProfile>
  ): Promise<SpecializedProfile | null> {
    console.log('profileService.updateSpecializedProfile called with:', { userId, profileType, profileData });
    // Add detailed logging for debugging
    console.log('DEBUG: updateSpecializedProfile called with:', {
      userId,
      profileType,
      profileDataKeys: Object.keys(profileData)
    })
    if (!userId || !profileType) {
      console.error('updateSpecializedProfile: Missing required parameters', { userId, profileType })
      return null
    }

    // Check authentication context
    const { data: { session } } = await supabase.auth.getSession()
    if (!session || !session.user) {
      console.error('updateSpecializedProfile: No authenticated user session found')
      throw new Error('Authentication required. Please sign in again.')
    }

    if (session.user.id !== userId) {
      console.error('updateSpecializedProfile: User ID mismatch', {
        sessionUserId: session.user.id,
        requestedUserId: userId
      })
      throw new Error('Unauthorized: User ID mismatch')
    }

    console.log('DEBUG: Authentication verified for user:', session.user.id)

    try {
      const tableName = getTableName(profileType)
      console.log(`Updating specialized profile in ${tableName} for user ${userId}:`, profileData)

      // Special debug logging for organization, investor, and industry expert profiles
      if (profileType === 'organisation' || profileType === 'investor' || profileType === 'industry_expert') {
        console.log(`DEBUG: updateSpecializedProfile for ${profileType} profile`)
        console.log('DEBUG: Table name:', tableName)
        console.log('DEBUG: Profile data keys:', Object.keys(profileData))
      }

      // Check if the specialized profile exists
      const existingProfile = await loadSpecializedProfile(userId, profileType)

      // Special debug logging for organization, investor, and industry expert profiles
      if (profileType === 'organisation' || profileType === 'investor' || profileType === 'industry_expert') {
        console.log(`DEBUG: Existing ${profileType} profile found:`, existingProfile ? 'Yes' : 'No')
        if (existingProfile) {
          console.log(`DEBUG: Existing ${profileType} profile data:`, existingProfile)
        }
      }

      if (!existingProfile) {
        console.log(`No specialized profile found in ${tableName} for user ${userId}, creating instead`)
        return createSpecializedProfile(userId, profileType, profileData.profile_name, profileData)
      }

      // Add updated_at timestamp
      const updateData = {
        ...profileData,
        updated_at: new Date().toISOString()
      }

      // Special handling for JSON/JSONB fields
      const jsonFields = [
        'expertise_areas', 'areas_of_expertise', 'target_markets', 'innovation_focus',
        'collaboration_types', 'preferred_partners', 'resources_offered',
        'short_term_goals', 'long_term_goals', 'current_challenges',
        'looking_for', 'collaboration_interests', 'sdg_alignment',
        'investment_focus', 'investment_geography', 'certifications',
        'skills', 'languages', 'preferred_stages', 'preferred_locations',
        // Academic institution specific JSON fields
        'research_areas', 'other_research_areas', 'research_centers',
        'academic_programs', 'industry_partnerships',
        // Location fields that might be JSON
        'preferred_locations',
        // Goals fields that might be JSON
        'short_term_goals', 'long_term_goals', 'current_challenges',
        'looking_for', 'collaboration_interests', 'sdg_alignment'
      ]

      // Log all JSON fields for debugging
      console.log('DEBUG: JSON fields to process:', jsonFields)

      // Map personal details fields to specialized profile fields
      const personalToSpecializedFieldMap = {
        'email': 'contact_email',
        'phone_country_code': 'contact_phone_country_code',
        'phone_number': 'contact_phone_number',
        'bio': 'bio'
      }

      // Log the mapping for debugging
      console.log('DEBUG: Personal to specialized field mapping:', personalToSpecializedFieldMap)

      // Process all JSON fields with proper serialization
      jsonFields.forEach(field => {
        if (updateData[field] !== undefined) {
          console.log(`Processing JSON field ${field} before update:`, updateData[field], typeof updateData[field])

          // Handle different input types
          let processedValue = updateData[field]

          // If it's a string, try to parse it as JSON first
          if (typeof processedValue === 'string') {
            try {
              processedValue = JSON.parse(processedValue)
              console.log(`Parsed JSON string for ${field}:`, processedValue)
            } catch (e) {
              // If parsing fails, treat it as a single-item array
              processedValue = [processedValue]
              console.log(`Failed to parse JSON for ${field}, treating as single item:`, processedValue)
            }
          }

          // Handle null values
          if (processedValue === null || processedValue === undefined) {
            processedValue = []
            console.log(`Null/undefined value for ${field}, setting to empty array`)
          }

          // Handle objects that are not arrays (like form objects)
          if (typeof processedValue === 'object' && !Array.isArray(processedValue)) {
            // Check if it's a plain object with values
            if (processedValue && Object.keys(processedValue).length > 0) {
              // Convert object values to array
              processedValue = Object.values(processedValue).filter(v => v !== null && v !== undefined && v !== '')
              console.log(`Converted object to array for ${field}:`, processedValue)
            } else {
              processedValue = []
              console.log(`Empty object for ${field}, setting to empty array`)
            }
          }

          // Ensure we have an array for JSONB fields
          if (!Array.isArray(processedValue)) {
            processedValue = [processedValue]
            console.log(`Non-array value for ${field}, converting to array:`, processedValue)
          }

          // Clean the array - remove empty, null, or undefined values
          processedValue = processedValue.filter(item =>
            item !== null &&
            item !== undefined &&
            item !== '' &&
            (typeof item !== 'string' || item.trim() !== '')
          )

          // Store the processed value
          updateData[field] = processedValue
          console.log(`Final processed value for ${field}:`, updateData[field])
        }
      })

      // Load the base profile to get personal details
      const baseProfile = await loadBaseProfile(userId)
      if (baseProfile) {
        // Map personal details fields to specialized profile fields
        Object.entries(personalToSpecializedFieldMap).forEach(([personalField, specializedField]) => {
          // Only map if the specialized field is not already set and the personal field has a value
          if (updateData[specializedField] === undefined && baseProfile[personalField] !== undefined && baseProfile[personalField] !== null) {
            console.log(`Mapping personal field ${personalField} to specialized field ${specializedField}:`, baseProfile[personalField])
            updateData[specializedField] = baseProfile[personalField]
          }
        })
      }

      // Special debug logging for organization, investor, and industry expert profiles
      if (profileType === 'organisation' || profileType === 'investor' || profileType === 'industry_expert') {
        console.log(`DEBUG: About to update ${profileType} profile in database`)
        console.log('DEBUG: Table name:', tableName)
        console.log('DEBUG: Update data:', updateData)
      }

      // Log the update data for debugging
      console.log(`DEBUG: Updating ${profileType} profile with data:`, {
        tableName,
        userId,
        updateDataKeys: Object.keys(updateData),
        updateDataValues: Object.entries(updateData).map(([k, v]) => [k, typeof v === 'object' ? 'object' : v])
      })

      // CRITICAL DEBUG: Log the final data being sent to the database
      console.log('CRITICAL DEBUG - Final data being sent to database:', updateData);
      console.log('CRITICAL DEBUG - Location fields in final data:', {
        address: updateData.address,
        city: updateData.city,
        state_province: updateData.state_province,
        country: updateData.country,
        postal_code: updateData.postal_code,
        willing_to_relocate: updateData.willing_to_relocate,
        preferred_locations: updateData.preferred_locations
      });

      console.log('CRITICAL DEBUG - Goals fields in final data:', {
        short_term_goals: updateData.short_term_goals,
        long_term_goals: updateData.long_term_goals,
        current_challenges: updateData.current_challenges,
        looking_for: updateData.looking_for,
        collaboration_interests: updateData.collaboration_interests,
        sdg_alignment: updateData.sdg_alignment,
        additional_interests: updateData.additional_interests
      });

      try {
        // Direct database update with explicit fields
        const { data, error: updateError } = await supabase
          .from(tableName)
          .update({
            ...updateData,
            // Explicitly include location fields
            address: updateData.address,
            city: updateData.city,
            state_province: updateData.state_province,
            country: updateData.country,
            postal_code: updateData.postal_code,
            willing_to_relocate: updateData.willing_to_relocate,
            preferred_locations: updateData.preferred_locations,
            // Explicitly include goals fields
            short_term_goals: updateData.short_term_goals,
            long_term_goals: updateData.long_term_goals,
            current_challenges: updateData.current_challenges,
            looking_for: updateData.looking_for,
            collaboration_interests: updateData.collaboration_interests,
            sdg_alignment: updateData.sdg_alignment,
            additional_interests: updateData.additional_interests,
            // Explicitly include academic institution fields
            research_areas: updateData.research_areas,
            other_research_areas: updateData.other_research_areas,
            research_centers: updateData.research_centers,
            academic_programs: updateData.academic_programs,
            industry_partnerships: updateData.industry_partnerships
          })
          .eq('user_id', userId)
          .select()

        if (updateError) {
          console.error(`Error updating specialized profile in ${tableName}:`, updateError)

          // Enhanced error logging with context
          const errorContext = {
            tableName,
            userId,
            profileType,
            updateDataKeys: Object.keys(updateData),
            errorCode: updateError.code,
            errorMessage: updateError.message,
            errorDetails: updateError.details,
            timestamp: new Date().toISOString()
          }
          console.error('Profile update error context:', errorContext)

          // Handle specific error types with user-friendly messages
          if (updateError.code === '42P01') {
            // Table does not exist
            throw new Error(`Profile type "${profileType}" is not supported yet. Please contact support.`)
          }

          if (updateError.code === '42703') {
            // Column does not exist - this will be handled in the catch block
            throw updateError
          }

          if (updateError.code === '23505') {
            // Unique constraint violation
            throw new Error('A profile with this information already exists. Please check your data.')
          }

          if (updateError.code === '23503') {
            // Foreign key constraint violation
            throw new Error('Invalid reference data. Please refresh the page and try again.')
          }

          if (updateError.code === 'PGRST116') {
            // No rows updated (likely due to RLS)
            throw new Error('You do not have permission to update this profile. Please sign in again.')
          }

          // Generic error with helpful message
          const friendlyMessage = updateError.message.includes('permission denied') || updateError.message.includes('RLS')
            ? 'You do not have permission to update this profile. Please sign in again.'
            : `Failed to update profile: ${updateError.message}`

          throw new Error(friendlyMessage)
        }

        // Log success
        console.log(`DEBUG: Successfully updated ${profileType} profile in ${tableName}`)
        return data?.[0] ? sanitizeProfileData(data[0]) as SpecializedProfile : null
      } catch (updateError) {
        console.error(`Error updating specialized profile in ${tableName}:`, updateError)

        // Handle column errors with automatic retry
        if (updateError.message && updateError.message.includes('column') && updateError.message.includes('does not exist')) {
          const columnMatch = updateError.message.match(/column "([^"]+)" does not exist/)
          const columnName = columnMatch ? columnMatch[1] : null

          if (columnName) {
            console.warn(`Column '${columnName}' does not exist in table ${tableName}, retrying without it`)

            // Remove the problematic field and retry
            const { [columnName]: _, ...cleanedData } = updateData
            console.log(`Retrying update without field '${columnName}'`)

            try {
              const { data: retryData, error: retryError } = await supabase
                .from(tableName)
                .update(cleanedData)
                .eq('user_id', userId)
                .select()

              if (retryError) {
                console.error('Retry failed with error:', retryError)
                throw new Error(`Failed to update profile after removing invalid field: ${retryError.message}`)
              }

              console.log('Profile update successful after removing invalid field')
              return retryData?.[0] ? sanitizeProfileData(retryData[0]) as SpecializedProfile : null
            } catch (retryErr) {
              console.error('Retry attempt failed:', retryErr)
              throw new Error(`Failed to update profile: ${retryErr.message}`)
            }
          }
        }

        // Re-throw with user-friendly message
        const friendlyMessage = updateError.message.includes('permission denied') || updateError.message.includes('RLS')
          ? 'You do not have permission to update this profile. Please sign in again.'
          : `Failed to update profile: ${updateError.message}`

        throw new Error(friendlyMessage)
      }
    } catch (err) {
      console.error('Error in updateSpecializedProfile:', err)
      throw err
    }
  }

  /**
   * Update profile completion percentage
   *
   * @param userId The user ID
   * @param profileType The profile type
   * @param completionPercentage The completion percentage
   * @returns True if the update was successful
   */
  async function updateProfileCompletion(
    userId: string,
    profileType: string | null,
    completionPercentage: number
  ): Promise<boolean> {
    if (!userId) {
      console.error('updateProfileCompletion: No user ID provided')
      return false
    }

    try {
      console.log(`Updating profile completion for user ${userId} to ${completionPercentage}%`)

      // Update the base profile
      const { error: baseUpdateError } = await supabase
        .from('personal_details')
        .update({
          profile_completion: completionPercentage,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (baseUpdateError) {
        console.error('Error updating base profile completion:', baseUpdateError)
        throw baseUpdateError
      }

      // If we have a profile type, update the specialized profile too
      if (profileType) {
        const tableName = getTableName(profileType)
        console.log(`Updating specialized profile completion in ${tableName} for user ${userId}`)

        const { error: specializedUpdateError } = await supabase
          .from(tableName)
          .update({
            completion_percentage: completionPercentage,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId)

        if (specializedUpdateError) {
          console.error(`Error updating specialized profile completion in ${tableName}:`, specializedUpdateError)
          // Don't throw, we still updated the base profile
        }
      }

      return true
    } catch (err) {
      console.error('Error in updateProfileCompletion:', err)
      return false
    }
  }

  /**
   * Delete a profile
   *
   * @param userId The user ID
   * @param profileType The profile type
   * @returns True if the deletion was successful
   */
  async function deleteProfile(userId: string, profileType: string | null = null): Promise<boolean> {
    if (!userId) {
      console.error('deleteProfile: No user ID provided')
      return false
    }

    try {
      console.log(`Deleting profile for user ${userId}`)

      // If we have a profile type, delete the specialized profile first
      if (profileType) {
        const tableName = getTableName(profileType)
        console.log(`Deleting specialized profile from ${tableName} for user ${userId}`)

        const { error: specializedDeleteError } = await supabase
          .from(tableName)
          .delete()
          .eq('user_id', userId)

        if (specializedDeleteError) {
          console.error(`Error deleting specialized profile from ${tableName}:`, specializedDeleteError)
          // Don't throw, we still want to try deleting the base profile
        }
      }

      // Delete the base profile
      const { error: baseDeleteError } = await supabase
        .from('personal_details')
        .delete()
        .eq('user_id', userId)

      if (baseDeleteError) {
        console.error('Error deleting base profile:', baseDeleteError)
        throw baseDeleteError
      }

      console.log(`Profile deleted for user ${userId}`)
      return true
    } catch (err) {
      console.error('Error in deleteProfile:', err)
      return false
    }
  }

  /**
   * Calculate profile completion percentage
   *
   * @param baseProfile The base profile
   * @param specializedProfile The specialized profile
   * @returns The completion percentage
   */
  function calculateProfileCompletion(
    baseProfile: BaseProfile | null,
    specializedProfile: SpecializedProfile | null = null
  ): number {
    if (!baseProfile) return 0

    // Define required fields for base profile
    const baseRequiredFields = [
      'first_name', 'last_name', 'email', 'phone_number', 'gender', 'bio'
    ]

    // Count completed base fields
    let completedBaseFields = 0
    baseRequiredFields.forEach(field => {
      if (baseProfile[field] && baseProfile[field] !== '') {
        completedBaseFields++
      }
    })

    // Calculate base completion percentage
    const baseCompletion = (completedBaseFields / baseRequiredFields.length) * 100

    // If we don't have a specialized profile, return base completion
    if (!specializedProfile || !baseProfile.profile_type) {
      return Math.round(baseCompletion)
    }

    // Define required fields for specialized profiles based on type
    const specializedRequiredFields: Record<string, string[]> = {
      'innovator': ['bio', 'website', 'linkedin'],
      'mentor': ['bio', 'expertise_areas', 'mentoring_approach'],
      'investor': ['bio', 'investment_thesis', 'investment_stage'],
      'professional': ['bio', 'expertise', 'company'],
      'industry_expert': ['bio', 'areas_of_expertise', 'industry'],
      'academic_student': ['bio', 'field_of_study', 'institution'],
      'organisation': ['bio', 'organisation_type', 'website']
    }

    // Get required fields for this profile type
    const requiredFields = specializedRequiredFields[baseProfile.profile_type] || []

    // Count completed specialized fields
    let completedSpecializedFields = 0
    requiredFields.forEach(field => {
      if (specializedProfile[field] && specializedProfile[field] !== '') {
        completedSpecializedFields++
      }
    })

    // Calculate specialized completion percentage
    const specializedCompletion = requiredFields.length > 0
      ? (completedSpecializedFields / requiredFields.length) * 100
      : 0

    // Calculate overall completion (base + specialized)
    const overallCompletion = (baseCompletion + specializedCompletion) / 2

    return Math.round(overallCompletion)
  }

  /**
   * Format a profile type for display
   *
   * @param profileType The profile type
   * @returns The formatted profile type
   */
  function formatProfileType(profileType: string | null): string {
    if (!profileType) return 'Unknown'

    return profileType
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  /**
   * Map personal details to specialized profile fields
   *
   * @param personalDetails The personal details
   * @returns An object with mapped specialized profile fields
   */
  function mapPersonalDetailsToSpecialized(personalDetails: Partial<BaseProfile>): Record<string, any> {
    console.log('DEBUG: mapPersonalDetailsToSpecialized called with:', personalDetails)

    if (!personalDetails) {
      console.log('DEBUG: No personal details provided, returning empty object')
      return {}
    }

    const mappedFields: Record<string, any> = {}

    // Define field mappings
    const fieldMappings = {
      'email': 'contact_email',
      'phone_country_code': 'contact_phone_country_code',
      'phone_number': 'contact_phone_number',
      'bio': 'bio'
    }

    console.log('DEBUG: Field mappings:', fieldMappings)

    // Map fields
    Object.entries(fieldMappings).forEach(([personalField, specializedField]) => {
      console.log(`DEBUG: Checking personal field '${personalField}':`, personalDetails[personalField])

      if (personalDetails[personalField] !== undefined && personalDetails[personalField] !== null) {
        mappedFields[specializedField] = personalDetails[personalField]
        console.log(`DEBUG: Mapped '${personalField}' to '${specializedField}':`, mappedFields[specializedField])
      } else {
        console.log(`DEBUG: Skipping '${personalField}' - value is undefined or null`)
      }
    })

    console.log('DEBUG: Final mapped fields:', mappedFields)
    return mappedFields
  }

  /**
   * Load profiles with at least 50% completion
   *
   * @param page Page number for pagination
   * @param limit Number of profiles per page
   * @param filters Additional filters to apply
   * @returns An array of profiles with at least 50% completion
   */
  async function loadPublicProfiles(
    page: number = 1,
    limit: number = 12,
    filters: Record<string, any> = {}
  ): Promise<{ profiles: BaseProfile[], hasMore: boolean }> {
    try {
      console.log('Loading public profiles')

      // Use the publicProfiles service to fetch all profiles
      const { profiles, hasMore } = await import('./api/publicProfiles')
        .then(module => module.loadPublicProfiles(page, limit, filters))
        .then(result => ({
          profiles: result.profiles.map(p => ({
            ...p,
            profile_visibility: 'public' // Ensure all profiles are marked as public
          })).map(p => module.convertToBaseProfile(p)),
          hasMore: result.hasMore
        }));

      console.log(`Loaded ${profiles.length} profiles, hasMore: ${hasMore}`)

      return {
        profiles,
        hasMore
      }
    } catch (err) {
      console.error('Error in loadPublicProfiles:', err)
      throw err
    }
  }

  /**
   * Update profile visibility - this is now a no-op since all profiles are publicly visible
   * Kept for backward compatibility
   *
   * @param userId The user ID
   * @param visibility The new visibility (ignored)
   * @returns The profile or null
   */
  async function updateProfileVisibility(
    userId: string,
    visibility: 'public' | 'private' | 'connections_only'
  ): Promise<BaseProfile | null> {
    console.log('updateProfileVisibility: This function is now a no-op as all profiles are publicly visible')

    // If we need the profile, load it
    if (userId) {
      return await loadBaseProfile(userId)
    }

    return null
  }

  return {
    // State
    loading,
    error,

    // Profile operations
    loadBaseProfile,
    loadUserProfiles,
    loadPublicProfiles,
    loadSpecializedProfile,
    createBaseProfile,
    createSpecializedProfile,
    updateBaseProfile,
    updateSpecializedProfile,
    updateProfileCompletion,
    updateProfileVisibility,
    deleteProfile,

    // Utility functions
    calculateProfileCompletion,
    formatProfileType,
    getTableName,
    mapPersonalDetailsToSpecialized
  }
}
