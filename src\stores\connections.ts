import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/lib/supabase';
import { useActivityTrackingService } from '@/services/activityTrackingService';
import { useNotificationStore } from './notifications';

export interface Connection {
  id: string;
  user_id: string;
  connected_user_id: string;
  connection_type: string;
  connection_status: string;
  connection_strength: number;
  created_at: string;
  updated_at: string;
  connected_user?: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    profile_name: string;
  };
  user?: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    profile_name: string;
  };
}

export const useConnectionsStore = defineStore('connections', () => {
  // State
  const connections = ref<Connection[]>([]);
  const connectionRequests = ref<Connection[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Event system for notifying connection status changes
  const connectionStatusChangeEvent = ref(0);

  // Activity tracking service
  const activityService = useActivityTrackingService();
  const notificationStore = useNotificationStore();

  // Getters
  const getConnections = computed(() => connections.value);
  const getConnectionRequests = computed(() => connectionRequests.value);
  const isLoading = computed(() => loading.value);
  const getError = computed(() => error.value);
  const connectionsCount = computed(() => connections.value.length);

  /**
   * Trigger connection status change event to notify all connection buttons
   */
  function triggerConnectionStatusChange() {
    connectionStatusChangeEvent.value++;
    console.log('🔄 Connection status change event triggered:', connectionStatusChangeEvent.value);
  }

  /**
   * Synchronize connection data across all views
   */
  async function synchronizeConnectionData() {
    console.log('🔄 Synchronizing connection data across all views...');

    try {
      // Refresh connections
      await fetchUserConnections();

      // Refresh connection requests
      await fetchConnectionRequests();

      // Trigger global update event
      triggerConnectionStatusChange();

      console.log('✅ Connection data synchronized successfully');
    } catch (error) {
      console.error('❌ Error synchronizing connection data:', error);
    }
  }

  /**
   * Send a connection request to another user
   */
  async function connectWithUser(userId: string, connectionType: string = 'follow'): Promise<boolean> {
    if (loading.value) return false;

    try {
      loading.value = true;
      error.value = null;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user || user.id === userId) {
        error.value = 'Cannot connect: Invalid user ID';
        return false;
      }

      // Check if connection already exists - don't use single() to avoid 406 errors
      const { data: existingConnections, error: checkError } = await supabase
        .from('user_connections')
        .select('*')
        .eq('user_id', user.id)
        .eq('connected_user_id', userId)
        .limit(1);

      if (checkError) {
        throw checkError;
      }

      const existingConnection = existingConnections && existingConnections.length > 0
        ? existingConnections[0]
        : null;

      if (existingConnection) {
        // Connection already exists, update it
        const { error: updateError } = await supabase
          .from('user_connections')
          .update({
            connection_type: connectionType,
            connection_status: 'pending',
            updated_at: new Date().toISOString()
          })
          .eq('id', existingConnection.id);

        if (updateError) throw updateError;
      } else {
        // Create new connection
        const { error: insertError } = await supabase
          .from('user_connections')
          .insert({
            user_id: user.id,
            connected_user_id: userId,
            connection_type: connectionType,
            connection_status: 'pending'
          });

        if (insertError) throw insertError;
      }

      // Track the activity
      await activityService.trackActivity('connect_request', {
        connected_user_id: userId,
        connection_type: connectionType
      });

      // Trigger connection status change event to notify all connection buttons
      triggerConnectionStatusChange();

      notificationStore.success('Connection request sent successfully');
      return true;
    } catch (err: any) {
      error.value = err.message || 'Failed to connect with user';
      notificationStore.error(error.value);
      return false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Accept a connection request
   */
  async function acceptConnection(connectionId: string): Promise<boolean> {
    if (loading.value) return false;

    try {
      loading.value = true;
      error.value = null;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        error.value = 'Cannot accept connection: User not authenticated';
        return false;
      }

      // Get the connection
      const { data: connection, error: getError } = await supabase
        .from('user_connections')
        .select('*')
        .eq('id', connectionId)
        .eq('connected_user_id', user.id)
        .eq('connection_status', 'pending')
        .single();

      if (getError) throw getError;

      if (!connection) {
        error.value = 'Connection request not found';
        return false;
      }

      // Update the connection status
      const { error: updateError } = await supabase
        .from('user_connections')
        .update({
          connection_status: 'accepted',
          updated_at: new Date().toISOString()
        })
        .eq('id', connectionId);

      if (updateError) throw updateError;

      // Track the activity
      await activityService.trackActivity('connect_accept', {
        connection_id: connectionId,
        user_id: connection.user_id
      });

      // Remove the accepted request from the local state
      connectionRequests.value = connectionRequests.value.filter(req => req.id !== connectionId);

      // Refresh connections to include the new connection
      await fetchUserConnections();

      // Trigger connection status change event to notify all connection buttons
      triggerConnectionStatusChange();

      notificationStore.success('Connection request accepted');
      return true;
    } catch (err: any) {
      error.value = err.message || 'Failed to accept connection';
      notificationStore.error(error.value);
      return false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch user connections
   */
  async function fetchUserConnections(
    userId?: string,
    status: string = 'accepted',
    limit: number = 10,
    page: number = 1
  ): Promise<Connection[]> {
    try {
      loading.value = true;
      error.value = null;

      if (!userId) {
        const { data: { user } } = await supabase.auth.getUser();
        userId = user?.id;
      }

      if (!userId) {
        error.value = 'Cannot get connections: No user ID provided';
        return [];
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      console.log(`Fetching connections for user ${userId} with status ${status}`);

      // Fetch connections where the user is the initiator (user_id)
      // We'll get the basic connection data first, then fetch profile details separately
      const { data: connectionsAsUser, error: fetchError1 } = await supabase
        .from('user_connections')
        .select('*')
        .eq('user_id', userId)
        .eq('connection_status', status)
        .order('created_at', { ascending: false });

      // Fetch connections where the user is the target (connected_user_id)
      const { data: connectionsAsTarget, error: fetchError2 } = await supabase
        .from('user_connections')
        .select('*')
        .eq('connected_user_id', userId)
        .eq('connection_status', status)
        .order('created_at', { ascending: false });

      if (fetchError1) {
        console.error('Error fetching user connections (as user):', fetchError1);
        throw fetchError1;
      }

      if (fetchError2) {
        console.error('Error fetching user connections (as target):', fetchError2);
        throw fetchError2;
      }

      // Combine both sets of connections
      const allConnections = [
        ...(connectionsAsUser || []),
        ...(connectionsAsTarget || [])
      ];

      // Sort by created_at descending
      allConnections.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      // Apply pagination
      const paginatedConnections = allConnections.slice(from, to + 1);

      // Fetch profile details for each connection with better error handling
      const connectionsWithProfiles = await Promise.allSettled(
        paginatedConnections.map(async (connection) => {
          // Determine which user ID to fetch profile for
          const profileUserId = connection.user_id === userId
            ? connection.connected_user_id
            : connection.user_id;

          try {
            // Fetch profile details from public_profiles_view
            const { data: profileData, error: profileError } = await supabase
              .from('public_profiles_view')
              .select('user_id, email, first_name, last_name, profile_name, profile_type, profile_completion')
              .eq('user_id', profileUserId)
              .maybeSingle(); // Use maybeSingle instead of single to avoid 406 errors

            if (profileError) {
              console.warn(`Could not fetch profile for user ${profileUserId}:`, profileError);
              return {
                ...connection,
                connected_user: null
              };
            }

            return {
              ...connection,
              connected_user: profileData
            };
          } catch (error) {
            console.warn(`Error fetching profile for user ${profileUserId}:`, error);
            return {
              ...connection,
              connected_user: null
            };
          }
        })
      );

      // Filter out failed requests and extract successful results
      const successfulConnections = connectionsWithProfiles
        .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
        .map(result => result.value);

      console.log(`Fetched ${successfulConnections.length} connections for user ${userId} with status ${status} (${connectionsAsUser?.length || 0} as user, ${connectionsAsTarget?.length || 0} as target)`);
      connections.value = successfulConnections;
      return connections.value;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch connections';
      return [];
    } finally {
      loading.value = false;
    }
  }

  /**
   * Get connection count for a specific user
   */
  async function getUserConnectionCount(userId?: string): Promise<number> {
    try {
      if (!userId) {
        const { data: { user } } = await supabase.auth.getUser();
        userId = user?.id;
      }

      if (!userId) {
        return 0;
      }

      console.log(`Getting connection count for user ${userId}`);

      // Count connections where the user is the initiator (user_id)
      const { count: countAsUser, error: error1 } = await supabase
        .from('user_connections')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .eq('connection_status', 'accepted');

      // Count connections where the user is the target (connected_user_id)
      const { count: countAsTarget, error: error2 } = await supabase
        .from('user_connections')
        .select('*', { count: 'exact' })
        .eq('connected_user_id', userId)
        .eq('connection_status', 'accepted');

      if (error1) {
        console.error('Error getting connection count (as user):', error1);
        return 0;
      }

      if (error2) {
        console.error('Error getting connection count (as target):', error2);
        return 0;
      }

      const totalCount = (countAsUser || 0) + (countAsTarget || 0);
      console.log(`Connection count for user ${userId}: ${totalCount} (${countAsUser || 0} as user, ${countAsTarget || 0} as target)`);
      return totalCount;
    } catch (err: any) {
      console.error('Error in getUserConnectionCount:', err);
      return 0;
    }
  }

  /**
   * Fetch connection requests
   */
  async function fetchConnectionRequests(
    limit: number = 10,
    page: number = 1
  ): Promise<Connection[]> {
    try {
      loading.value = true;
      error.value = null;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        error.value = 'Cannot get connection requests: User not authenticated';
        return [];
      }

      console.log(`Fetching connection requests for user ${user.id} (page: ${page}, limit: ${limit})`);

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      // First, get the basic connection requests without joins
      const { data: simpleRequests, error: simpleError } = await supabase
        .from('user_connections')
        .select('*')
        .eq('connected_user_id', user.id)
        .eq('connection_status', 'pending')
        .order('created_at', { ascending: false })
        .range(from, to);

      if (simpleError) {
        console.error('Error fetching connection requests:', simpleError);
        throw simpleError;
      }

      console.log('Fetched connection requests:', simpleRequests?.length || 0);

      if (!simpleRequests || simpleRequests.length === 0) {
        console.log('No connection requests found');
        return [];
      }

      // Log the raw connection requests
      console.log('Raw connection requests:', simpleRequests);

      // Now fetch user data for each request
      const enhancedRequests = await Promise.all(
        simpleRequests.map(async (req) => {
          try {
            // First try to get user data from auth.users
            const { data: authUserData, error: authUserError } = await supabase
              .from('auth.users')
              .select('id, email')
              .eq('id', req.user_id)
              .single();

            // If that fails, try personal_details
            if (authUserError) {
              console.log(`Couldn't get auth user data for ${req.user_id}, trying personal_details`);
              const { data: personalData, error: personalError } = await supabase
                .from('personal_details')
                .select('first_name, last_name, email, profile_name')
                .eq('user_id', req.user_id)
                .single();

              if (personalError) {
                console.log(`Couldn't get personal details for ${req.user_id}, using basic data`);
                // If both fail, use a basic user object with the ID
                return {
                  ...req,
                  user: {
                    id: req.user_id,
                    email: `user-${req.user_id.substring(0, 8)}@example.com` // Fallback email
                  }
                };
              }

              return {
                ...req,
                user: {
                  id: req.user_id,
                  ...personalData
                }
              };
            }

            // If we got auth user data, use that
            return {
              ...req,
              user: authUserData
            };
          } catch (err) {
            console.error(`Error enhancing request ${req.id}:`, err);
            return {
              ...req,
              user: {
                id: req.user_id,
                email: `user-${req.user_id.substring(0, 8)}@example.com` // Fallback email
              }
            };
          }
        })
      );

      console.log('Enhanced requests:', enhancedRequests);

      // Update the store with the enhanced results
      connectionRequests.value = enhancedRequests;

      return connectionRequests.value;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch connection requests';
      console.error('Error in fetchConnectionRequests:', err);
      return [];
    } finally {
      loading.value = false;
    }
  }

  /**
   * Debug function to check for connection requests directly
   */
  async function debugCheckConnectionRequests(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot debug connection requests: User not authenticated');
        return;
      }

      console.log('Debug: Checking connection requests for user:', user.id);

      // First, get the basic connection requests without joins
      const { data: simpleRequests, error: simpleError } = await supabase
        .from('user_connections')
        .select('*')
        .eq('connected_user_id', user.id)
        .eq('connection_status', 'pending');

      if (simpleError) {
        console.error('Debug: Error checking connection requests:', simpleError);
        return;
      }

      console.log('Debug: Found connection requests:', simpleRequests?.length || 0);

      if (!simpleRequests || simpleRequests.length === 0) {
        console.log('Debug: No connection requests found');

        // Check if there are any connection requests at all in the system
        const { data: allRequests, error: allError } = await supabase
          .from('user_connections')
          .select('*')
          .eq('connection_status', 'pending')
          .limit(5);

        if (allError) {
          console.error('Debug: Error checking all connection requests:', allError);
        } else {
          console.log(`Debug: Found ${allRequests?.length || 0} pending connection requests in the system`);
          if (allRequests && allRequests.length > 0) {
            console.log('Debug: Sample connection requests:', allRequests);
          }
        }

        return;
      }

      // Log the raw connection requests
      console.log('Debug: Raw connection requests:', simpleRequests);

      // Now fetch user data for each request
      const enhancedRequests = await Promise.all(
        simpleRequests.map(async (req) => {
          try {
            // First try to get user data from auth.users
            const { data: authUserData, error: authUserError } = await supabase
              .from('auth.users')
              .select('id, email')
              .eq('id', req.user_id)
              .single();

            // If that fails, try personal_details
            if (authUserError) {
              console.log(`Debug: Couldn't get auth user data for ${req.user_id}, trying personal_details`);
              const { data: personalData, error: personalError } = await supabase
                .from('personal_details')
                .select('first_name, last_name, email, profile_name')
                .eq('user_id', req.user_id)
                .single();

              if (personalError) {
                console.log(`Debug: Couldn't get personal details for ${req.user_id}, using basic data`);
                // If both fail, use a basic user object with the ID
                return {
                  ...req,
                  user: {
                    id: req.user_id,
                    email: `user-${req.user_id.substring(0, 8)}@example.com` // Fallback email
                  }
                };
              }

              return {
                ...req,
                user: {
                  id: req.user_id,
                  ...personalData
                }
              };
            }

            // If we got auth user data, use that
            return {
              ...req,
              user: authUserData
            };
          } catch (err) {
            console.error(`Debug: Error enhancing request ${req.id}:`, err);
            return {
              ...req,
              user: {
                id: req.user_id,
                email: `user-${req.user_id.substring(0, 8)}@example.com` // Fallback email
              }
            };
          }
        })
      );

      console.log('Debug: Enhanced requests:', enhancedRequests);

      // Update the store with the enhanced results
      connectionRequests.value = enhancedRequests;
    } catch (err) {
      console.error('Debug: Unexpected error checking connection requests:', err);
    }
  }

  return {
    connections,
    connectionRequests,
    loading,
    error,
    connectionStatusChangeEvent,
    getConnections,
    getConnectionRequests,
    isLoading,
    getError,
    connectionsCount,
    triggerConnectionStatusChange,
    synchronizeConnectionData,
    connectWithUser,
    acceptConnection,
    fetchUserConnections,
    getUserConnectionCount,
    fetchConnectionRequests,
    debugCheckConnectionRequests
  };
});
