<template>
  <q-btn
    :loading="loading"
    :disable="isDisabled"
    v-bind="$attrs"
    @click="handleClick"
  >
    <slot />
  </q-btn>
</template>

<script setup lang="ts">
import { useNavigationButton } from '../../composables/useButtonState'

interface Props {
  to: string | { name: string; params?: any; query?: any }
  closeMenus?: boolean
  showLoadingFor?: number
}

const props = withDefaults(defineProps<Props>(), {
  closeMenus: false,
  showLoadingFor: 200
})

const emit = defineEmits<{
  success: [destination: string | object]
  error: [error: Error]
}>()

const { loading, isDisabled, navigateWithLoading } = useNavigationButton()

async function handleClick() {
  try {
    const success = await navigateWithLoading(props.to, {
      closeMenus: props.closeMenus,
      showLoadingFor: props.showLoadingFor
    })
    
    if (success) {
      emit('success', props.to)
    } else {
      throw new Error('Navigation failed')
    }
  } catch (error) {
    emit('error', error as Error)
  }
}
</script>

<style scoped>
/* Inherit all parent styles */
</style>
