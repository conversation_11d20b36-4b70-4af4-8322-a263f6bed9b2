-- Migration: Add Profile Embeddings for AI-Enhanced Matchmaking
-- File: supabase/migrations/20250116_add_profile_embeddings.sql
-- Description: Adds vector embedding columns to all profile tables for semantic matching

-- Ensure vector extension is available
CREATE EXTENSION IF NOT EXISTS vector;

-- =============================================================================
-- INNOVATOR PROFILES EMBEDDINGS
-- =============================================================================

ALTER TABLE innovator_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(384),
ADD COLUMN IF NOT EXISTS goals_embedding vector(384),
ADD COLUMN IF NOT EXISTS challenges_embedding vector(384),
ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

-- Create indexes for efficient vector similarity search
CREATE INDEX IF NOT EXISTS innovator_profiles_profile_embedding_idx 
ON innovator_profiles USING ivfflat (profile_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS innovator_profiles_goals_embedding_idx 
ON innovator_profiles USING ivfflat (goals_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS innovator_profiles_challenges_embedding_idx 
ON innovator_profiles USING ivfflat (challenges_embedding vector_cosine_ops) WITH (lists = 100);

-- =============================================================================
-- INVESTOR PROFILES EMBEDDINGS
-- =============================================================================

ALTER TABLE investor_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(384),
ADD COLUMN IF NOT EXISTS focus_embedding vector(384),
ADD COLUMN IF NOT EXISTS portfolio_embedding vector(384),
ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

-- Create indexes for efficient vector similarity search
CREATE INDEX IF NOT EXISTS investor_profiles_profile_embedding_idx 
ON investor_profiles USING ivfflat (profile_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS investor_profiles_focus_embedding_idx 
ON investor_profiles USING ivfflat (focus_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS investor_profiles_portfolio_embedding_idx 
ON investor_profiles USING ivfflat (portfolio_embedding vector_cosine_ops) WITH (lists = 100);

-- =============================================================================
-- MENTOR PROFILES EMBEDDINGS
-- =============================================================================

ALTER TABLE mentor_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(384),
ADD COLUMN IF NOT EXISTS expertise_embedding vector(384),
ADD COLUMN IF NOT EXISTS approach_embedding vector(384),
ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

-- Create indexes for efficient vector similarity search
CREATE INDEX IF NOT EXISTS mentor_profiles_profile_embedding_idx 
ON mentor_profiles USING ivfflat (profile_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS mentor_profiles_expertise_embedding_idx 
ON mentor_profiles USING ivfflat (expertise_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS mentor_profiles_approach_embedding_idx 
ON mentor_profiles USING ivfflat (approach_embedding vector_cosine_ops) WITH (lists = 100);

-- =============================================================================
-- PROFESSIONAL PROFILES EMBEDDINGS
-- =============================================================================

ALTER TABLE professional_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(384),
ADD COLUMN IF NOT EXISTS skills_embedding vector(384),
ADD COLUMN IF NOT EXISTS experience_embedding vector(384),
ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

-- Create indexes for efficient vector similarity search
CREATE INDEX IF NOT EXISTS professional_profiles_profile_embedding_idx 
ON professional_profiles USING ivfflat (profile_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS professional_profiles_skills_embedding_idx 
ON professional_profiles USING ivfflat (skills_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS professional_profiles_experience_embedding_idx 
ON professional_profiles USING ivfflat (experience_embedding vector_cosine_ops) WITH (lists = 100);

-- =============================================================================
-- INDUSTRY EXPERT PROFILES EMBEDDINGS
-- =============================================================================

ALTER TABLE industry_expert_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(384),
ADD COLUMN IF NOT EXISTS expertise_embedding vector(384),
ADD COLUMN IF NOT EXISTS content_embedding vector(384),
ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

-- Create indexes for efficient vector similarity search
CREATE INDEX IF NOT EXISTS industry_expert_profiles_profile_embedding_idx 
ON industry_expert_profiles USING ivfflat (profile_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS industry_expert_profiles_expertise_embedding_idx 
ON industry_expert_profiles USING ivfflat (expertise_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS industry_expert_profiles_content_embedding_idx 
ON industry_expert_profiles USING ivfflat (content_embedding vector_cosine_ops) WITH (lists = 100);

-- =============================================================================
-- ACADEMIC STUDENT PROFILES EMBEDDINGS
-- =============================================================================

ALTER TABLE academic_student_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(384),
ADD COLUMN IF NOT EXISTS research_embedding vector(384),
ADD COLUMN IF NOT EXISTS skills_embedding vector(384),
ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

-- Create indexes for efficient vector similarity search
CREATE INDEX IF NOT EXISTS academic_student_profiles_profile_embedding_idx 
ON academic_student_profiles USING ivfflat (profile_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS academic_student_profiles_research_embedding_idx 
ON academic_student_profiles USING ivfflat (research_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS academic_student_profiles_skills_embedding_idx 
ON academic_student_profiles USING ivfflat (skills_embedding vector_cosine_ops) WITH (lists = 100);

-- =============================================================================
-- ACADEMIC INSTITUTION PROFILES EMBEDDINGS
-- =============================================================================

ALTER TABLE academic_institution_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(384),
ADD COLUMN IF NOT EXISTS research_embedding vector(384),
ADD COLUMN IF NOT EXISTS programs_embedding vector(384),
ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

-- Create indexes for efficient vector similarity search
CREATE INDEX IF NOT EXISTS academic_institution_profiles_profile_embedding_idx 
ON academic_institution_profiles USING ivfflat (profile_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS academic_institution_profiles_research_embedding_idx 
ON academic_institution_profiles USING ivfflat (research_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS academic_institution_profiles_programs_embedding_idx 
ON academic_institution_profiles USING ivfflat (programs_embedding vector_cosine_ops) WITH (lists = 100);

-- =============================================================================
-- ORGANISATION PROFILES EMBEDDINGS
-- =============================================================================

ALTER TABLE organisation_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(384),
ADD COLUMN IF NOT EXISTS focus_embedding vector(384),
ADD COLUMN IF NOT EXISTS collaboration_embedding vector(384),
ADD COLUMN IF NOT EXISTS embedding_metadata jsonb DEFAULT '{}',
ADD COLUMN IF NOT EXISTS embeddings_generated_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS embedding_model_version varchar(50) DEFAULT 'gte-small-v1';

-- Create indexes for efficient vector similarity search
CREATE INDEX IF NOT EXISTS organisation_profiles_profile_embedding_idx 
ON organisation_profiles USING ivfflat (profile_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS organisation_profiles_focus_embedding_idx 
ON organisation_profiles USING ivfflat (focus_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS organisation_profiles_collaboration_embedding_idx 
ON organisation_profiles USING ivfflat (collaboration_embedding vector_cosine_ops) WITH (lists = 100);

-- =============================================================================
-- COMMENTS AND DOCUMENTATION
-- =============================================================================

-- Add comments for documentation
COMMENT ON COLUMN innovator_profiles.profile_embedding IS 'Vector embedding of profile bio, innovation area, and description for semantic matching';
COMMENT ON COLUMN innovator_profiles.goals_embedding IS 'Vector embedding of short/long term goals and looking_for fields';
COMMENT ON COLUMN innovator_profiles.challenges_embedding IS 'Vector embedding of current challenges and additional interests';

COMMENT ON COLUMN investor_profiles.profile_embedding IS 'Vector embedding of profile bio and investment philosophy';
COMMENT ON COLUMN investor_profiles.focus_embedding IS 'Vector embedding of investment focus, criteria, and preferred sectors';
COMMENT ON COLUMN investor_profiles.portfolio_embedding IS 'Vector embedding of portfolio description and achievements';

COMMENT ON COLUMN mentor_profiles.profile_embedding IS 'Vector embedding of profile bio and mentoring philosophy';
COMMENT ON COLUMN mentor_profiles.expertise_embedding IS 'Vector embedding of areas of expertise and industry experience';
COMMENT ON COLUMN mentor_profiles.approach_embedding IS 'Vector embedding of mentoring approach and methods';

-- Add metadata tracking
COMMENT ON COLUMN innovator_profiles.embedding_metadata IS 'Metadata about embedding generation (source fields, processing info)';
COMMENT ON COLUMN innovator_profiles.embeddings_generated_at IS 'Timestamp when embeddings were last generated';
COMMENT ON COLUMN innovator_profiles.embedding_model_version IS 'Version of the embedding model used';
