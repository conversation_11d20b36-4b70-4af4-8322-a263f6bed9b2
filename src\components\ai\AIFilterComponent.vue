<template>
  <q-card class="ai-filter-card">
    <q-card-section class="q-pa-md">
      <!-- AI Search Header -->
      <div class="ai-search-header q-mb-md">
        <div class="text-h6 text-primary q-mb-sm">
          <q-icon name="psychology" color="primary" size="sm" class="q-mr-xs" />
          AI-Powered Search
        </div>
        <div class="text-caption text-grey-6">
          Use natural language to find exactly what you're looking for
        </div>
      </div>

      <!-- Main AI Search Button -->
      <div class="main-search q-mb-lg">
        <AITriggerButton
          trigger-key="ai_search"
          label="AI Search"
          icon="search"
          color="primary"
          tooltip="Use AI to search with natural language"
          :context="`${context}-search`"
          size="md"
          variant="compact"
          class="full-width ai-search-btn"
          @triggered="onTriggerActivated"
          @success="onTriggerSuccess"
          @error="onTriggerError"
        />
      </div>

      <!-- Quick AI Filters -->
      <div v-if="quickFilters.length > 0" class="quick-filters q-mb-md">
        <div class="text-subtitle2 text-weight-medium q-mb-sm">
          <q-icon name="flash_on" color="amber" size="sm" class="q-mr-xs" />
          Quick Filters
        </div>
        <div class="filter-grid">
          <template v-for="filter in quickFilters" :key="filter.key">
            <AITriggerButton
              :trigger-key="filter.key"
              :label="filter.label"
              :icon="filter.icon"
              :color="filter.color"
              :tooltip="filter.tooltip"
              :context="`${context}-quick`"
              size="sm"
              variant="compact"
              class="quick-filter-btn"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </template>
        </div>
      </div>

      <!-- Category Filters -->
      <div v-if="categoryFilters.length > 0" class="category-filters q-mb-md">
        <div class="text-subtitle2 text-weight-medium q-mb-sm">
          <q-icon name="category" color="blue" size="sm" class="q-mr-xs" />
          Categories
        </div>
        <div class="filter-grid">
          <template v-for="filter in categoryFilters" :key="filter.key">
            <AITriggerButton
              :trigger-key="filter.key"
              :label="filter.label"
              :icon="filter.icon"
              :color="filter.color"
              :tooltip="filter.tooltip"
              :context="`${context}-category`"
              size="sm"
              variant="compact"
              class="category-filter-btn"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </template>
        </div>
      </div>

      <!-- Specialized Filters -->
      <div v-if="specializedFilters.length > 0" class="specialized-filters">
        <div class="text-subtitle2 text-weight-medium q-mb-sm">
          <q-icon name="tune" color="purple" size="sm" class="q-mr-xs" />
          Specialized Search
        </div>
        <div class="filter-grid">
          <template v-for="filter in specializedFilters" :key="filter.key">
            <AITriggerButton
              :trigger-key="filter.key"
              :label="filter.label"
              :icon="filter.icon"
              :color="filter.color"
              :tooltip="filter.tooltip"
              :context="`${context}-specialized`"
              size="sm"
              variant="compact"
              class="specialized-filter-btn"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </template>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import AITriggerButton from './AITriggerButton.vue'

// Props
interface Props {
  context: string
  quickFilters?: AIFilter[]
  categoryFilters?: AIFilter[]
  specializedFilters?: AIFilter[]
}

interface AIFilter {
  key: string
  label: string
  icon: string
  color: string
  tooltip: string
}

const props = withDefaults(defineProps<Props>(), {
  quickFilters: () => [],
  categoryFilters: () => [],
  specializedFilters: () => []
})

// Emits
const emit = defineEmits<{
  'trigger-activated': [triggerKey: string]
  'trigger-success': [result: any]
  'trigger-error': [error: any]
}>()

// Event handlers
function onTriggerActivated(triggerKey: string) {
  console.log('🎯 AI filter trigger activated:', triggerKey)
  emit('trigger-activated', triggerKey)
}

function onTriggerSuccess(result: any) {
  console.log('✅ AI filter trigger success:', result)
  emit('trigger-success', result)
}

function onTriggerError(error: any) {
  console.error('❌ AI filter trigger error:', error)
  emit('trigger-error', error)
}
</script>

<style scoped>
.ai-filter-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(13, 138, 62, 0.12);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
}

.ai-search-header {
  text-align: center;
  padding: 8px 0;
}

.main-search {
  display: flex;
  justify-content: center;
}

.ai-search-btn {
  min-height: 48px;
  font-weight: 600;
  border-radius: 8px;
  background: linear-gradient(135deg, #0d8a3e 0%, #0f9d47 100%);
  box-shadow: 0 2px 8px rgba(13, 138, 62, 0.3);
  transition: all 0.3s ease;
}

.ai-search-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 138, 62, 0.4);
}

.filter-grid {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  overflow-x: auto;
  padding-bottom: 4px;
}

.filter-grid::-webkit-scrollbar {
  height: 4px;
}

.filter-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.filter-grid::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

.filter-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Ensure filter buttons maintain minimum width */
.quick-filter-btn,
.category-filter-btn,
.specialized-filter-btn {
  min-width: 120px;
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .quick-filter-btn,
  .category-filter-btn,
  .specialized-filter-btn {
    min-width: 100px;
  }
}

.quick-filters {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.08) 0%, rgba(255, 193, 7, 0.03) 100%);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 193, 7, 0.15);
}

.category-filters {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(25, 118, 210, 0.03) 100%);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(25, 118, 210, 0.15);
}

.specialized-filters {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.08) 0%, rgba(156, 39, 176, 0.03) 100%);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(156, 39, 176, 0.15);
}

.quick-filter-btn,
.category-filter-btn,
.specialized-filter-btn {
  min-height: 36px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.quick-filter-btn:hover,
.category-filter-btn:hover,
.specialized-filter-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-grid {
    gap: 6px;
  }

  .quick-filters,
  .category-filters,
  .specialized-filters {
    padding: 8px;
  }
}
</style>
