<template>
  <q-avatar
    :size="size"
    :class="clickable ? 'cursor-pointer' : ''"
    @click="handleClick"
  >
    <!-- If avatar URL is provided and valid, show the image -->
    <img v-if="avatarUrl" :src="avatarUrl" @error="handleImageError" />

    <!-- Otherwise, show initials with background color -->
    <div v-else class="initials-avatar" :style="{ backgroundColor: bgColor }">
      {{ initials }}
    </div>
  </q-avatar>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';
import { generateInitials, generateAvatarColor } from '../../utils/userUtils';
import { getInitials } from '../../utils/nameUtils';

const props = defineProps({
  // User data
  name: {
    type: String,
    default: ''
  },
  email: {
    type: String,
    default: ''
  },
  avatarUrl: {
    type: String,
    default: ''
  },
  userId: {
    type: [String, Number],
    default: ''
  },

  // Avatar appearance
  size: {
    type: String,
    default: '40px'
  },
  clickable: {
    type: Boolean,
    default: true
  },
  color: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['click']);

// Track if the image failed to load
const imageError = ref(false);

// Computed properties
const initials = computed(() => {
  // First try the new utility function
  if (props.name) {
    return getInitials(props.name);
  }

  if (props.email) {
    return getInitials(props.email);
  }

  // Fall back to the old function for backward compatibility
  return generateInitials(props.name, props.email);
});

const bgColor = computed(() => {
  // If a specific color is provided, use it
  if (props.color) {
    return props.color;
  }

  // Use name or email as the identifier for consistent color
  const identifier = props.name || props.email || props.userId?.toString();
  return generateAvatarColor(identifier);
});

// Methods
function handleClick() {
  if (props.clickable) {
    emit('click', props.userId);
  }
}

function handleImageError() {
  imageError.value = true;
}
</script>

<style scoped>
.initials-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  text-transform: uppercase;
}
</style>
