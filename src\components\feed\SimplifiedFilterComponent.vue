<template>
  <q-card class="filter-card">
    <q-card-section class="q-pa-md">
      <!-- AI Search Section -->
      <div class="ai-search-section q-mb-md">
        <div class="text-h6 text-primary q-mb-md">
          <q-icon name="psychology" color="primary" size="sm" class="q-mr-xs" />
          AI-Powered Search & Discovery
        </div>

        <!-- Main AI Search Button -->
        <div class="q-mb-md">
          <AITriggerButton
            trigger-key="ai_search"
            label="AI Search"
            icon="search"
            color="primary"
            tooltip="Use AI to search and discover content with natural language"
            :context="`community-${activeTab}`"
            size="md"
            variant="compact"
            class="full-width"
            @triggered="onTriggerActivated"
            @success="onTriggerSuccess"
            @error="onTriggerError"
          />
        </div>

        <!-- Tab-Specific AI Triggers -->
        <div class="row q-gutter-xs q-mb-md">
          <template v-for="trigger in currentTabTriggers" :key="trigger.key">
            <AITriggerButton
              :trigger-key="trigger.key"
              :label="trigger.label"
              :icon="trigger.icon"
              :color="trigger.color || 'secondary'"
              :tooltip="trigger.tooltip"
              :context="`community-${activeTab}`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </template>
        </div>
      </div>

      <!-- AI Filter Categories -->
      <div class="ai-filters-section">
        <div class="text-subtitle1 text-weight-medium q-mb-sm">
          <q-icon name="tune" color="secondary" size="sm" class="q-mr-xs" />
          Smart Filters
        </div>
        <div class="text-caption text-grey-6 q-mb-md">
          Click any filter to get AI-powered results for that category
        </div>

        <!-- AI Time-based Filters -->
        <div class="q-mb-md">
          <div class="text-caption text-grey-6 q-mb-xs">Time-based Search</div>
          <div class="row q-gutter-xs">
            <AITriggerButton
              trigger-key="recent_content"
              label="Recent"
              icon="schedule"
              color="blue"
              tooltip="Find recent content from the last few days"
              :context="`community-${activeTab}-time`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="trending_content"
              label="Trending"
              icon="trending_up"
              color="orange"
              tooltip="Find trending and popular content"
              :context="`community-${activeTab}-time`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="this_week"
              label="This Week"
              icon="date_range"
              color="purple"
              tooltip="Find content from this week"
              :context="`community-${activeTab}-time`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </div>
        </div>

        <!-- AI Category Filters -->
        <div class="q-mb-md">
          <div class="text-caption text-grey-6 q-mb-xs">Content Categories</div>
          <div class="row q-gutter-xs">
            <template v-for="category in aiCategoryFilters" :key="category.key">
              <AITriggerButton
                :trigger-key="category.key"
                :label="category.label"
                :icon="category.icon"
                :color="category.color"
                :tooltip="category.tooltip"
                :context="`community-${activeTab}-category`"
                size="sm"
                variant="compact"
                @triggered="onTriggerActivated"
                @success="onTriggerSuccess"
                @error="onTriggerError"
              />
            </template>
          </div>
        </div>

        <!-- AI Topic Filters -->
        <div class="q-mb-md">
          <div class="text-caption text-grey-6 q-mb-xs">Popular Topics</div>
          <div class="row q-gutter-xs">
            <template v-for="topic in aiTopicFilters" :key="topic.key">
              <AITriggerButton
                :trigger-key="topic.key"
                :label="topic.label"
                :icon="topic.icon"
                :color="topic.color"
                :tooltip="topic.tooltip"
                :context="`community-${activeTab}-topic`"
                size="sm"
                variant="compact"
                @triggered="onTriggerActivated"
                @success="onTriggerSuccess"
                @error="onTriggerError"
              />
            </template>
          </div>
        </div>
      </div>

      <!-- Tab-Specific AI Filters -->
      <template v-if="activeTab === 'feed'">
        <div class="ai-feed-filters q-mb-md">
          <div class="text-caption text-grey-6 q-mb-xs">Feed Specific</div>
          <div class="row q-gutter-xs">
            <AITriggerButton
              trigger-key="innovation_posts"
              label="Innovation"
              icon="lightbulb"
              color="amber"
              tooltip="Find innovation and startup related posts"
              :context="`community-feed-innovation`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="collaboration_posts"
              label="Collaboration"
              icon="handshake"
              color="green"
              tooltip="Find collaboration opportunities and partnerships"
              :context="`community-feed-collaboration`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="funding_posts"
              label="Funding"
              icon="attach_money"
              color="teal"
              tooltip="Find funding opportunities and investment posts"
              :context="`community-feed-funding`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </div>
        </div>
      </template>

      <template v-else-if="activeTab === 'profiles'">
        <div class="ai-profile-filters q-mb-md">
          <div class="text-caption text-grey-6 q-mb-xs">Profile Types</div>
          <div class="row q-gutter-xs">
            <AITriggerButton
              trigger-key="find_mentors"
              label="Mentors"
              icon="school"
              color="blue"
              tooltip="Find experienced mentors in your field"
              :context="`community-profiles-mentors`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_investors"
              label="Investors"
              icon="trending_up"
              color="green"
              tooltip="Find investors and funding partners"
              :context="`community-profiles-investors`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_entrepreneurs"
              label="Entrepreneurs"
              icon="rocket_launch"
              color="orange"
              tooltip="Find fellow entrepreneurs and founders"
              :context="`community-profiles-entrepreneurs`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_researchers"
              label="Researchers"
              icon="science"
              color="purple"
              tooltip="Find researchers and academics"
              :context="`community-profiles-researchers`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </div>
        </div>
      </template>

      <template v-else-if="activeTab === 'events'">
        <div class="ai-event-filters q-mb-md">
          <div class="text-caption text-grey-6 q-mb-xs">Event Types</div>
          <div class="row q-gutter-xs">
            <AITriggerButton
              trigger-key="find_workshops"
              label="Workshops"
              icon="build"
              color="blue"
              tooltip="Find hands-on workshops and training sessions"
              :context="`community-events-workshops`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_conferences"
              label="Conferences"
              icon="groups"
              color="purple"
              tooltip="Find conferences and large networking events"
              :context="`community-events-conferences`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_networking"
              label="Networking"
              icon="people"
              color="green"
              tooltip="Find networking events and meetups"
              :context="`community-events-networking`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_competitions"
              label="Competitions"
              icon="emoji_events"
              color="amber"
              tooltip="Find competitions and challenges"
              :context="`community-events-competitions`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </div>
        </div>
      </template>

      <template v-else-if="activeTab === 'blog'">
        <div class="ai-blog-filters q-mb-md">
          <div class="text-caption text-grey-6 q-mb-xs">Article Types</div>
          <div class="row q-gutter-xs">
            <AITriggerButton
              trigger-key="find_insights"
              label="Insights"
              icon="psychology"
              color="blue"
              tooltip="Find industry insights and analysis"
              :context="`community-blog-insights`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_trends"
              label="Trends"
              icon="trending_up"
              color="orange"
              tooltip="Find trending topics and market analysis"
              :context="`community-blog-trends`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_success_stories"
              label="Success Stories"
              icon="star"
              color="amber"
              tooltip="Find success stories and case studies"
              :context="`community-blog-success`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_research"
              label="Research"
              icon="science"
              color="purple"
              tooltip="Find research articles and academic content"
              :context="`community-blog-research`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </div>
        </div>
      </template>

      <template v-else-if="activeTab === 'marketplace'">
        <div class="ai-marketplace-filters q-mb-md">
          <div class="text-caption text-grey-6 q-mb-xs">Listing Types</div>
          <div class="row q-gutter-xs">
            <AITriggerButton
              trigger-key="find_products"
              label="Products"
              icon="inventory"
              color="blue"
              tooltip="Find products and physical items"
              :context="`community-marketplace-products`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_services"
              label="Services"
              icon="handyman"
              color="green"
              tooltip="Find services and professional offerings"
              :context="`community-marketplace-services`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_jobs"
              label="Jobs"
              icon="work"
              color="purple"
              tooltip="Find job opportunities and positions"
              :context="`community-marketplace-jobs`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_free_items"
              label="Free"
              icon="volunteer_activism"
              color="teal"
              tooltip="Find free items and resources"
              :context="`community-marketplace-free`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </div>
        </div>
      </template>

      <template v-else-if="activeTab === 'groups'">
        <div class="ai-group-filters q-mb-md">
          <div class="text-caption text-grey-6 q-mb-xs">Group Categories</div>
          <div class="row q-gutter-xs">
            <AITriggerButton
              trigger-key="find_fintech_groups"
              label="FinTech"
              icon="account_balance"
              color="blue"
              tooltip="Find financial technology groups"
              :context="`community-groups-fintech`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_agritech_groups"
              label="AgriTech"
              icon="agriculture"
              color="green"
              tooltip="Find agricultural technology groups"
              :context="`community-groups-agritech`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_healthtech_groups"
              label="HealthTech"
              icon="medical_services"
              color="red"
              tooltip="Find health technology groups"
              :context="`community-groups-healthtech`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
            <AITriggerButton
              trigger-key="find_edtech_groups"
              label="EdTech"
              icon="school"
              color="purple"
              tooltip="Find education technology groups"
              :context="`community-groups-edtech`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </div>
        </div>
    </q-card-section>
  </q-card>
</template>



    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import AITriggerButton from '@/components/ai/AITriggerButton.vue'

// Props
interface Props {
  activeTab: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'trigger-activated': [triggerKey: string]
  'trigger-success': [result: any]
  'trigger-error': [error: any]
}>()

// AI Trigger Configurations for each tab
const aiTriggers = {
  feed: [
    {
      key: 'content_discovery',
      label: 'Discover Content',
      icon: 'explore',
      color: 'secondary',
      tooltip: 'Get AI help finding relevant content and discussions'
    },
    {
      key: 'networking',
      label: 'Find Connections',
      icon: 'people',
      color: 'accent',
      tooltip: 'Get AI help with networking and connecting with people'
    }
  ],
  profiles: [
    {
      key: 'find_mentors',
      label: 'Find Mentors',
      icon: 'school',
      color: 'primary',
      tooltip: 'Get AI help finding mentors in your field'
    }
  ],
  marketplace: [
    {
      key: 'find_products',
      label: 'Find Products',
      icon: 'shopping_cart',
      color: 'primary',
      tooltip: 'Get AI help finding relevant products and services'
    }
  ],
  groups: [
    {
      key: 'find_groups',
      label: 'Find Groups',
      icon: 'groups',
      color: 'primary',
      tooltip: 'Get AI help finding relevant groups to join'
    }
  ],
  blog: [
    {
      key: 'find_articles',
      label: 'Find Articles',
      icon: 'article',
      color: 'primary',
      tooltip: 'Get AI help finding relevant articles and insights'
    }
  ],
  events: [
    {
      key: 'find_events',
      label: 'Find Events',
      icon: 'event',
      color: 'primary',
      tooltip: 'Get AI help finding relevant events and workshops'
    }
  ]
}

// AI Category Filters - Universal across tabs
const aiCategoryFilters = computed(() => [
  {
    key: 'innovation_search',
    label: 'Innovation',
    icon: 'lightbulb',
    color: 'amber',
    tooltip: 'Search for innovation and startup content'
  },
  {
    key: 'technology_search',
    label: 'Technology',
    icon: 'computer',
    color: 'blue',
    tooltip: 'Search for technology-related content'
  },
  {
    key: 'collaboration_search',
    label: 'Collaboration',
    icon: 'handshake',
    color: 'green',
    tooltip: 'Search for collaboration opportunities'
  },
  {
    key: 'funding_search',
    label: 'Funding',
    icon: 'attach_money',
    color: 'teal',
    tooltip: 'Search for funding and investment opportunities'
  }
])

// AI Topic Filters - Dynamic based on tab
const aiTopicFilters = computed(() => {
  const baseTopics = [
    {
      key: 'startup_search',
      label: 'Startup',
      icon: 'rocket_launch',
      color: 'orange',
      tooltip: 'Search for startup-related content'
    },
    {
      key: 'networking_search',
      label: 'Networking',
      icon: 'people',
      color: 'purple',
      tooltip: 'Search for networking opportunities'
    }
  ]

  // Add tab-specific topics
  switch (props.activeTab) {
    case 'profiles':
      return [
        ...baseTopics,
        {
          key: 'mentorship_search',
          label: 'Mentorship',
          icon: 'school',
          color: 'indigo',
          tooltip: 'Search for mentorship opportunities'
        }
      ]
    case 'events':
      return [
        ...baseTopics,
        {
          key: 'workshop_search',
          label: 'Workshops',
          icon: 'build',
          color: 'cyan',
          tooltip: 'Search for workshops and training'
        }
      ]
    default:
      return baseTopics
  }
})

// AI Content Type Filters - Tab specific
const aiContentTypeFilters = computed(() => {
  switch (props.activeTab) {
    case 'marketplace':
      return [
        {
          key: 'product_search',
          label: 'Products',
          icon: 'inventory',
          color: 'blue',
          tooltip: 'Search for products and items'
        },
        {
          key: 'service_search',
          label: 'Services',
          icon: 'handyman',
          color: 'green',
          tooltip: 'Search for services and offerings'
        }
      ]
    case 'blog':
      return [
        {
          key: 'insight_search',
          label: 'Insights',
          icon: 'psychology',
          color: 'purple',
          tooltip: 'Search for insights and analysis'
        },
        {
          key: 'trend_search',
          label: 'Trends',
          icon: 'trending_up',
          color: 'orange',
          tooltip: 'Search for trending topics'
        }
      ]
    default:
      return []
  }
})

// Computed properties
const currentTabTriggers = computed(() => {
  return aiTriggers[props.activeTab as keyof typeof aiTriggers] || []
})

// Event handlers for AI triggers
function onTriggerActivated(triggerKey: string) {
  console.log('🎯 AI trigger activated:', triggerKey)
  emit('trigger-activated', triggerKey)
}

function onTriggerSuccess(result: any) {
  console.log('✅ AI trigger success:', result)
  emit('trigger-success', result)
}

function onTriggerError(error: any) {
  console.error('❌ AI trigger error:', error)
  emit('trigger-error', error)
}
</script>

<style scoped>
.filter-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(13, 138, 62, 0.1);
}

.ai-search-section {
  background: linear-gradient(135deg, rgba(13, 138, 62, 0.08) 0%, rgba(13, 138, 62, 0.03) 100%);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(13, 138, 62, 0.15);
}

.ai-filters-section {
  background-color: rgba(25, 118, 210, 0.04);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.ai-feed-filters,
.ai-profile-filters,
.ai-event-filters,
.ai-blog-filters,
.ai-marketplace-filters,
.ai-group-filters {
  background-color: rgba(156, 39, 176, 0.04);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(156, 39, 176, 0.1);
}

/* AI Trigger Button Spacing */
.row.q-gutter-xs {
  gap: 8px;
}

.row.q-gutter-xs > * {
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ai-search-section,
  .ai-filters-section {
    padding: 12px;
  }

  .ai-feed-filters,
  .ai-profile-filters,
  .ai-event-filters,
  .ai-blog-filters,
  .ai-marketplace-filters,
  .ai-group-filters {
    padding: 8px;
  }
}
</style>
