import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Types for query routing
interface QueryRouterRequest {
  query: string;
  user_context?: {
    user_id?: string;
    profile_type?: string;
    profile_status?: string;
    profile_completion?: number;
    recent_interactions?: string[];
    interests?: string[];
  };
  force_route?: 'rag' | 'text2sql' | 'hybrid';
  conversation_context?: {
    previous_queries?: string[];
    conversation_topic?: string;
    user_intent?: string;
  };
}

interface QueryRouterResponse {
  success: boolean;
  route: 'rag' | 'text2sql' | 'hybrid';
  confidence: number;
  reasoning: string;
  suggested_approach: {
    primary_method: string;
    fallback_method?: string;
    context_needed: string[];
    matching_strategy?: 'semantic' | 'interest_based' | 'collaborative' | 'hybrid';
  };
  processing_time_ms: number;
  error?: string;
}

// DeepSeek API configuration for lightweight classification
const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY');
const DEEPSEEK_BASE_URL = 'https://api.deepseek.com';

/**
 * Enhanced pattern-based query classification with context awareness
 */
const QUERY_PATTERNS = {
  general_assistance: [
    // Simple greetings and conversational queries
    /^(hi|hello|hey|good morning|good afternoon|good evening)$/i,
    /^how are you\??$/i,
    /^what's up\??$/i,
    /^how's it going\??$/i,
    /^nice to meet you$/i,
    /^thanks?( you)?$/i,
    /^thank you$/i,
    /^bye|goodbye|see you$/i,
    /^(yes|no|ok|okay|sure)$/i,
    // Simple help requests
    /^help$/i,
    /^what can you do\??$/i,
    /^what are your capabilities\??$/i,
    /^how can you help me\??$/i,
    /^what is this platform\??$/i,
    /^tell me about this platform$/i
  ],
  rag: [
    // Content and semantic queries (for advice, explanations, recommendations)
    /tell me about.*how to|describe.*process|explain.*concept/i,
    /what is.*innovation|what does.*mean|how does.*work/i,
    /help me.*with|advice.*on|guidance.*for/i,
    /recommend.*based on|suggest.*for|find similar/i,
    /experience|background|expertise|skills.*description/i,
    /bio|profile.*story|journey|career path/i,
    // Enhanced patterns for AI-powered matching and recommendations
    /interested in|passionate about|looking for.*advice/i,
    /network.*strategy|networking.*tips|how to connect/i,
    /discover.*opportunities|explore.*options/i,
    // Profile and content discovery
    /find.*mentors|show.*innovators|discover.*investors/i,
    /who.*specializes in|experts in|people with experience/i,
    /connect me with|introduce me to|help me find/i
  ],
  text2sql: [
    // Analytics and data queries
    /how many|count|total|number of|statistics/i,
    /last month|this year|between|since|during/i,
    /top \d+|ranking|metrics|performance|analytics/i,
    /average|median|sum|maximum|minimum/i,
    /growth|trend|increase|decrease|percentage/i,
    /compare numbers|data analysis|report/i,
    /dashboard|chart|graph|visualization/i,
    // User listing and database queries (CRITICAL FIX)
    /which.*are on|who are.*on the platform|list.*users|show.*members/i,
    /what.*innovators|what.*investors|what.*mentors.*platform/i,
    /find.*profiles|show.*profiles|list.*profiles/i,
    /users.*with|members.*who|profiles.*that/i,
    /browse.*users|view.*members|see.*profiles/i
  ],
  hybrid: [
    // Complex queries needing both approaches
    /show me.*and.*data|find.*with.*statistics/i,
    /profiles.*metrics|users.*analytics/i,
    /top.*profiles|best.*with.*numbers/i,
    /compare.*and.*show|analyze.*profiles/i,
    /recommend.*based on.*data/i
  ]
};

/**
 * Enhanced query classification with user context awareness
 */
function classifyQueryWithContext(
  query: string,
  userContext?: QueryRouterRequest['user_context'],
  conversationContext?: QueryRouterRequest['conversation_context']
): { route: string; confidence: number; reasoning: string; matching_strategy?: string } {
  const baseClassification = classifyQueryByPatterns(query)

  // Apply context-based adjustments
  let adjustedConfidence = baseClassification.confidence
  let reasoning = baseClassification.reasoning
  let matchingStrategy: string | undefined

  // User profile context adjustments
  if (userContext) {
    // New users might benefit more from RAG-based discovery
    if (userContext.profile_completion && userContext.profile_completion < 50) {
      if (baseClassification.route === 'rag') {
        adjustedConfidence += 0.1
        reasoning += ' | New user benefits from content discovery'
      }
    }

    // Profile type specific adjustments
    if (userContext.profile_type) {
      const profileBasedAdjustments = getProfileBasedRouting(query, userContext.profile_type)
      if (profileBasedAdjustments.boost) {
        adjustedConfidence += 0.05
        reasoning += ` | ${profileBasedAdjustments.reason}`
        matchingStrategy = profileBasedAdjustments.matching_strategy
      }
    }

    // Interest-based routing
    if (userContext.interests && userContext.interests.length > 0) {
      const hasInterestMatch = userContext.interests.some(interest =>
        query.toLowerCase().includes(interest.toLowerCase())
      )
      if (hasInterestMatch && baseClassification.route === 'rag') {
        adjustedConfidence += 0.1
        reasoning += ' | Query matches user interests'
        matchingStrategy = 'interest_based'
      }
    }
  }

  // Conversation context adjustments
  if (conversationContext?.previous_queries) {
    const recentQueries = conversationContext.previous_queries.slice(-3)
    const hasAnalyticsHistory = recentQueries.some(q =>
      classifyQueryByPatterns(q).route === 'text2sql'
    )

    if (hasAnalyticsHistory && baseClassification.route === 'text2sql') {
      adjustedConfidence += 0.05
      reasoning += ' | Consistent with recent analytics queries'
    }
  }

  return {
    route: baseClassification.route,
    confidence: Math.min(0.95, adjustedConfidence),
    reasoning,
    matching_strategy: matchingStrategy
  }
}

/**
 * Get profile-type specific routing preferences
 */
function getProfileBasedRouting(query: string, profileType: string): {
  boost: boolean;
  reason: string;
  matching_strategy?: string;
} {
  const queryLower = query.toLowerCase()

  switch (profileType) {
    case 'investor':
      if (queryLower.includes('innovator') || queryLower.includes('startup') || queryLower.includes('entrepreneur')) {
        return {
          boost: true,
          reason: 'Investor seeking innovators',
          matching_strategy: 'semantic'
        }
      }
      break
    case 'innovator':
      if (queryLower.includes('investor') || queryLower.includes('funding') || queryLower.includes('mentor')) {
        return {
          boost: true,
          reason: 'Innovator seeking support',
          matching_strategy: 'hybrid'
        }
      }
      break
    case 'mentor':
      if (queryLower.includes('student') || queryLower.includes('mentee') || queryLower.includes('guidance')) {
        return {
          boost: true,
          reason: 'Mentor seeking mentees',
          matching_strategy: 'collaborative'
        }
      }
      break
    case 'academic_student':
      if (queryLower.includes('mentor') || queryLower.includes('expert') || queryLower.includes('guidance')) {
        return {
          boost: true,
          reason: 'Student seeking mentorship',
          matching_strategy: 'interest_based'
        }
      }
      break
  }

  return { boost: false, reason: '' }
}

/**
 * Advanced query classification using patterns and keywords
 */
function classifyQueryByPatterns(query: string): { route: string; confidence: number; reasoning: string } {
  const lowerQuery = query.toLowerCase().trim();

  // Check for general assistance patterns first (highest priority)
  for (const pattern of QUERY_PATTERNS.general_assistance) {
    if (pattern.test(lowerQuery)) {
      return {
        route: 'general_assistance',
        confidence: 0.95,
        reasoning: 'Simple greeting or general assistance query'
      };
    }
  }

  // Check for explicit hybrid patterns
  for (const pattern of QUERY_PATTERNS.hybrid) {
    if (pattern.test(lowerQuery)) {
      return {
        route: 'hybrid',
        confidence: 0.85,
        reasoning: 'Query contains both content and analytical elements'
      };
    }
  }

  // Check for text2sql patterns
  let sqlScore = 0;
  for (const pattern of QUERY_PATTERNS.text2sql) {
    if (pattern.test(lowerQuery)) {
      sqlScore += 1;
    }
  }

  // Check for RAG patterns
  let ragScore = 0;
  for (const pattern of QUERY_PATTERNS.rag) {
    if (pattern.test(lowerQuery)) {
      ragScore += 1;
    }
  }
  
  // Keyword-based scoring
  const analyticsKeywords = ['count', 'total', 'how many', 'statistics', 'metrics', 'data', 'analytics'];
  const contentKeywords = ['tell me', 'describe', 'find', 'show', 'help', 'recommend', 'similar'];
  
  analyticsKeywords.forEach(keyword => {
    if (lowerQuery.includes(keyword)) sqlScore += 0.5;
  });
  
  contentKeywords.forEach(keyword => {
    if (lowerQuery.includes(keyword)) ragScore += 0.5;
  });
  
  // Determine route based on scores
  if (sqlScore > ragScore && sqlScore >= 1) {
    return {
      route: 'text2sql',
      confidence: Math.min(0.9, 0.6 + (sqlScore * 0.1)),
      reasoning: `Analytics query detected (score: ${sqlScore} vs ${ragScore})`
    };
  } else if (ragScore > sqlScore && ragScore >= 1) {
    return {
      route: 'rag',
      confidence: Math.min(0.9, 0.6 + (ragScore * 0.1)),
      reasoning: `Content/semantic query detected (score: ${ragScore} vs ${sqlScore})`
    };
  } else if (sqlScore > 0 && ragScore > 0) {
    return {
      route: 'hybrid',
      confidence: 0.7,
      reasoning: 'Mixed query with both content and analytical elements'
    };
  } else {
    // Check if it's a very short query that might be conversational
    if (lowerQuery.length <= 20 && !lowerQuery.includes('find') && !lowerQuery.includes('show') && !lowerQuery.includes('how many')) {
      return {
        route: 'general_assistance',
        confidence: 0.8,
        reasoning: 'Short conversational query, likely general assistance'
      };
    }

    // Default to RAG for longer, unmatched queries
    return {
      route: 'rag',
      confidence: 0.5,
      reasoning: 'General query, defaulting to content search'
    };
  }
}

/**
 * LLM-based query classification for complex cases
 */
async function classifyQueryWithLLM(query: string): Promise<{ route: string; confidence: number; reasoning: string }> {
  try {
    const classificationPrompt = `You are a query router for an innovation platform. Classify this query into one of four categories:

1. GENERAL_ASSISTANCE: Simple greetings, thanks, basic conversational queries (hi, hello, how are you, thanks, etc.)
2. RAG: Content/semantic questions about profiles, posts, innovation, advice, recommendations
3. TEXT2SQL: Analytics, counts, metrics, data queries, statistics, trends
4. HYBRID: Complex questions needing both content and data analysis

Query: "${query}"

Respond with ONLY a JSON object in this format:
{
  "route": "rag|text2sql|hybrid",
  "confidence": 0.0-1.0,
  "reasoning": "brief explanation"
}`;

    const response = await fetch(`${DEEPSEEK_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [{ role: 'user', content: classificationPrompt }],
        temperature: 0.1,
        max_tokens: 150
      }),
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content.trim();
    
    // Parse JSON response
    const result = JSON.parse(content);
    
    return {
      route: result.route,
      confidence: Math.max(0.1, Math.min(1.0, result.confidence)),
      reasoning: result.reasoning || 'LLM classification'
    };

  } catch (error) {
    console.error('LLM classification failed:', error);
    // Fallback to pattern-based classification
    return classifyQueryByPatterns(query);
  }
}

/**
 * Get suggested approach based on route and context
 */
function getSuggestedApproach(route: string, query: string, userContext?: any): any {
  const approaches = {
    general_assistance: {
      primary_method: 'direct_response',
      context_needed: ['user_basic_info'],
      description: 'Provide direct conversational response without complex processing',
      skip_rag: true,
      skip_sql: true,
      response_type: 'conversational'
    },
    rag: {
      primary_method: 'semantic_search',
      context_needed: ['user_profiles', 'posts', 'platform_content'],
      description: 'Search platform content using semantic similarity'
    },
    text2sql: {
      primary_method: 'sql_generation',
      fallback_method: 'predefined_queries',
      context_needed: ['database_schema', 'table_relationships', 'user_permissions'],
      description: 'Generate SQL queries for data analysis'
    },
    hybrid: {
      primary_method: 'rag_then_sql',
      fallback_method: 'parallel_processing',
      context_needed: ['user_profiles', 'posts', 'database_schema', 'analytics_data'],
      description: 'Combine content search with data analysis'
    }
  };

  return approaches[route as keyof typeof approaches] || approaches.rag;
}

/**
 * Main Edge Function handler
 */
Deno.serve(async (req: Request) => {
  const startTime = Date.now();
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        }
      }
    );
  }

  try {
    const body = await req.json() as QueryRouterRequest;
    const { query, user_context, force_route, conversation_context } = body;

    if (!query || typeof query !== 'string') {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid query: string required'
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }

    console.log(`Routing query: "${query.substring(0, 100)}..." with context:`, {
      profile_type: user_context?.profile_type,
      profile_completion: user_context?.profile_completion,
      has_conversation_context: !!conversation_context
    });

    let classification;

    if (force_route) {
      // Use forced route
      classification = {
        route: force_route,
        confidence: 1.0,
        reasoning: 'Forced route specified by user',
        matching_strategy: 'hybrid'
      };
    } else {
      // Use enhanced context-aware classification
      classification = classifyQueryWithContext(query, user_context, conversation_context);

      // Use LLM for low-confidence cases
      if (classification.confidence < 0.7 && DEEPSEEK_API_KEY) {
        console.log('Low confidence, using LLM classification');
        const llmClassification = await classifyQueryWithLLM(query);
        // Merge LLM results with context-aware results
        classification = {
          ...llmClassification,
          matching_strategy: classification.matching_strategy || 'hybrid'
        };
      }
    }

    const suggestedApproach = getSuggestedApproach(classification.route, query, user_context);

    // Add matching strategy to suggested approach
    if (classification.matching_strategy) {
      suggestedApproach.matching_strategy = classification.matching_strategy;
    }

    const processingTime = Date.now() - startTime;

    const response: QueryRouterResponse = {
      success: true,
      route: classification.route as 'rag' | 'text2sql' | 'hybrid',
      confidence: classification.confidence,
      reasoning: classification.reasoning,
      suggested_approach: suggestedApproach,
      processing_time_ms: processingTime
    };

    console.log(`Query routed to: ${classification.route} (confidence: ${classification.confidence})`);

    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('Query router error:', error);
    
    const processingTime = Date.now() - startTime;
    
    return new Response(
      JSON.stringify({
        success: false,
        processing_time_ms: processingTime,
        error: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
