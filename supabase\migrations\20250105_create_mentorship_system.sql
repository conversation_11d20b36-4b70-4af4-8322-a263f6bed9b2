-- Enhanced Mentorship System Database Schema
-- This migration creates comprehensive mentorship management capabilities

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- MENTORSHIP REQUESTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.mentorship_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mentee_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  mentor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Request details
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  goals TEXT, -- What the mentee hopes to achieve
  preferred_duration VARCHAR(50), -- '1-3 months', '3-6 months', etc.
  preferred_frequency VARCHAR(50), -- 'Weekly', 'Bi-weekly', 'Monthly'
  preferred_format VARCHAR(50), -- 'Video calls', 'In-person', 'Mixed'
  
  -- Status and workflow
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'cancelled', 'completed')),
  mentor_response TEXT, -- Mentor's response message
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  responded_at TIMESTAMP WITH TIME ZONE,
  
  -- Constraints
  UNIQUE(mentee_id, mentor_id, created_at) -- Prevent duplicate requests on same day
);

-- =====================================================
-- MENTORSHIP SESSIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.mentorship_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mentorship_request_id UUID REFERENCES public.mentorship_requests(id) ON DELETE CASCADE,
  mentor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  mentee_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Session details
  title VARCHAR(255) NOT NULL,
  description TEXT,
  session_type VARCHAR(50) DEFAULT 'one-on-one' CHECK (session_type IN ('one-on-one', 'group', 'workshop', 'review')),
  
  -- Scheduling
  scheduled_start TIMESTAMP WITH TIME ZONE NOT NULL,
  scheduled_end TIMESTAMP WITH TIME ZONE NOT NULL,
  actual_start TIMESTAMP WITH TIME ZONE,
  actual_end TIMESTAMP WITH TIME ZONE,
  
  -- Meeting details
  meeting_link TEXT, -- Video call link
  meeting_location TEXT, -- Physical location or platform
  meeting_password VARCHAR(100),
  
  -- Session management
  status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled', 'no_show')),
  cancellation_reason TEXT,
  
  -- Session content
  agenda TEXT,
  notes TEXT, -- Session notes (visible to both parties)
  mentor_private_notes TEXT, -- Private notes for mentor only
  homework_assigned TEXT, -- Tasks assigned to mentee
  
  -- Follow-up
  next_session_id UUID REFERENCES public.mentorship_sessions(id),
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MENTORSHIP EVENTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.mentorship_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mentor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Event details
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  event_type VARCHAR(50) DEFAULT 'workshop' CHECK (event_type IN ('workshop', 'webinar', 'group_session', 'networking', 'masterclass')),
  
  -- Scheduling
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  timezone VARCHAR(50) DEFAULT 'UTC',
  
  -- Capacity and registration
  max_participants INTEGER DEFAULT 50,
  current_participants INTEGER DEFAULT 0,
  registration_required BOOLEAN DEFAULT true,
  registration_deadline TIMESTAMP WITH TIME ZONE,
  
  -- Meeting details
  meeting_link TEXT,
  meeting_location TEXT,
  meeting_password VARCHAR(100),
  
  -- Event management
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'cancelled', 'completed')),
  is_public BOOLEAN DEFAULT true,
  requires_approval BOOLEAN DEFAULT false,
  
  -- Content
  agenda TEXT,
  prerequisites TEXT,
  materials_needed TEXT,
  
  -- Pricing (if applicable)
  is_free BOOLEAN DEFAULT true,
  price DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'USD',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  published_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- MENTORSHIP EVENT REGISTRATIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.mentorship_event_registrations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_id UUID NOT NULL REFERENCES public.mentorship_events(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Registration details
  status VARCHAR(20) DEFAULT 'registered' CHECK (status IN ('registered', 'waitlisted', 'cancelled', 'attended', 'no_show')),
  registration_notes TEXT,
  
  -- Attendance tracking
  checked_in_at TIMESTAMP WITH TIME ZONE,
  checked_out_at TIMESTAMP WITH TIME ZONE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(event_id, user_id)
);

-- =====================================================
-- MENTORSHIP FEEDBACK TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.mentorship_feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID REFERENCES public.mentorship_sessions(id) ON DELETE CASCADE,
  event_id UUID REFERENCES public.mentorship_events(id) ON DELETE CASCADE,
  
  -- Feedback source
  reviewer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  reviewer_type VARCHAR(20) NOT NULL CHECK (reviewer_type IN ('mentor', 'mentee', 'participant')),
  
  -- Ratings (1-5 scale)
  overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 5),
  content_quality INTEGER CHECK (content_quality >= 1 AND content_quality <= 5),
  communication INTEGER CHECK (communication >= 1 AND communication <= 5),
  helpfulness INTEGER CHECK (helpfulness >= 1 AND helpfulness <= 5),
  
  -- Written feedback
  feedback_text TEXT,
  suggestions TEXT,
  
  -- Visibility
  is_public BOOLEAN DEFAULT false,
  is_testimonial BOOLEAN DEFAULT false,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints - ensure only one feedback per person per session/event
  UNIQUE(session_id, reviewer_id),
  UNIQUE(event_id, reviewer_id),
  CHECK ((session_id IS NOT NULL AND event_id IS NULL) OR (session_id IS NULL AND event_id IS NOT NULL))
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Mentorship Requests indexes
CREATE INDEX IF NOT EXISTS idx_mentorship_requests_mentee_id ON public.mentorship_requests(mentee_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_requests_mentor_id ON public.mentorship_requests(mentor_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_requests_status ON public.mentorship_requests(status);
CREATE INDEX IF NOT EXISTS idx_mentorship_requests_created_at ON public.mentorship_requests(created_at);

-- Mentorship Sessions indexes
CREATE INDEX IF NOT EXISTS idx_mentorship_sessions_mentor_id ON public.mentorship_sessions(mentor_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_sessions_mentee_id ON public.mentorship_sessions(mentee_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_sessions_scheduled_start ON public.mentorship_sessions(scheduled_start);
CREATE INDEX IF NOT EXISTS idx_mentorship_sessions_status ON public.mentorship_sessions(status);

-- Mentorship Events indexes
CREATE INDEX IF NOT EXISTS idx_mentorship_events_mentor_id ON public.mentorship_events(mentor_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_events_start_time ON public.mentorship_events(start_time);
CREATE INDEX IF NOT EXISTS idx_mentorship_events_status ON public.mentorship_events(status);
CREATE INDEX IF NOT EXISTS idx_mentorship_events_is_public ON public.mentorship_events(is_public);

-- Event Registrations indexes
CREATE INDEX IF NOT EXISTS idx_event_registrations_event_id ON public.mentorship_event_registrations(event_id);
CREATE INDEX IF NOT EXISTS idx_event_registrations_user_id ON public.mentorship_event_registrations(user_id);
CREATE INDEX IF NOT EXISTS idx_event_registrations_status ON public.mentorship_event_registrations(status);

-- Feedback indexes
CREATE INDEX IF NOT EXISTS idx_mentorship_feedback_session_id ON public.mentorship_feedback(session_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_feedback_event_id ON public.mentorship_feedback(event_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_feedback_reviewer_id ON public.mentorship_feedback(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_mentorship_feedback_is_public ON public.mentorship_feedback(is_public);
