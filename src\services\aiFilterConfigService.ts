/**
 * AI Filter Configuration Service
 * 
 * Provides AI-powered filter configurations for different tabs and contexts
 * in the virtual community. Each filter triggers text2sql queries instead
 * of traditional filtering.
 */

export interface AIFilter {
  key: string
  label: string
  icon: string
  color: string
  tooltip: string
}

export interface AIFilterConfig {
  quickFilters: AIFilter[]
  categoryFilters: AIFilter[]
  specializedFilters: AIFilter[]
}

export class AIFilterConfigService {
  
  /**
   * Get AI filter configuration for a specific tab
   */
  getFilterConfig(tab: string): AIFilterConfig {
    switch (tab) {
      case 'feed':
        return this.getFeedFilters()
      case 'profiles':
        return this.getProfileFilters()
      case 'events':
        return this.getEventFilters()
      case 'blog':
        return this.getBlogFilters()
      case 'marketplace':
        return this.getMarketplaceFilters()
      case 'groups':
        return this.getGroupFilters()
      default:
        return this.getDefaultFilters()
    }
  }

  /**
   * Feed tab filters
   */
  private getFeedFilters(): AIFilterConfig {
    return {
      quickFilters: [
        {
          key: 'recent_posts',
          label: 'Recent',
          icon: 'schedule',
          color: 'blue',
          tooltip: 'Find recent posts and updates'
        },
        {
          key: 'trending_posts',
          label: 'Trending',
          icon: 'trending_up',
          color: 'orange',
          tooltip: 'Find trending and popular posts'
        },
        {
          key: 'featured_posts',
          label: 'Featured',
          icon: 'star',
          color: 'amber',
          tooltip: 'Find featured and highlighted posts'
        }
      ],
      categoryFilters: [
        {
          key: 'innovation_content',
          label: 'Innovation',
          icon: 'lightbulb',
          color: 'amber',
          tooltip: 'Find innovation and startup content'
        },
        {
          key: 'collaboration_content',
          label: 'Collaboration',
          icon: 'handshake',
          color: 'green',
          tooltip: 'Find collaboration opportunities'
        },
        {
          key: 'funding_content',
          label: 'Funding',
          icon: 'attach_money',
          color: 'teal',
          tooltip: 'Find funding and investment content'
        },
        {
          key: 'technology_content',
          label: 'Technology',
          icon: 'computer',
          color: 'blue',
          tooltip: 'Find technology-related content'
        }
      ],
      specializedFilters: [
        {
          key: 'startup_journey',
          label: 'Startup Journey',
          icon: 'rocket_launch',
          color: 'purple',
          tooltip: 'Find content about startup experiences'
        },
        {
          key: 'networking_opportunities',
          label: 'Networking',
          icon: 'people',
          color: 'indigo',
          tooltip: 'Find networking opportunities'
        }
      ]
    }
  }

  /**
   * Profiles tab filters
   */
  private getProfileFilters(): AIFilterConfig {
    return {
      quickFilters: [
        {
          key: 'active_profiles',
          label: 'Active',
          icon: 'online_prediction',
          color: 'green',
          tooltip: 'Find recently active profiles'
        },
        {
          key: 'verified_profiles',
          label: 'Verified',
          icon: 'verified',
          color: 'blue',
          tooltip: 'Find verified and trusted profiles'
        }
      ],
      categoryFilters: [
        {
          key: 'find_mentors',
          label: 'Mentors',
          icon: 'school',
          color: 'blue',
          tooltip: 'Find experienced mentors'
        },
        {
          key: 'find_investors',
          label: 'Investors',
          icon: 'trending_up',
          color: 'green',
          tooltip: 'Find investors and funding partners'
        },
        {
          key: 'find_entrepreneurs',
          label: 'Entrepreneurs',
          icon: 'rocket_launch',
          color: 'orange',
          tooltip: 'Find fellow entrepreneurs'
        },
        {
          key: 'find_researchers',
          label: 'Researchers',
          icon: 'science',
          color: 'purple',
          tooltip: 'Find researchers and academics'
        }
      ],
      specializedFilters: [
        {
          key: 'industry_experts',
          label: 'Industry Experts',
          icon: 'expert_mode',
          color: 'indigo',
          tooltip: 'Find industry experts and thought leaders'
        },
        {
          key: 'local_connections',
          label: 'Local Network',
          icon: 'location_on',
          color: 'red',
          tooltip: 'Find people in your area'
        }
      ]
    }
  }

  /**
   * Events tab filters
   */
  private getEventFilters(): AIFilterConfig {
    return {
      quickFilters: [
        {
          key: 'upcoming_events',
          label: 'Upcoming',
          icon: 'event',
          color: 'blue',
          tooltip: 'Find upcoming events'
        },
        {
          key: 'free_events',
          label: 'Free',
          icon: 'volunteer_activism',
          color: 'green',
          tooltip: 'Find free events and workshops'
        },
        {
          key: 'online_events',
          label: 'Online',
          icon: 'computer',
          color: 'purple',
          tooltip: 'Find virtual and online events'
        }
      ],
      categoryFilters: [
        {
          key: 'find_workshops',
          label: 'Workshops',
          icon: 'build',
          color: 'blue',
          tooltip: 'Find hands-on workshops'
        },
        {
          key: 'find_conferences',
          label: 'Conferences',
          icon: 'groups',
          color: 'purple',
          tooltip: 'Find conferences and summits'
        },
        {
          key: 'find_networking_events',
          label: 'Networking',
          icon: 'people',
          color: 'green',
          tooltip: 'Find networking events'
        },
        {
          key: 'find_competitions',
          label: 'Competitions',
          icon: 'emoji_events',
          color: 'amber',
          tooltip: 'Find competitions and challenges'
        }
      ],
      specializedFilters: [
        {
          key: 'mentorship_events',
          label: 'Mentorship',
          icon: 'school',
          color: 'indigo',
          tooltip: 'Find mentorship-related events'
        },
        {
          key: 'funding_events',
          label: 'Funding Events',
          icon: 'attach_money',
          color: 'teal',
          tooltip: 'Find funding and investment events'
        }
      ]
    }
  }

  /**
   * Blog tab filters
   */
  private getBlogFilters(): AIFilterConfig {
    return {
      quickFilters: [
        {
          key: 'latest_articles',
          label: 'Latest',
          icon: 'schedule',
          color: 'blue',
          tooltip: 'Find latest articles and posts'
        },
        {
          key: 'popular_articles',
          label: 'Popular',
          icon: 'trending_up',
          color: 'orange',
          tooltip: 'Find popular and trending articles'
        },
        {
          key: 'quick_reads',
          label: 'Quick Reads',
          icon: 'timer',
          color: 'green',
          tooltip: 'Find short articles (under 5 minutes)'
        }
      ],
      categoryFilters: [
        {
          key: 'find_insights',
          label: 'Insights',
          icon: 'psychology',
          color: 'blue',
          tooltip: 'Find industry insights and analysis'
        },
        {
          key: 'find_trends',
          label: 'Trends',
          icon: 'trending_up',
          color: 'orange',
          tooltip: 'Find trending topics and analysis'
        },
        {
          key: 'find_success_stories',
          label: 'Success Stories',
          icon: 'star',
          color: 'amber',
          tooltip: 'Find success stories and case studies'
        },
        {
          key: 'find_research',
          label: 'Research',
          icon: 'science',
          color: 'purple',
          tooltip: 'Find research articles and studies'
        }
      ],
      specializedFilters: [
        {
          key: 'startup_guides',
          label: 'Startup Guides',
          icon: 'rocket_launch',
          color: 'indigo',
          tooltip: 'Find startup guides and how-tos'
        },
        {
          key: 'funding_insights',
          label: 'Funding Insights',
          icon: 'attach_money',
          color: 'teal',
          tooltip: 'Find funding and investment insights'
        }
      ]
    }
  }

  /**
   * Marketplace tab filters
   */
  private getMarketplaceFilters(): AIFilterConfig {
    return {
      quickFilters: [
        {
          key: 'new_listings',
          label: 'New',
          icon: 'fiber_new',
          color: 'blue',
          tooltip: 'Find new listings and offers'
        },
        {
          key: 'free_items',
          label: 'Free',
          icon: 'volunteer_activism',
          color: 'green',
          tooltip: 'Find free items and resources'
        },
        {
          key: 'local_listings',
          label: 'Local',
          icon: 'location_on',
          color: 'red',
          tooltip: 'Find local listings and services'
        }
      ],
      categoryFilters: [
        {
          key: 'find_products',
          label: 'Products',
          icon: 'inventory',
          color: 'blue',
          tooltip: 'Find products and physical items'
        },
        {
          key: 'find_services',
          label: 'Services',
          icon: 'handyman',
          color: 'green',
          tooltip: 'Find services and professional offerings'
        },
        {
          key: 'find_jobs',
          label: 'Jobs',
          icon: 'work',
          color: 'purple',
          tooltip: 'Find job opportunities'
        },
        {
          key: 'find_equipment',
          label: 'Equipment',
          icon: 'precision_manufacturing',
          color: 'orange',
          tooltip: 'Find equipment and tools'
        }
      ],
      specializedFilters: [
        {
          key: 'startup_resources',
          label: 'Startup Resources',
          icon: 'rocket_launch',
          color: 'indigo',
          tooltip: 'Find resources for startups'
        },
        {
          key: 'tech_equipment',
          label: 'Tech Equipment',
          icon: 'computer',
          color: 'teal',
          tooltip: 'Find technology equipment'
        }
      ]
    }
  }

  /**
   * Groups tab filters
   */
  private getGroupFilters(): AIFilterConfig {
    return {
      quickFilters: [
        {
          key: 'active_groups',
          label: 'Active',
          icon: 'online_prediction',
          color: 'green',
          tooltip: 'Find active and engaged groups'
        },
        {
          key: 'new_groups',
          label: 'New',
          icon: 'fiber_new',
          color: 'blue',
          tooltip: 'Find newly created groups'
        },
        {
          key: 'open_groups',
          label: 'Open to Join',
          icon: 'group_add',
          color: 'purple',
          tooltip: 'Find groups open for new members'
        }
      ],
      categoryFilters: [
        {
          key: 'find_fintech_groups',
          label: 'FinTech',
          icon: 'account_balance',
          color: 'blue',
          tooltip: 'Find financial technology groups'
        },
        {
          key: 'find_agritech_groups',
          label: 'AgriTech',
          icon: 'agriculture',
          color: 'green',
          tooltip: 'Find agricultural technology groups'
        },
        {
          key: 'find_healthtech_groups',
          label: 'HealthTech',
          icon: 'medical_services',
          color: 'red',
          tooltip: 'Find health technology groups'
        },
        {
          key: 'find_edtech_groups',
          label: 'EdTech',
          icon: 'school',
          color: 'purple',
          tooltip: 'Find education technology groups'
        }
      ],
      specializedFilters: [
        {
          key: 'innovation_hubs',
          label: 'Innovation Hubs',
          icon: 'lightbulb',
          color: 'amber',
          tooltip: 'Find innovation and startup hubs'
        },
        {
          key: 'research_groups',
          label: 'Research Groups',
          icon: 'science',
          color: 'indigo',
          tooltip: 'Find research and academic groups'
        }
      ]
    }
  }

  /**
   * Default filters for unknown tabs
   */
  private getDefaultFilters(): AIFilterConfig {
    return {
      quickFilters: [
        {
          key: 'recent_content',
          label: 'Recent',
          icon: 'schedule',
          color: 'blue',
          tooltip: 'Find recent content'
        },
        {
          key: 'popular_content',
          label: 'Popular',
          icon: 'trending_up',
          color: 'orange',
          tooltip: 'Find popular content'
        }
      ],
      categoryFilters: [
        {
          key: 'innovation_search',
          label: 'Innovation',
          icon: 'lightbulb',
          color: 'amber',
          tooltip: 'Search innovation content'
        },
        {
          key: 'technology_search',
          label: 'Technology',
          icon: 'computer',
          color: 'blue',
          tooltip: 'Search technology content'
        }
      ],
      specializedFilters: []
    }
  }
}

// Export singleton instance
export const aiFilterConfigService = new AIFilterConfigService()
