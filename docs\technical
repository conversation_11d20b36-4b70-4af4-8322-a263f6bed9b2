# Content Security Policy (CSP) Troubleshooting Guide

This document provides guidance on how to troubleshoot and fix Content Security Policy (CSP) issues in the ZbInnovation platform.

## Current Status

We've temporarily set a very permissive CSP to ensure the site works correctly in production. This is not ideal for security, but it allows us to identify exactly what resources need to be allowed.

## Common CSP Violations

Based on the console errors, we've identified the following resources that need to be allowed:

1. <PERSON><PERSON><PERSON> from:
   - `https://code.iconify.design/3/3.1.0/iconify.min.js`
   - `https://*.same-assets.com`

2. Fonts from:
   - `https://fonts.gstatic.com`

3. Images from:
   - `https://ext.same-assets.com/4258758033/1285344749.png`

4. Other issues:
   - Reference error related to `feature-profile-downlevel.js`
   - `setTimeout` handler issues

## How to Fix CSP Issues

### Option 1: Use the Permissive CSP (Temporary Solution)

We've updated the `.htaccess` file to use a very permissive CSP:

```
Header always set Content-Security-Policy "default-src * 'unsafe-inline' 'unsafe-eval' data: blob:;"
```

This allows all resources to load but provides minimal security. Use this only for debugging.

### Option 2: Use the Comprehensive CSP (Recommended)

Once you've identified all the resources that need to be allowed, update the CSP in both:
- `public/.htaccess`
- `scripts/ensure-apache-security.js`

Use the following CSP as a starting point:

```
Header always set Content-Security-Policy "default-src 'self'; \
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://*.supabase.co https://code.iconify.design https://*.same-assets.com; \
  connect-src 'self' https://*.supabase.co https://api.sendgrid.com https://*.same-assets.com; \
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; \
  font-src 'self' https://fonts.gstatic.com data:; \
  img-src 'self' data: https://*.supabase.co https://*.same-assets.com https://ext.same-assets.com; \
  frame-src 'self' https://*.supabase.co; \
  media-src 'self' data:; \
  object-src 'none'; \
  worker-src 'self' blob:; \
  manifest-src 'self'; \
  base-uri 'self'; \
  form-action 'self'; \
  frame-ancestors 'self';"
```

## Manual CSP Configuration

To configure CSP settings manually:

1. **Edit .htaccess directly**: Update the CSP headers in the `public/.htaccess` file
2. **Test in browser**: Use browser developer tools to check for CSP violations
3. **Validate configuration**: Ensure all required resources are properly allowed

## Testing CSP Changes

1. Create a test HTML file with your proposed CSP
2. Open it in a browser and check the console for CSP violations
3. Adjust the CSP based on the violations
4. Repeat until no violations occur

## Apache Server Configuration

If you're still seeing CSP issues after updating the `.htaccess` file, check your Apache server configuration:

1. Make sure the server is not overriding your CSP headers
2. Check if `mod_headers` is enabled
3. Verify that `.htaccess` files are being processed (check `AllowOverride` settings)

## References

- [MDN Content Security Policy](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)
- [CSP Evaluator](https://csp-evaluator.withgoogle.com/)
- [CSP Builder](https://report-uri.com/home/<USER>
