<template>
  <q-card 
    class="featured-card cursor-pointer"
    :class="{ 'featured-card--hover': !loading }"
    @click="handleClick"
  >
    <!-- Image Container -->
    <div class="featured-image-container">
      <q-img
        :src="cardImage"
        :ratio="16/10"
        class="featured-image"
        loading="lazy"
        @error="handleImageError"
      >
        <template v-slot:error>
          <div class="absolute-full flex flex-center bg-gradient-to-br from-grey-4 to-grey-6">
            <q-icon :name="fallbackIcon" size="3em" color="white" />
          </div>
        </template>
        
        <!-- Type Badge -->
        <div class="absolute-top-left q-ma-sm">
          <q-chip
            :color="typeColor"
            text-color="white"
            :icon="typeIcon"
            size="sm"
            class="type-badge"
          >
            {{ typeLabel }}
          </q-chip>
        </div>

        <!-- Overlay Content -->
        <div class="absolute-bottom featured-overlay">
          <div class="featured-content q-pa-md">
            <!-- Title -->
            <h4 class="featured-title text-white q-mb-xs">
              {{ cardTitle }}
            </h4>

            <!-- Subtitle/Description -->
            <p class="featured-subtitle text-white q-mb-sm">
              {{ cardSubtitle }}
            </p>

            <!-- Meta Information -->
            <div class="featured-meta row items-center">
              <q-icon
                v-if="metaIcon"
                :name="metaIcon"
                size="sm"
                color="white"
                class="q-mr-xs"
              />
              <span class="text-caption text-white">{{ metaText }}</span>
            </div>

            <!-- Additional marketplace info -->
            <div v-if="type === 'marketplace'" class="marketplace-details q-mt-xs">
              <div v-if="marketplaceCategory" class="text-caption text-white opacity-80 q-mb-xs">
                <q-icon name="category" size="xs" class="q-mr-xs" />
                {{ marketplaceCategory }}
              </div>
              <div v-if="marketplaceLocation" class="text-caption text-white opacity-80 q-mb-xs">
                <q-icon name="location_on" size="xs" class="q-mr-xs" />
                {{ marketplaceLocation }}
              </div>
              <div v-if="marketplaceType" class="text-caption text-white opacity-80">
                <q-icon name="label" size="xs" class="q-mr-xs" />
                Type: {{ marketplaceType }}
              </div>
            </div>
          </div>
        </div>

        <!-- Hover Effect Overlay -->
        <div class="hover-overlay absolute-full flex flex-center">
          <q-btn
            round
            color="white"
            text-color="primary"
            icon="arrow_forward"
            size="md"
            class="hover-button"
          />
        </div>
      </q-img>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

// Props
interface Props {
  item: any;
  type: 'blog' | 'marketplace' | 'profile' | 'event' | 'group';
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  click: [type: string, item: any];
}>();

// State
const loading = ref(false);
const imageError = ref(false);

// Computed
const cardImage = computed(() => {
  if (imageError.value) {
    return generateFallbackImage();
  }

  switch (props.type) {
    case 'blog':
      return props.item.featuredImage || props.item.image || props.item.media_urls?.featured || generateFallbackImage();
    case 'marketplace':
      return props.item.images?.[0] || props.item.image || props.item.media_urls?.primary || generateFallbackImage();
    case 'profile':
      return props.item.profilePicture || props.item.avatar || props.item.image || generateFallbackImage();
    case 'event':
      return props.item.image || props.item.media_urls?.banner || generateFallbackImage();
    case 'group':
      return props.item.image || props.item.banner || generateFallbackImage();
    default:
      return generateFallbackImage();
  }
});

const cardTitle = computed(() => {
  switch (props.type) {
    case 'blog':
      return props.item.blogTitle || props.item.title || 'Featured Article';
    case 'marketplace':
      return props.item.title || props.item.name || 'Featured Item';
    case 'profile':
      return props.item.displayName || props.item.name || `${props.item.firstName} ${props.item.lastName}`.trim() || 'Featured Profile';
    case 'event':
      return props.item.title || props.item.name || 'Featured Event';
    case 'group':
      return props.item.name || props.item.title || 'Featured Group';
    default:
      return 'Featured Content';
  }
});

const cardSubtitle = computed(() => {
  switch (props.type) {
    case 'blog':
      return props.item.excerpt || props.item.description || 'Read this featured article';
    case 'marketplace':
      return props.item.description || props.item.summary || 'Explore this opportunity';
    case 'profile':
      return props.item.profileType || props.item.role || props.item.title || 'Connect with this member';
    case 'event':
      return props.item.description || props.item.summary || 'Join this event';
    case 'group':
      return props.item.description || props.item.summary || 'Join this community';
    default:
      return 'Discover more';
  }
});

const typeColor = computed(() => {
  switch (props.type) {
    case 'blog': return 'blue';
    case 'marketplace': return 'green';
    case 'profile': return 'purple';
    case 'event': return 'orange';
    case 'group': return 'teal';
    default: return 'primary';
  }
});

const typeIcon = computed(() => {
  switch (props.type) {
    case 'blog': return 'article';
    case 'marketplace': return 'storefront';
    case 'profile': return 'person';
    case 'event': return 'event';
    case 'group': return 'groups';
    default: return 'star';
  }
});

const typeLabel = computed(() => {
  switch (props.type) {
    case 'blog': return 'Blog';
    case 'marketplace': return 'Market';
    case 'profile': return 'Profile';
    case 'event': return 'Event';
    case 'group': return 'Group';
    default: return 'Featured';
  }
});

const fallbackIcon = computed(() => {
  return typeIcon.value;
});

const metaIcon = computed(() => {
  switch (props.type) {
    case 'blog': return 'schedule';
    case 'marketplace': return 'attach_money';
    case 'profile': return 'location_on';
    case 'event': return 'calendar_today';
    case 'group': return 'people';
    default: return null;
  }
});

const metaText = computed(() => {
  switch (props.type) {
    case 'blog':
      return formatDate(props.item.publishedAt || props.item.created_at);
    case 'marketplace':
      return props.item.price || 'Contact for pricing';
    case 'profile':
      return props.item.location || props.item.city || 'Location not specified';
    case 'event':
      return formatDate(props.item.date || props.item.startDate);
    case 'group':
      return `${props.item.memberCount || 0} members`;
    default:
      return '';
  }
});

// Marketplace-specific computed properties
const marketplaceCategory = computed(() => {
  if (props.type !== 'marketplace') return null;
  return props.item.category || null;
});

const marketplaceLocation = computed(() => {
  if (props.type !== 'marketplace') return null;
  return props.item.location || null;
});

const marketplaceType = computed(() => {
  if (props.type !== 'marketplace') return null;
  return props.item.subType || props.item.type || null;
});

// Methods
function generateFallbackImage(): string {
  const colors = ['4F46E5', '059669', '7C3AED', 'EA580C', '0891B2'];
  const color = colors[Math.abs(hashCode(cardTitle.value)) % colors.length];
  return `https://via.placeholder.com/400x250/${color}/FFFFFF?text=${encodeURIComponent(typeLabel.value)}`;
}

function hashCode(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash;
}

function formatDate(dateString?: string): string {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  });
}

function handleImageError() {
  imageError.value = true;
}

function handleClick() {
  emit('click', props.type, props.item);
}
</script>

<style scoped>
.featured-card {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 200px;
}

.featured-card--hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.featured-image-container {
  position: relative;
  height: 100%;
}

.featured-image {
  height: 100%;
}

.type-badge {
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.2) !important;
}

.featured-overlay {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, transparent 100%);
  backdrop-filter: blur(1px);
}

.featured-title {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.featured-subtitle {
  font-size: 0.875rem;
  line-height: 1.3;
  margin: 0;
  opacity: 0.9;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.featured-meta {
  font-size: 0.75rem;
  opacity: 0.8;
}

.hover-overlay {
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(2px);
}

.featured-card--hover:hover .hover-overlay {
  opacity: 1;
}

.hover-button {
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.featured-card--hover:hover .hover-button {
  transform: scale(1);
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .featured-card {
    height: 180px;
  }
  
  .featured-title {
    font-size: 0.9rem;
  }
  
  .featured-subtitle {
    font-size: 0.8rem;
  }
}
</style>
