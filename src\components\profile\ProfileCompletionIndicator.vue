<template>
  <div class="profile-completion-indicator q-pa-md">
    <!-- No profile exists yet - Show guidance message -->
    <div v-if="!currentProfile || !currentProfile.profile_type" class="q-mb-md">
      <div class="text-h6 text-weight-medium">Create Your Profile</div>
      <p class="text-body2 q-mt-sm">Creating a profile is the first step to connect with other innovators and access all features of the ZbInnovation.</p>
      <div class="text-caption text-grey-8 q-mt-sm">
        <ul class="q-mb-none">
          <li>Step 1: Choose your profile type (Innovator, Investor, Mentor, etc.)</li>
          <li>Step 2: Fill in your personal details (name, email, phone, etc.)</li>
          <li>Step 3: Complete profile-specific information</li>
        </ul>
      </div>
    </div>

    <!-- Profile exists - Show completion percentage -->
    <div v-else>
      <div class="row items-center q-mb-md">
        <div class="col">
          <div class="text-h6 text-weight-medium">Profile Completion</div>
        </div>
        <div class="col-auto">
          <div class="text-subtitle1 text-weight-medium q-mr-md text-primary">
            Your profile is incomplete
          </div>
        </div>
      </div>

      <q-linear-progress
        :value="completionPercentage / 100"
        :color="progressColor"
        :size="size"
        rounded
        track-color="grey-3"
        class="progress-bar"
      />
    </div>

    <div class="row items-center q-mt-sm">
      <!-- Show steps badge only for existing profiles -->
      <div v-if="showSteps && currentProfile && currentProfile.profile_type" class="col-auto q-mr-md">
        <q-badge :color="progressColor" :label="`${completedSteps}/${totalSteps} steps`" />
      </div>

      <div class="col" :class="{ 'text-right': currentProfile && currentProfile.profile_type, 'text-center': !currentProfile || !currentProfile.profile_type }">
        <!-- No profile exists yet - Show prominent create button -->
        <q-btn
          v-if="!currentProfile || !currentProfile.profile_type"
          :color="progressColor"
          label="CREATE YOUR PROFILE"
          @click="handleButtonClick"
          :disable="!currentProfile"
          size="md"
          class="q-py-sm dashboard-action-btn"
          outline
        >
          <template v-slot:prepend>
            <unified-icon name="person_add" class="q-mr-xs" />
          </template>
        </q-btn>

        <!-- Profile exists - Show appropriate action button -->
        <q-btn
          v-else
          :color="progressColor"
          :label="completionPercentage < 100 ? 'Complete Your Profile' : 'View Profile'"
          @click="handleButtonClick"
          size="md"
          outline
          class="dashboard-action-btn"
        >
          <template v-slot:prepend>
            <unified-icon :name="completionPercentage < 100 ? 'edit' : 'visibility'" class="q-mr-xs" />
          </template>
        </q-btn>
      </div>
    </div>

    <!-- Show missing fields only for existing profiles with missing information -->
    <div v-if="currentProfile && currentProfile.profile_type && showMissingFields && missingFields.length > 0" class="missing-fields q-mt-md bg-grey-2 q-pa-sm rounded-borders">
      <div class="text-subtitle2 text-weight-medium q-mb-xs">
        <icons name="error" size="18px" class="q-mr-xs" /> Missing Information
      </div>
      <div class="text-caption text-grey-8">
        <q-chip
          v-for="field in missingFields"
          :key="field"
          size="sm"
          dense
          :color="progressColor"
          text-color="white"
          class="q-ma-xs"
        >
          {{ formatFieldName(field) }}
        </q-chip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, inject } from 'vue'
import { useRouter } from 'vue-router'
import { useProfileStore } from '@/stores/profile'
import { useGlobalServicesStore } from '@/stores/globalServices'
import { useUnifiedProfileQuestions } from '@/services/profileQuestions/index'
import UnifiedIcon from '../ui/UnifiedIcon.vue'
import Icons from '../ui/Icons.vue'

const props = defineProps({
  // If provided, will use this profile instead of the current profile from the store
  profile: {
    type: Object,
    default: null
  },
  // If provided, will use this data for specialized profile completion calculation
  profileData: {
    type: Object,
    default: () => ({})
  },
  // Show steps badge
  showSteps: {
    type: Boolean,
    default: false
  },
  // Total steps (only used if showSteps is true)
  totalSteps: {
    type: Number,
    default: 3
  },
  // Show missing fields
  showMissingFields: {
    type: Boolean,
    default: false
  },
  // Color of the progress bar
  color: {
    type: String,
    default: 'primary'
  },
  // Size of the progress bar
  size: {
    type: String,
    default: '10px'
  }
})

const profileStore = useProfileStore()
const globalServices = useGlobalServicesStore()
const { calculateCompletion, getMissingFields } = globalServices.profileCompletionService

// Use the provided profile or fall back to the current profile from the store
const currentProfile = computed(() => props.profile || profileStore.currentProfile)

// Calculate completion percentage
const completionPercentage = computed(() => {
  if (!currentProfile.value) return 0

  // Use the stored profile_completion value for consistency
  return currentProfile.value.profile_completion || 0
})

// Calculate completed steps based on completion percentage
const completedSteps = computed(() => {
  return Math.ceil((completionPercentage.value / 100) * props.totalSteps)
})

// Get missing fields
const missingFields = computed(() => {
  if (!currentProfile.value) return []
  return getMissingFields(currentProfile.value, props.profileData)
})

// Determine color based on completion percentage
const progressColor = computed(() => {
  if (completionPercentage.value < 30) return 'primary'
  if (completionPercentage.value < 70) return 'primary'
  if (completionPercentage.value < 100) return 'primary'
  return 'primary'
})

// Get router for navigation
const router = useRouter()

// Get the openCreateProfileDialog function from the parent component
const openCreateProfileDialog = inject('openCreateProfileDialog', null)

// Handle button click based on profile state
function handleButtonClick() {
  try {
    if (currentProfile.value && currentProfile.value.user_id) {
      // If we have a current profile, navigate to the profile edit route with the profile ID
      console.log('Navigating to profile-edit with ID:', currentProfile.value.user_id)
      router.push({
        name: 'profile-edit',
        params: { id: currentProfile.value.user_id }
      })
    } else {
      // If no profile exists, navigate to the profile create route
      console.log('Navigating to profile-create')
      router.push({ name: 'profile-create' })
    }
  } catch (error) {
    console.error('Navigation error:', error)
  }
}

// Format field names for display
function formatFieldName(field: string): string {
  // Convert snake_case to Title Case with spaces
  return field
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}
</script>

<style scoped>
.profile-completion-indicator {
  width: 100%;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.progress-bar {
  height: 10px;
  border-radius: 5px;
  overflow: hidden;
}

.missing-fields {
  max-width: 100%;
  border-radius: 4px;
}

.q-chip {
  margin: 2px;
}

/* Dashboard action button styling for consistency */
.dashboard-action-btn {
  min-height: 44px;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dashboard-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>
