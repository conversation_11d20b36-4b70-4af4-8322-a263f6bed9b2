/**
 * Mentorship session reminder email template
 */

import { extractNameFromEmail } from '../../../shared/email-templates/template-utils.ts'

/**
 * Generates a mentorship session reminder email
 * @param email The participant's email
 * @param participantName The name of the email recipient
 * @param otherParticipantName The name of the other participant (mentor/mentee)
 * @param sessionTitle The title of the session
 * @param sessionDate The formatted date of the session
 * @param sessionTime The formatted time of the session
 * @param meetingLink The meeting link (if available)
 * @param meetingPlatform The meeting platform
 * @param sessionUrl The URL to view session details
 * @param firstName Optional first name of the recipient
 * @returns HTML and subject for the email
 */
export function generateMentorshipSessionReminderEmail(
  email: string,
  participantName: string,
  otherParticipantName: string,
  sessionTitle: string,
  sessionDate: string,
  sessionTime: string,
  meetingLink: string,
  meetingPlatform: string,
  sessionUrl: string,
  firstName?: string
): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined;
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,';

  const subject = `Reminder: Mentorship session with ${otherParticipantName} tomorrow`;

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="ZB Innovation Hub" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <div style="text-align: center; margin: 24px 0;">
        <div style="background-color: #0D8A3E; color: white; padding: 16px; border-radius: 8px; display: inline-block;">
          <h2 style="margin: 0; font-size: 24px;">⏰ Session Reminder</h2>
        </div>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        This is a friendly reminder about your upcoming mentorship session with <strong>${otherParticipantName}</strong>.
      </p>

      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 24px 0;">
        <h3 style="color: #0D8A3E; margin: 0 0 16px 0; font-size: 18px;">${sessionTitle}</h3>
        
        <div style="margin-bottom: 12px;">
          <strong>📅 Date:</strong> ${sessionDate}
        </div>
        
        <div style="margin-bottom: 12px;">
          <strong>🕐 Time:</strong> ${sessionTime}
        </div>
        
        <div style="margin-bottom: 12px;">
          <strong>👥 With:</strong> ${otherParticipantName}
        </div>
        
        ${meetingPlatform !== 'in-person' ? `
          <div style="margin-bottom: 12px;">
            <strong>💻 Platform:</strong> ${meetingPlatform}
          </div>
        ` : `
          <div style="margin-bottom: 12px;">
            <strong>📍 Type:</strong> In-person meeting
          </div>
        `}
      </div>

      ${meetingLink ? `
        <div style="text-align: center; margin: 32px 0;">
          <a href="${meetingLink}" style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
            Join Meeting
          </a>
        </div>
      ` : ''}

      <div style="text-align: center; margin: 16px 0;">
        <a href="${sessionUrl}" style="background-color: transparent; color: #0D8A3E; padding: 8px 16px; text-decoration: none; border: 2px solid #0D8A3E; border-radius: 6px; font-weight: bold; display: inline-block;">
          View Session Details
        </a>
      </div>

      <div style="background-color: #e8f5e8; padding: 16px; border-radius: 6px; margin: 24px 0;">
        <p style="margin: 0; font-size: 14px; color: #0D8A3E;">
          <strong>💡 Preparation Tips:</strong>
        </p>
        <ul style="margin: 8px 0 0 0; padding-left: 20px; font-size: 14px; color: #0D8A3E;">
          <li>Review your goals for this session</li>
          <li>Prepare any questions you want to discuss</li>
          <li>Test your technology if it's a virtual meeting</li>
          <li>Have a notebook ready for taking notes</li>
        </ul>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5; color: #666;">
        If you need to reschedule or cancel this session, please do so as soon as possible to respect everyone's time.
      </p>

      <div style="border-top: 1px solid #eee; padding-top: 24px; margin-top: 32px;">
        <p style="margin-bottom: 8px; line-height: 1.5;">
          Best regards,<br>
          The ZB Innovation Hub Team
        </p>
      </div>

      <div style="margin-top: 24px; padding: 16px; background-color: #f8f9fa; border-radius: 6px; font-size: 12px; color: #666;">
        <p style="margin: 0; text-align: center;">
          © ${new Date().getFullYear()} ZB Innovation Hub. All rights reserved.<br>
          This email was sent to ${email}
        </p>
        <p style="margin: 8px 0 0 0; text-align: center;">
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Manage Email Preferences</a> | 
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Unsubscribe</a>
        </p>
      </div>
    </div>
  `;

  return { html, subject };
}
