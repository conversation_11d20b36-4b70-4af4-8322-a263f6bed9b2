import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/lib/supabase';
import { useNotificationStore } from './notifications';
import { applyMatchmakingMigration } from '@/lib/applyMatchmakingMigration';

// Note: fs and path imports removed for browser compatibility

export const useDatabaseStore = defineStore('database', () => {
  // State
  const loading = ref(false);
  const error = ref<string | null>(null);
  const migrationStatus = ref<{
    postsViewFixed: boolean;
    connectionsTableFixed: boolean;
    matchmakingMigrationApplied: boolean;
  }>({
    postsViewFixed: false,
    connectionsTableFixed: false,
    matchmakingMigrationApplied: false
  });

  // Notification store
  const notificationStore = useNotificationStore();

  // Getters
  const isLoading = computed(() => loading.value);
  const getError = computed(() => error.value);
  const getMigrationStatus = computed(() => migrationStatus.value);

  /**
   * Create the user_messages table if it doesn't exist
   */
  async function createMessagingTable(): Promise<{
    success: boolean;
    error?: any;
  }> {
    try {
      loading.value = true;
      error.value = null;

      // Check if the user_messages table exists
      const { error: tableCheckError } = await supabase
        .from('user_messages')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      // If the table exists, return success
      if (!tableCheckError) {
        return { success: true };
      }

      // Create the user_messages table
      const { error: createError } = await supabase.rpc('exec_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS public.user_messages (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            sender_id UUID NOT NULL REFERENCES auth.users(id),
            recipient_id UUID NOT NULL REFERENCES auth.users(id),
            content TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Add indexes for better performance
          CREATE INDEX IF NOT EXISTS idx_user_messages_sender_id ON public.user_messages(sender_id);
          CREATE INDEX IF NOT EXISTS idx_user_messages_recipient_id ON public.user_messages(recipient_id);
          CREATE INDEX IF NOT EXISTS idx_user_messages_created_at ON public.user_messages(created_at);
        `
      });

      if (createError) {
        throw createError;
      }

      notificationStore.success('Messaging table created successfully!');
      return { success: true };
    } catch (err: any) {
      error.value = err.message || 'Failed to create messaging table';
      notificationStore.error(`Error creating messaging table: ${error.value}`);
      return {
        success: false,
        error: err
      };
    } finally {
      loading.value = false;
    }
  }

  /**
   * Apply database fixes
   */
  async function fixDatabaseIssues(): Promise<{
    success: boolean;
    postsViewFixed: boolean;
    connectionsTableFixed: boolean;
    messagingTableFixed: boolean;
    error?: any;
  }> {
    try {
      loading.value = true;
      error.value = null;

      // Read the migration file
      // Migration SQL will be handled by Supabase migrations
      const migrationSql = `-- Database fixes handled by Supabase migrations`;

      // Execute the migration
      const { error: migrationError } = await supabase.rpc('exec_sql', {
        sql: migrationSql
      });

      if (migrationError) {
        throw migrationError;
      }

      // Check if the posts_with_authors view was created successfully
      const { error: viewCheckError } = await supabase
        .from('posts_with_authors')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      const postsViewFixed = !viewCheckError;

      // Check if the user_connections table was created successfully
      const { error: tableCheckError } = await supabase
        .from('user_connections')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      const connectionsTableFixed = !tableCheckError;

      // Create the messaging table
      const { success: messagingTableFixed, error: messagingError } = await createMessagingTable();

      // Update migration status
      migrationStatus.value = {
        postsViewFixed,
        connectionsTableFixed,
        matchmakingMigrationApplied: true
      };

      if (postsViewFixed && connectionsTableFixed && messagingTableFixed) {
        notificationStore.success('Database issues fixed successfully!');
        return {
          success: true,
          postsViewFixed: true,
          connectionsTableFixed: true,
          messagingTableFixed: true
        };
      } else {
        // Construct a detailed message about what was fixed and what failed
        const fixedItems = [];
        const failedItems = [];

        if (postsViewFixed) fixedItems.push('posts view');
        else failedItems.push('posts view');

        if (connectionsTableFixed) fixedItems.push('connections table');
        else failedItems.push('connections table');

        if (messagingTableFixed) fixedItems.push('messaging table');
        else failedItems.push('messaging table');

        if (fixedItems.length > 0) {
          notificationStore.success(`Fixed: ${fixedItems.join(', ')}`);
        }

        if (failedItems.length > 0) {
          notificationStore.warning(`Failed to fix: ${failedItems.join(', ')}`);
        }

        return {
          success: fixedItems.length === 3, // All three items need to be fixed for complete success
          postsViewFixed,
          connectionsTableFixed,
          messagingTableFixed,
          error: messagingError || viewCheckError || tableCheckError || migrationError
        };
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to fix database issues';
      notificationStore.error(`Error fixing database issues: ${error.value}`);
      return {
        success: false,
        postsViewFixed: false,
        connectionsTableFixed: false,
        messagingTableFixed: false,
        error: err
      };
    } finally {
      loading.value = false;
    }
  }

  /**
   * Apply matchmaking migration
   */
  async function applyMatchmaking(): Promise<{
    success: boolean;
    matchmakingMigrationApplied: boolean;
    error?: any;
  }> {
    try {
      loading.value = true;
      error.value = null;

      // Apply the matchmaking migration
      const result = await applyMatchmakingMigration();

      if (!result.success) {
        throw result.error;
      }

      // Update migration status
      migrationStatus.value.matchmakingMigrationApplied = true;

      return {
        success: true,
        matchmakingMigrationApplied: true
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to apply matchmaking migration';
      console.error('Error applying matchmaking migration:', err);

      return {
        success: false,
        matchmakingMigrationApplied: false,
        error: err
      };
    } finally {
      loading.value = false;
    }
  }

  return {
    loading,
    error,
    migrationStatus,
    isLoading,
    getError,
    getMigrationStatus,
    createMessagingTable,
    fixDatabaseIssues,
    applyMatchmaking
  };
});
