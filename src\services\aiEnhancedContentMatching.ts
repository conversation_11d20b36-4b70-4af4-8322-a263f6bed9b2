/**
 * AI Enhanced Content Matching Service
 * 
 * Provides sophisticated content and profile matching using vector embeddings,
 * interest-based filtering, and collaborative filtering techniques.
 * 
 * Builds on the existing profile matching system with AI-powered enhancements.
 */

import { supabase } from '@/lib/supabase'
import { aiEnhancedEmbeddingService, type EnhancedMatchResult } from './aiEnhancedEmbeddingService'

export interface ContentMatchRequest {
  user_id: string
  content_type: 'profile' | 'post' | 'event' | 'opportunity'
  query?: string
  filters?: {
    profile_types?: string[]
    industries?: string[]
    locations?: string[]
    min_completion?: number
    max_results?: number
  }
  matching_strategy: 'semantic' | 'interest_based' | 'hybrid' | 'collaborative'
  include_explanations?: boolean
}

export interface ContentMatchResult {
  id: string
  type: string
  title: string
  description: string
  similarity_score: number
  match_reasons: string[]
  profile_data?: any
  metadata: {
    source_table: string
    embedding_type: string
    match_strategy: string
  }
}

export interface RecommendationContext {
  user_profile: any
  recent_interactions: string[]
  preference_patterns: Record<string, number>
  collaboration_history: string[]
}

export class AIEnhancedContentMatching {

  /**
   * Find enhanced content matches using AI-powered similarity
   */
  async findEnhancedMatches(request: ContentMatchRequest): Promise<ContentMatchResult[]> {
    try {
      console.log('🔍 Finding enhanced content matches:', request.matching_strategy)

      switch (request.matching_strategy) {
        case 'semantic':
          return await this.findSemanticMatches(request)
        case 'interest_based':
          return await this.findInterestBasedMatches(request)
        case 'hybrid':
          return await this.findHybridMatches(request)
        case 'collaborative':
          return await this.findCollaborativeMatches(request)
        default:
          return await this.findHybridMatches(request)
      }
    } catch (error) {
      console.error('Error in findEnhancedMatches:', error)
      return []
    }
  }

  /**
   * Semantic matching using query embeddings
   */
  private async findSemanticMatches(request: ContentMatchRequest): Promise<ContentMatchResult[]> {
    if (!request.query) {
      console.warn('Semantic matching requires a query')
      return []
    }

    try {
      // Generate embedding for the query
      const { data: embeddingData, error: embeddingError } = await supabase.functions.invoke('embed', {
        body: {
          input: request.query,
          model: 'bge-m3'
        }
      })

      if (embeddingError || !embeddingData?.embedding) {
        console.error('Failed to generate query embedding:', embeddingError)
        return []
      }

      // Search for similar content using existing RAG function
      const { data: matches, error: searchError } = await supabase.rpc('get_rag_context', {
        query_embedding: `[${embeddingData.embedding.join(',')}]`,
        user_id_param: request.user_id,
        context_types: this.getContextTypes(request.content_type),
        max_context_items: request.filters?.max_results || 10,
        similarity_threshold: 0.6
      })

      if (searchError) {
        console.error('Error in semantic search:', searchError)
        return []
      }

      return this.formatMatches(matches || [], 'semantic', request.include_explanations)
    } catch (error) {
      console.error('Error in semantic matching:', error)
      return []
    }
  }

  /**
   * Interest-based matching using user interest embeddings
   */
  private async findInterestBasedMatches(request: ContentMatchRequest): Promise<ContentMatchResult[]> {
    try {
      // Generate user interest embedding
      const userInterestEmbedding = await aiEnhancedEmbeddingService.generateUserInterestEmbedding(request.user_id)
      
      if (!userInterestEmbedding) {
        console.log('No user interest embedding available, falling back to semantic')
        return await this.findSemanticMatches({ ...request, query: 'relevant content' })
      }

      // Search using interest embedding
      const { data: matches, error } = await supabase.rpc('get_rag_context', {
        query_embedding: `[${userInterestEmbedding.join(',')}]`,
        user_id_param: request.user_id,
        context_types: this.getContextTypes(request.content_type),
        max_context_items: request.filters?.max_results || 10,
        similarity_threshold: 0.65
      })

      if (error) {
        console.error('Error in interest-based search:', error)
        return []
      }

      return this.formatMatches(matches || [], 'interest_based', request.include_explanations)
    } catch (error) {
      console.error('Error in interest-based matching:', error)
      return []
    }
  }

  /**
   * Hybrid matching combining semantic and interest-based approaches
   */
  private async findHybridMatches(request: ContentMatchRequest): Promise<ContentMatchResult[]> {
    try {
      // Get both semantic and interest-based matches
      const semanticMatches = request.query ? 
        await this.findSemanticMatches(request) : []
      
      const interestMatches = await this.findInterestBasedMatches(request)

      // Combine and deduplicate results
      const combinedMatches = this.combineAndRankMatches(
        semanticMatches, 
        interestMatches, 
        { semantic: 0.6, interest: 0.4 }
      )

      return combinedMatches.slice(0, request.filters?.max_results || 10)
    } catch (error) {
      console.error('Error in hybrid matching:', error)
      return []
    }
  }

  /**
   * Collaborative filtering based on similar users' preferences
   */
  private async findCollaborativeMatches(request: ContentMatchRequest): Promise<ContentMatchResult[]> {
    try {
      // Find similar users based on profile embeddings
      const similarUsers = await this.findSimilarUsers(request.user_id, request.filters?.profile_types)
      
      if (similarUsers.length === 0) {
        console.log('No similar users found, falling back to interest-based matching')
        return await this.findInterestBasedMatches(request)
      }

      // Get content that similar users have interacted with
      const collaborativeContent = await this.getContentFromSimilarUsers(
        similarUsers, 
        request.content_type,
        request.filters?.max_results || 10
      )

      return this.formatMatches(collaborativeContent, 'collaborative', request.include_explanations)
    } catch (error) {
      console.error('Error in collaborative matching:', error)
      return []
    }
  }

  /**
   * Find users with similar profiles
   */
  private async findSimilarUsers(userId: string, profileTypes?: string[]): Promise<string[]> {
    try {
      // Get user's profile type
      const { data: userProfile } = await supabase
        .from('personal_details')
        .select('profile_type')
        .eq('user_id', userId)
        .single()

      if (!userProfile?.profile_type) return []

      // Use existing similarity functions to find similar profiles
      const functionName = `search_similar_${userProfile.profile_type}_profiles`
      
      const { data: similarProfiles, error } = await supabase.rpc(functionName, {
        user_id_param: userId,
        match_threshold: 0.7,
        max_results: 20
      })

      if (error) {
        console.warn('Could not find similar users:', error)
        return []
      }

      return (similarProfiles || [])
        .map((profile: any) => profile.user_id)
        .filter(Boolean)
        .slice(0, 10)
    } catch (error) {
      console.warn('Error finding similar users:', error)
      return []
    }
  }

  /**
   * Get content that similar users have interacted with
   */
  private async getContentFromSimilarUsers(
    similarUserIds: string[], 
    contentType: string,
    maxResults: number
  ): Promise<any[]> {
    try {
      // For now, get posts from similar users
      // This can be expanded to include other interaction types
      const { data: posts, error } = await supabase
        .from('posts')
        .select(`
          id,
          title,
          content,
          tags,
          author_id,
          created_at,
          content_embedding,
          title_embedding
        `)
        .in('author_id', similarUserIds)
        .order('created_at', { ascending: false })
        .limit(maxResults)

      if (error) {
        console.error('Error getting content from similar users:', error)
        return []
      }

      return posts || []
    } catch (error) {
      console.error('Error in getContentFromSimilarUsers:', error)
      return []
    }
  }

  /**
   * Combine and rank matches from different strategies
   */
  private combineAndRankMatches(
    semanticMatches: ContentMatchResult[],
    interestMatches: ContentMatchResult[],
    weights: { semantic: number; interest: number }
  ): ContentMatchResult[] {
    const combinedMap = new Map<string, ContentMatchResult>()

    // Add semantic matches
    semanticMatches.forEach(match => {
      const adjustedScore = match.similarity_score * weights.semantic
      combinedMap.set(match.id, {
        ...match,
        similarity_score: adjustedScore,
        match_reasons: [...match.match_reasons, 'Semantic similarity'],
        metadata: { ...match.metadata, match_strategy: 'hybrid_semantic' }
      })
    })

    // Add or combine interest matches
    interestMatches.forEach(match => {
      const adjustedScore = match.similarity_score * weights.interest
      
      if (combinedMap.has(match.id)) {
        // Combine scores for items found in both
        const existing = combinedMap.get(match.id)!
        existing.similarity_score += adjustedScore
        existing.match_reasons.push('Interest alignment')
        existing.metadata.match_strategy = 'hybrid_combined'
      } else {
        combinedMap.set(match.id, {
          ...match,
          similarity_score: adjustedScore,
          match_reasons: [...match.match_reasons, 'Interest alignment'],
          metadata: { ...match.metadata, match_strategy: 'hybrid_interest' }
        })
      }
    })

    // Sort by combined score
    return Array.from(combinedMap.values())
      .sort((a, b) => b.similarity_score - a.similarity_score)
  }

  /**
   * Format raw matches into ContentMatchResult format
   */
  private formatMatches(
    rawMatches: any[], 
    strategy: string, 
    includeExplanations: boolean = false
  ): ContentMatchResult[] {
    return rawMatches.map(match => {
      const matchReasons = []
      
      if (includeExplanations) {
        if (match.relevance_score > 0.8) matchReasons.push('High content relevance')
        if (match.relevance_score > 0.7) matchReasons.push('Good semantic match')
        if (strategy === 'interest_based') matchReasons.push('Matches your interests')
        if (strategy === 'collaborative') matchReasons.push('Popular with similar users')
      }

      return {
        id: match.source_id || match.id,
        type: match.metadata?.type || 'content',
        title: match.metadata?.title || match.content?.substring(0, 50) || 'Content',
        description: match.content?.substring(0, 200) || '',
        similarity_score: match.relevance_score || 0,
        match_reasons: matchReasons,
        profile_data: match.metadata?.profile_data,
        metadata: {
          source_table: match.source_table || 'unknown',
          embedding_type: match.metadata?.embedding_type || 'content',
          match_strategy: strategy
        }
      }
    })
  }

  /**
   * Get context types for RAG search based on content type
   */
  private getContextTypes(contentType: string): string[] | null {
    const typeMap: Record<string, string[]> = {
      'profile': ['profile'],
      'post': ['post', 'content'],
      'event': ['event'],
      'opportunity': ['opportunity', 'post'],
      'all': null // null means all types
    }

    return typeMap[contentType] || null
  }

  /**
   * Generate personalized recommendations for a user
   */
  async generatePersonalizedRecommendations(
    userId: string,
    context: RecommendationContext,
    maxResults: number = 10
  ): Promise<ContentMatchResult[]> {
    try {
      // Create a hybrid request based on user context
      const request: ContentMatchRequest = {
        user_id: userId,
        content_type: 'all',
        matching_strategy: 'hybrid',
        filters: {
          max_results: maxResults,
          min_completion: 50 // Only recommend well-completed profiles
        },
        include_explanations: true
      }

      // Get hybrid matches
      const matches = await this.findHybridMatches(request)

      // Apply personalization based on context
      return this.personalizeRecommendations(matches, context)
    } catch (error) {
      console.error('Error generating personalized recommendations:', error)
      return []
    }
  }

  /**
   * Apply personalization to recommendations based on user context
   */
  private personalizeRecommendations(
    matches: ContentMatchResult[],
    context: RecommendationContext
  ): ContentMatchResult[] {
    return matches.map(match => {
      // Boost scores based on user preferences
      let personalizedScore = match.similarity_score

      // Boost based on profile type preferences
      if (context.preference_patterns[match.type]) {
        personalizedScore *= (1 + context.preference_patterns[match.type] * 0.2)
      }

      // Add personalized match reasons
      const personalizedReasons = [...match.match_reasons]
      if (context.recent_interactions.some(interaction => 
        match.description.toLowerCase().includes(interaction.toLowerCase())
      )) {
        personalizedReasons.push('Related to your recent activity')
        personalizedScore *= 1.1
      }

      return {
        ...match,
        similarity_score: Math.min(personalizedScore, 1.0), // Cap at 1.0
        match_reasons: personalizedReasons
      }
    }).sort((a, b) => b.similarity_score - a.similarity_score)
  }
}

// Export singleton instance
export const aiEnhancedContentMatching = new AIEnhancedContentMatching()
