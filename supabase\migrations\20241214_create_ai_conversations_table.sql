-- Enable the pgvector extension for vector similarity search
CREATE EXTENSION IF NOT EXISTS vector;

-- Create ai_conversations table for storing conversation context with vector embeddings
CREATE TABLE IF NOT EXISTS ai_conversations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  message TEXT NOT NULL,
  response TEXT NOT NULL,
  embedding vector(1536), -- OpenAI ada-002 embedding dimension
  context JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS ai_conversations_user_id_idx ON ai_conversations(user_id);
CREATE INDEX IF NOT EXISTS ai_conversations_created_at_idx ON ai_conversations(created_at DESC);
CREATE INDEX IF NOT EXISTS ai_conversations_embedding_idx ON ai_conversations USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Create function for vector similarity search
CREATE OR REPLACE FUNCTION match_conversations(
  query_embedding vector(1536),
  match_threshold float,
  match_count int,
  user_id uuid
)
RETURNS TABLE (
  id uuid,
  message text,
  response text,
  context jsonb,
  similarity float,
  created_at timestamp with time zone
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    ai_conversations.id,
    ai_conversations.message,
    ai_conversations.response,
    ai_conversations.context,
    1 - (ai_conversations.embedding <=> query_embedding) AS similarity,
    ai_conversations.created_at
  FROM ai_conversations
  WHERE ai_conversations.user_id = match_conversations.user_id
    AND 1 - (ai_conversations.embedding <=> query_embedding) > match_threshold
  ORDER BY ai_conversations.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_ai_conversations_updated_at
  BEFORE UPDATE ON ai_conversations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add RLS (Row Level Security) policies
ALTER TABLE ai_conversations ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own conversations
CREATE POLICY "Users can access their own conversations" ON ai_conversations
  FOR ALL USING (auth.uid() = user_id);

-- Policy: Service role can access all conversations (for edge functions)
CREATE POLICY "Service role can access all conversations" ON ai_conversations
  FOR ALL USING (auth.role() = 'service_role');

-- Grant necessary permissions
GRANT ALL ON ai_conversations TO authenticated;
GRANT ALL ON ai_conversations TO service_role;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS ai_conversations_user_created_idx ON ai_conversations(user_id, created_at DESC);

-- Add comments for documentation
COMMENT ON TABLE ai_conversations IS 'Stores AI chat conversations with vector embeddings for semantic search';
COMMENT ON COLUMN ai_conversations.embedding IS 'Vector embedding of the conversation for similarity search';
COMMENT ON COLUMN ai_conversations.context IS 'Additional context like profile_type, current_page, etc.';
COMMENT ON FUNCTION match_conversations IS 'Function to find similar conversations using vector similarity search';
