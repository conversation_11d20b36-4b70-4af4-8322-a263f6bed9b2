<template>
  <q-card class="featured-profile-skeleton">
    <div class="skeleton-container">
      <!-- Header Skeleton -->
      <div class="skeleton-header">
        <q-skeleton height="100%" />
        
        <!-- Badge Skeleton -->
        <div class="absolute-top-right q-ma-sm">
          <q-skeleton type="QChip" />
        </div>
      </div>

      <!-- Avatar Skeleton -->
      <div class="skeleton-avatar">
        <q-skeleton type="QAvatar" size="80px" />
      </div>

      <!-- Content Skeleton -->
      <div class="skeleton-content q-pa-md text-center">
        <!-- Name Skeleton -->
        <q-skeleton 
          type="text" 
          width="70%" 
          height="1.2rem"
          class="q-mb-xs q-mx-auto"
        />
        
        <!-- Bio Skeleton -->
        <q-skeleton 
          type="text" 
          width="90%" 
          height="0.9rem"
          class="q-mb-xs q-mx-auto"
        />
        <q-skeleton 
          type="text" 
          width="60%" 
          height="0.9rem"
          class="q-mb-sm q-mx-auto"
        />
        
        <!-- Stats Skeleton -->
        <div class="row justify-center q-gutter-md q-mb-sm">
          <div class="text-center">
            <q-skeleton type="text" width="30px" height="1rem" class="q-mb-xs" />
            <q-skeleton type="text" width="50px" height="0.7rem" />
          </div>
          <div class="text-center">
            <q-skeleton type="text" width="30px" height="1rem" class="q-mb-xs" />
            <q-skeleton type="text" width="60px" height="0.7rem" />
          </div>
        </div>
        
        <!-- Tags Skeleton -->
        <div class="row justify-center q-gutter-xs q-mb-sm">
          <q-skeleton type="QChip" v-for="n in 2" :key="n" />
        </div>
        
        <!-- Button Skeleton -->
        <q-skeleton type="QBtn" class="q-mt-md" />
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
// No props or logic needed for skeleton
</script>

<style scoped>
.featured-profile-skeleton {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  height: 340px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.skeleton-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.skeleton-header {
  height: 80px;
  position: relative;
}

.skeleton-avatar {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.skeleton-content {
  flex: 1;
  padding-top: 50px;
  display: flex;
  flex-direction: column;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .featured-profile-skeleton {
    height: 320px;
  }
}
</style>
