<template>
  <div class="profile-view q-pa-md">
    <div v-if="loading" class="q-pa-md flex flex-center">
      <q-spinner color="primary" size="3em" />
      <div class="q-ml-md">Loading profile...</div>
    </div>

    <!-- New User Welcome Component -->
    <div v-else-if="!profile || !profile.profile_type">
      <new-user-welcome @create-profile="$emit('create-profile')" />
    </div>

    <div v-else>
      <!-- Profile Header -->
      <q-card class="profile-header-card q-mb-lg">
        <q-card-section class="bg-primary text-white">
          <div class="row items-center">
            <div class="col-12 col-md-auto">
              <user-avatar
                :name="`${profile.first_name} ${profile.last_name}`"
                :email="profile.email"
                :avatar-url="profile.avatar_url"
                :user-id="profile.user_id || profile.id"
                size="100px"
                :clickable="false"
                color="#0D8A3E"
                class="q-mr-md"
              />
            </div>

            <div class="col">
              <div class="text-h4">{{ profile.first_name }} {{ profile.last_name }}</div>
              <div class="text-subtitle1">{{ formatProfileType(profile.profile_type) }}</div>
              <div class="text-caption text-white-8">{{ profile.email }}</div>

              <div class="q-mt-sm">
                <q-badge v-if="profile.profile_state === 'ACTIVE'" color="positive">Active</q-badge>
                <q-badge v-else-if="profile.profile_state === 'PENDING_APPROVAL'" color="warning">Pending Approval</q-badge>
                <q-badge v-else-if="profile.profile_state === 'IN_PROGRESS'" color="info">In Progress</q-badge>
              </div>
            </div>

            <div class="col-12 col-md-auto q-mt-md q-mt-md-none">
              <q-btn
                outline
                color="white"
                :label="profile.profile_type ? 'Edit Profile' : 'Create Profile'"
                :to="profile.profile_type ? { name: 'profile-edit', params: { id: profile.id } } : { name: 'profile-create' }"
              >
                <unified-icon :name="profile.profile_type ? 'edit' : 'person_add'" class="q-mr-xs" />
              </q-btn>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Profile Completion -->
      <div v-if="profile.profile_completion < 100" class="profile-completion q-mb-lg">
        <div class="text-subtitle1 q-mb-sm">Profile Completion</div>
        <ProfileCompletionIndicator
          :profile="profile"
          :profile-data="specializedProfile"
          show-missing-fields
        />
      </div>

      <!-- Personal Details Section -->
      <q-card class="q-mb-md">
        <q-card-section>
          <div class="text-h6">Personal Details</div>
        </q-card-section>

        <q-separator />

        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="text-subtitle2">First Name</div>
              <div>{{ profile.first_name || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Last Name</div>
              <div>{{ profile.last_name || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Email</div>
              <div>{{ profile.email || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Phone</div>
              <div>
                <span v-if="profile.phone_country_code && profile.phone_number">
                  {{ profile.phone_country_code }} {{ profile.phone_number }}
                </span>
                <span v-else>Not provided</span>
              </div>
            </div>

            <div class="col-12">
              <div class="text-subtitle2">Bio</div>
              <div>{{ profile.bio || 'Not provided' }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Profile Type Specific Sections -->
      <template v-if="specializedProfile">
        <q-card v-for="(section, sectionIndex) in profileSections" :key="sectionIndex" class="q-mb-md">
          <q-card-section>
            <div class="text-h6">{{ section.title }}</div>
          </q-card-section>

          <q-separator />

          <q-card-section>
            <div class="row q-col-gutter-md">
              <template v-for="(field, index) in section.questions" :key="index">
                <div
                  :class="field.fullWidth ? 'col-12' : 'col-12 col-md-6'"
                >
                  <div class="text-subtitle2">{{ field.label }}</div>

                  <!-- Boolean field -->
                  <div v-if="field.type === 'boolean'">
                    {{ specializedProfile[field.name] ? 'Yes' : 'No' }}
                  </div>

                  <!-- Array field -->
                  <div v-else-if="Array.isArray(specializedProfile[field.name])" class="q-mt-xs">
                    <q-chip
                      v-for="item in specializedProfile[field.name]"
                      :key="item"
                      dense
                      color="primary"
                      text-color="white"
                      class="q-ma-xs"
                    >
                      {{ item }}
                    </q-chip>
                    <div v-if="!specializedProfile[field.name] || specializedProfile[field.name].length === 0">
                      Not provided
                    </div>
                  </div>

                  <!-- URL field -->
                  <div v-else-if="field.name.includes('website') || field.name.includes('url')">
                    <a v-if="specializedProfile[field.name]" :href="specializedProfile[field.name]" target="_blank">
                      {{ specializedProfile[field.name] }}
                    </a>
                    <span v-else>Not provided</span>
                  </div>

                  <!-- Currency field -->
                  <div v-else-if="field.name.includes('amount') || field.name.includes('price')">
                    {{ formatCurrency(specializedProfile[field.name]) || 'Not provided' }}
                  </div>

                  <!-- Default text field -->
                  <div v-else>
                    {{ specializedProfile[field.name] || 'Not provided' }}
                  </div>
                </div>
              </template>
            </div>
          </q-card-section>
        </q-card>
      </template>

      <!-- No specialized profile data -->
      <div v-else class="q-pa-md">
        <p class="text-italic">No additional profile information available.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { useProfileQuestions } from '@/services/profileQuestions'
import ProfileCompletionIndicator from './ProfileCompletionIndicator.vue'
import UnifiedIcon from '../ui/UnifiedIcon.vue'
import NewUserWelcome from './NewUserWelcome.vue'

const props = defineProps<{
  profile: any
  specializedProfile?: any
  loading?: boolean
}>()

const emit = defineEmits(['create-profile'])

const { setProfileType, currentQuestions } = useProfileQuestions()
const profileSections = computed(() => currentQuestions.value?.sections || [])

// Format profile type for display
function formatProfileType(type: string | null): string {
  if (!type) return 'Unknown'
  return type.charAt(0).toUpperCase() + type.slice(1)
}

// Get initials from name
function getInitials(firstName?: string, lastName?: string): string {
  const first = firstName ? firstName.charAt(0) : ''
  const last = lastName ? lastName.charAt(0) : ''
  return (first + last).toUpperCase()
}

// Format currency
function formatCurrency(value: number | undefined): string {
  if (value === undefined || value === null) return ''
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

// Watch for changes in profile type
onMounted(() => {
  console.log('ProfileView component mounted, profile type:', props.profile?.profile_type)
  // Set profile type for dynamic sections
  if (props.profile?.profile_type) {
    console.log('Setting profile type to:', props.profile.profile_type)
    setProfileType(props.profile.profile_type)
  }
})

// Watch for changes in profile type
watch(() => props.profile?.profile_type, (newType) => {
  if (newType) {
    console.log('Profile type changed to:', newType)
    setProfileType(newType)
  }
})
</script>

<style scoped>
.profile-view {
  max-width: 1200px;
  margin: 0 auto;
}

/* Mobile responsiveness improvements */
@media (max-width: 599px) {
  .profile-view {
    padding: 8px !important;
  }

  .q-card {
    margin-bottom: 16px;
  }

  .q-card-section {
    padding: 16px 12px;
  }

  .profile-header-card .q-card-section {
    padding: 16px;
  }

  .profile-header-card .row {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .profile-header-card .q-avatar {
    margin-bottom: 16px;
    margin-right: 0 !important;
  }

  .profile-header-card .col-md-auto {
    margin-top: 16px !important;
  }

  .row.q-col-gutter-md {
    margin: 0 -8px;
  }

  .col-12 {
    padding: 0 8px;
  }
}
</style>
