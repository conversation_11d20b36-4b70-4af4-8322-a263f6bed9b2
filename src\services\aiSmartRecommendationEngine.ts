/**
 * AI Smart Recommendation Engine
 * 
 * Provides intelligent recommendations using vector similarity, collaborative filtering,
 * and user behavior analysis. Integrates with the enhanced content matching system.
 */

import { supabase } from '@/lib/supabase'
import { aiEnhancedContentMatching, type ContentMatchRequest } from './aiEnhancedContentMatching'
import { aiEnhancedEmbeddingService } from './aiEnhancedEmbeddingService'

export interface RecommendationRequest {
  user_id: string
  recommendation_type: 'profiles' | 'content' | 'opportunities' | 'mixed'
  context?: {
    current_page?: string
    recent_activity?: string[]
    search_query?: string
    filters?: Record<string, any>
  }
  preferences?: {
    max_results?: number
    min_relevance_score?: number
    include_explanations?: boolean
    diversity_factor?: number // 0-1, higher = more diverse results
  }
}

export interface SmartRecommendation {
  id: string
  type: 'profile' | 'post' | 'event' | 'opportunity'
  title: string
  description: string
  relevance_score: number
  confidence: number
  explanation: string[]
  metadata: {
    source: 'vector_similarity' | 'collaborative_filtering' | 'behavior_based' | 'hybrid'
    profile_type?: string
    tags?: string[]
    created_at?: string
  }
  action_suggestions: string[]
}

export interface RecommendationResponse {
  recommendations: SmartRecommendation[]
  total_found: number
  processing_time_ms: number
  recommendation_strategy: string
  next_refresh_suggested_in_hours: number
}

export class AISmartRecommendationEngine {

  /**
   * Generate smart recommendations for a user
   */
  async generateRecommendations(request: RecommendationRequest): Promise<RecommendationResponse> {
    const startTime = Date.now()
    
    try {
      console.log('🎯 Generating smart recommendations:', request.recommendation_type)

      // Get user profile and context
      const userProfile = await this.getUserProfile(request.user_id)
      if (!userProfile) {
        return this.getEmptyResponse(startTime)
      }

      // Determine recommendation strategy
      const strategy = this.determineRecommendationStrategy(request, userProfile)
      
      // Generate recommendations based on strategy
      const recommendations = await this.executeRecommendationStrategy(
        strategy, 
        request, 
        userProfile
      )

      // Apply diversity and ranking
      const rankedRecommendations = this.applyDiversityAndRanking(
        recommendations,
        request.preferences?.diversity_factor || 0.3,
        request.preferences?.max_results || 10
      )

      // Calculate next refresh time
      const nextRefreshHours = this.calculateNextRefreshTime(userProfile, recommendations.length)

      return {
        recommendations: rankedRecommendations,
        total_found: recommendations.length,
        processing_time_ms: Date.now() - startTime,
        recommendation_strategy: strategy,
        next_refresh_suggested_in_hours: nextRefreshHours
      }

    } catch (error) {
      console.error('Error generating smart recommendations:', error)
      return this.getEmptyResponse(startTime)
    }
  }

  /**
   * Get user profile with enhanced context
   */
  private async getUserProfile(userId: string): Promise<any> {
    try {
      const { data: profile, error } = await supabase
        .from('personal_details')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error || !profile) return null

      // Get recent activity for context
      const { data: recentActivity } = await supabase
        .from('ai_conversations')
        .select('summary, context_data, created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(5)

      return {
        ...profile,
        recent_activity: recentActivity || []
      }
    } catch (error) {
      console.error('Error getting user profile:', error)
      return null
    }
  }

  /**
   * Determine the best recommendation strategy
   */
  private determineRecommendationStrategy(
    request: RecommendationRequest, 
    userProfile: any
  ): string {
    // New users benefit from collaborative filtering
    if (userProfile.profile_completion < 50) {
      return 'collaborative_with_onboarding'
    }

    // Users with rich profiles benefit from vector similarity
    if (userProfile.profile_completion >= 80) {
      return 'vector_similarity_primary'
    }

    // Users with activity history benefit from behavior-based
    if (userProfile.recent_activity && userProfile.recent_activity.length >= 3) {
      return 'behavior_based_hybrid'
    }

    // Default to hybrid approach
    return 'hybrid_balanced'
  }

  /**
   * Execute the chosen recommendation strategy
   */
  private async executeRecommendationStrategy(
    strategy: string,
    request: RecommendationRequest,
    userProfile: any
  ): Promise<SmartRecommendation[]> {
    switch (strategy) {
      case 'vector_similarity_primary':
        return await this.getVectorSimilarityRecommendations(request, userProfile)
      
      case 'collaborative_with_onboarding':
        return await this.getCollaborativeRecommendations(request, userProfile)
      
      case 'behavior_based_hybrid':
        return await this.getBehaviorBasedRecommendations(request, userProfile)
      
      case 'hybrid_balanced':
      default:
        return await this.getHybridRecommendations(request, userProfile)
    }
  }

  /**
   * Get recommendations using vector similarity
   */
  private async getVectorSimilarityRecommendations(
    request: RecommendationRequest,
    userProfile: any
  ): Promise<SmartRecommendation[]> {
    const matchRequest: ContentMatchRequest = {
      user_id: request.user_id,
      content_type: this.mapRecommendationType(request.recommendation_type),
      matching_strategy: 'semantic',
      filters: {
        max_results: request.preferences?.max_results || 15,
        min_completion: 60,
        ...request.context?.filters
      },
      include_explanations: true
    }

    const matches = await aiEnhancedContentMatching.findEnhancedMatches(matchRequest)
    
    return matches.map(match => this.formatAsRecommendation(match, 'vector_similarity'))
  }

  /**
   * Get recommendations using collaborative filtering
   */
  private async getCollaborativeRecommendations(
    request: RecommendationRequest,
    userProfile: any
  ): Promise<SmartRecommendation[]> {
    const matchRequest: ContentMatchRequest = {
      user_id: request.user_id,
      content_type: this.mapRecommendationType(request.recommendation_type),
      matching_strategy: 'collaborative',
      filters: {
        max_results: request.preferences?.max_results || 15,
        profile_types: this.getRecommendedProfileTypes(userProfile.profile_type),
        ...request.context?.filters
      },
      include_explanations: true
    }

    const matches = await aiEnhancedContentMatching.findEnhancedMatches(matchRequest)
    
    return matches.map(match => this.formatAsRecommendation(match, 'collaborative_filtering'))
  }

  /**
   * Get recommendations based on user behavior
   */
  private async getBehaviorBasedRecommendations(
    request: RecommendationRequest,
    userProfile: any
  ): Promise<SmartRecommendation[]> {
    // Extract interests from recent activity
    const interests = this.extractInterestsFromActivity(userProfile.recent_activity)
    
    const matchRequest: ContentMatchRequest = {
      user_id: request.user_id,
      content_type: this.mapRecommendationType(request.recommendation_type),
      matching_strategy: 'interest_based',
      query: interests.join(' '),
      filters: {
        max_results: request.preferences?.max_results || 15,
        ...request.context?.filters
      },
      include_explanations: true
    }

    const matches = await aiEnhancedContentMatching.findEnhancedMatches(matchRequest)
    
    return matches.map(match => this.formatAsRecommendation(match, 'behavior_based'))
  }

  /**
   * Get hybrid recommendations combining multiple strategies
   */
  private async getHybridRecommendations(
    request: RecommendationRequest,
    userProfile: any
  ): Promise<SmartRecommendation[]> {
    const matchRequest: ContentMatchRequest = {
      user_id: request.user_id,
      content_type: this.mapRecommendationType(request.recommendation_type),
      matching_strategy: 'hybrid',
      filters: {
        max_results: request.preferences?.max_results || 15,
        ...request.context?.filters
      },
      include_explanations: true
    }

    const matches = await aiEnhancedContentMatching.findEnhancedMatches(matchRequest)
    
    return matches.map(match => this.formatAsRecommendation(match, 'hybrid'))
  }

  /**
   * Map recommendation type to content type
   */
  private mapRecommendationType(type: string): 'profile' | 'post' | 'event' | 'opportunity' {
    const mapping: Record<string, 'profile' | 'post' | 'event' | 'opportunity'> = {
      'profiles': 'profile',
      'content': 'post',
      'opportunities': 'opportunity',
      'mixed': 'profile' // Default to profile for mixed
    }
    
    return mapping[type] || 'profile'
  }

  /**
   * Get recommended profile types based on user's profile type
   */
  private getRecommendedProfileTypes(userProfileType: string): string[] {
    const recommendations: Record<string, string[]> = {
      'innovator': ['investor', 'mentor', 'industry_expert'],
      'investor': ['innovator', 'professional'],
      'mentor': ['innovator', 'academic_student', 'professional'],
      'professional': ['innovator', 'mentor', 'professional'],
      'academic_student': ['mentor', 'industry_expert', 'academic_institution'],
      'industry_expert': ['innovator', 'professional'],
      'academic_institution': ['innovator', 'industry_expert']
    }
    
    return recommendations[userProfileType] || ['innovator', 'professional']
  }

  /**
   * Extract interests from user activity
   */
  private extractInterestsFromActivity(recentActivity: any[]): string[] {
    const interests: string[] = []
    
    recentActivity.forEach(activity => {
      if (activity.summary) {
        interests.push(activity.summary)
      }
      if (activity.context_data?.interests) {
        interests.push(...activity.context_data.interests)
      }
    })
    
    return interests.slice(0, 10) // Limit to prevent token overflow
  }

  /**
   * Format content match as recommendation
   */
  private formatAsRecommendation(match: any, source: string): SmartRecommendation {
    return {
      id: match.id,
      type: match.type as 'profile' | 'post' | 'event' | 'opportunity',
      title: match.title,
      description: match.description,
      relevance_score: match.similarity_score,
      confidence: Math.min(0.95, match.similarity_score + 0.1),
      explanation: match.match_reasons || [],
      metadata: {
        source: source as any,
        profile_type: match.profile_data?.profile_type,
        tags: match.metadata?.tags,
        created_at: match.metadata?.created_at
      },
      action_suggestions: this.generateActionSuggestions(match)
    }
  }

  /**
   * Generate action suggestions for a recommendation
   */
  private generateActionSuggestions(match: any): string[] {
    const suggestions: string[] = []
    
    if (match.type === 'profile') {
      suggestions.push('View Profile', 'Send Connection Request')
      if (match.profile_data?.profile_type === 'mentor') {
        suggestions.push('Request Mentorship')
      }
    } else if (match.type === 'post') {
      suggestions.push('Read Full Post', 'Like', 'Comment')
    } else if (match.type === 'opportunity') {
      suggestions.push('Learn More', 'Apply', 'Save for Later')
    }
    
    return suggestions
  }

  /**
   * Apply diversity and ranking to recommendations
   */
  private applyDiversityAndRanking(
    recommendations: SmartRecommendation[],
    diversityFactor: number,
    maxResults: number
  ): SmartRecommendation[] {
    // Sort by relevance score first
    const sorted = recommendations.sort((a, b) => b.relevance_score - a.relevance_score)
    
    // Apply diversity if requested
    if (diversityFactor > 0) {
      return this.diversifyRecommendations(sorted, diversityFactor, maxResults)
    }
    
    return sorted.slice(0, maxResults)
  }

  /**
   * Diversify recommendations to avoid too much similarity
   */
  private diversifyRecommendations(
    recommendations: SmartRecommendation[],
    diversityFactor: number,
    maxResults: number
  ): SmartRecommendation[] {
    const diversified: SmartRecommendation[] = []
    const typeCount: Record<string, number> = {}
    
    for (const rec of recommendations) {
      if (diversified.length >= maxResults) break
      
      const currentTypeCount = typeCount[rec.type] || 0
      const maxPerType = Math.ceil(maxResults / 4) // Distribute across types
      
      if (currentTypeCount < maxPerType || diversified.length < maxResults * 0.8) {
        diversified.push(rec)
        typeCount[rec.type] = currentTypeCount + 1
      }
    }
    
    return diversified
  }

  /**
   * Calculate when to suggest next refresh
   */
  private calculateNextRefreshTime(userProfile: any, recommendationCount: number): number {
    // More active users get more frequent updates
    if (userProfile.recent_activity?.length >= 3) {
      return 6 // 6 hours for active users
    }
    
    // New users get more frequent updates to help onboarding
    if (userProfile.profile_completion < 50) {
      return 12 // 12 hours for new users
    }
    
    // Default refresh time
    return 24 // 24 hours for regular users
  }

  /**
   * Get empty response for error cases
   */
  private getEmptyResponse(startTime: number): RecommendationResponse {
    return {
      recommendations: [],
      total_found: 0,
      processing_time_ms: Date.now() - startTime,
      recommendation_strategy: 'none',
      next_refresh_suggested_in_hours: 24
    }
  }
}

// Export singleton instance
export const aiSmartRecommendationEngine = new AISmartRecommendationEngine()
