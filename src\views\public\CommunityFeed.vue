<template>
  <q-page>
    <div class="feed-hero">
      <div class="container q-mx-auto q-px-md">
        <div class="row">
          <div class="col-1" />
          <div class="col-10">
            <div class="text-h2 text-weight-bold q-mb-md text-center" style="color: #0D8A3E">Community Feed</div>
            <p class="text-body1 text-center q-mb-lg" style="max-width: 800px; margin: 0 auto;">
              Stay updated with the latest news, events, and stories from our innovation community.
            </p>
          </div>
          <div class="col-1" />
        </div>
      </div>
    </div>

    <div class="feed-section q-py-xl">
      <div class="container q-mx-auto q-px-md">
        <div class="row q-col-gutter-lg">
          <div class="col-12 col-md-8">
            <!-- AI Triggers for Community Feed -->
            <div class="q-mb-md">
              <smart-ai-triggers
                page="community"
                mode="card"
                :context="{
                  section: 'feed',
                  hasContent: posts.length > 0
                }"
                :max-triggers="3"
              />
            </div>

            <!-- Loading State -->
            <div v-if="loading" class="text-center q-pa-xl">
              <q-spinner color="primary" size="3em" />
              <p>Loading community feed...</p>
            </div>

            <!-- Error State -->
            <div v-else-if="error" class="text-center q-pa-xl text-negative">
              <q-icon name="error" size="3em" />
              <p>{{ error }}</p>
              <q-btn color="primary" label="Try Again" @click="loadData" />
            </div>

            <!-- Empty State -->
            <div v-else-if="posts.length === 0" class="text-center q-pa-xl">
              <q-icon name="info" size="3em" color="grey" />
              <p>No posts found.</p>
            </div>

            <!-- Main Feed -->
            <div v-else class="feed-posts">
              <q-card v-for="(post, index) in posts" :key="index" class="q-mb-md feed-card">
                <q-img
                  :src="post.image || 'https://placehold.co/600x400?text=No+Image'"
                  :ratio="16/9"
                />
                <q-card-section>
                  <div class="row items-center q-mb-xs">
                    <q-chip
                      dense
                      :color="getCategoryColor(post.category)"
                      text-color="white"
                      class="q-mr-sm"
                    >
                      {{ post.category || 'Uncategorized' }}
                    </q-chip>
                    <span class="text-caption text-grey">{{ post.date || 'No date' }}</span>
                  </div>
                  <div class="text-h6">{{ post.title || 'Untitled Post' }}</div>
                  <p class="text-body2 q-mt-sm">{{ post.excerpt || post.content || 'No content available' }}</p>
                </q-card-section>
                <q-card-actions>
                  <q-btn flat color="primary" label="Read More" />
                  <q-space />
                  <q-btn flat round icon="thumb_up" />
                  <q-btn flat round icon="comment" />
                  <q-btn flat round icon="share" />
                </q-card-actions>
              </q-card>

              <div class="text-center q-mt-lg">
                <q-btn color="primary" label="Load More" outline @click="loadMorePosts" />
              </div>
            </div>
          </div>

          <div class="col-12 col-md-4">
            <!-- Sidebar -->
            <q-card class="q-mb-md">
              <q-card-section>
                <div class="text-h6">Categories</div>
              </q-card-section>
              <q-separator />
              <q-list padding>
                <q-item clickable v-ripple>
                  <q-item-section>All</q-item-section>
                  <q-item-section avatar>
                    <q-badge color="primary" rounded>{{ posts.length }}</q-badge>
                  </q-item-section>
                </q-item>
                <q-item clickable v-ripple>
                  <q-item-section>Events</q-item-section>
                  <q-item-section avatar>
                    <q-badge color="green" rounded>3</q-badge>
                  </q-item-section>
                </q-item>
                <q-item clickable v-ripple>
                  <q-item-section>News</q-item-section>
                  <q-item-section avatar>
                    <q-badge color="blue" rounded>2</q-badge>
                  </q-item-section>
                </q-item>
                <q-item clickable v-ripple>
                  <q-item-section>Success Stories</q-item-section>
                  <q-item-section avatar>
                    <q-badge color="orange" rounded>1</q-badge>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card>

            <q-card class="q-mb-md">
              <q-card-section>
                <div class="text-h6">Upcoming Events</div>
              </q-card-section>
              <q-separator />

              <!-- Loading Events -->
              <div v-if="loading" class="text-center q-pa-md">
                <q-spinner color="primary" size="2em" />
                <p class="q-ma-none">Loading events...</p>
              </div>

              <!-- No Events -->
              <div v-else-if="events.length === 0" class="text-center q-pa-md">
                <p class="q-ma-none">No upcoming events found.</p>
              </div>

              <!-- Events List -->
              <q-list v-else padding>
                <q-item v-for="(event, index) in events" :key="index">
                  <q-item-section>
                    <q-item-label>{{ event.title || 'Untitled Event' }}</q-item-label>
                    <q-item-label caption>{{ event.date || 'No date specified' }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>

              <q-card-actions>
                <q-btn flat color="primary" label="View All Events" class="full-width" />
              </q-card-actions>
            </q-card>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { usePostsStore } from '../../stores/posts';
import { useEventsStore } from '../../stores/events';
import { useFilterStore } from '../../stores/filterStore';
import SmartAITriggers from '../../components/ai/SmartAITriggers.vue';

// Initialize stores
const postsStore = usePostsStore();
const eventsStore = useEventsStore();
const filterStore = useFilterStore();

// Use computed properties to access store data
const posts = computed(() => postsStore.filteredPosts);
const events = computed(() => eventsStore.upcomingEvents);
const loading = ref(true);
const error = ref(null);

// Load data on component mount
onMounted(async () => {
  try {
    loading.value = true;
    error.value = null;

    // Set the active tab in the filter store
    filterStore.setActiveTab('feed');

    // Load posts and events in parallel
    await Promise.all([
      postsStore.fetchPosts(),
      eventsStore.fetchEvents()
    ]);
  } catch (err) {
    console.error('Error loading community feed data:', err);
    error.value = 'Failed to load data. Please try again.';
  } finally {
    loading.value = false;
  }
});

// Function to reload data
async function loadData() {
  try {
    loading.value = true;
    error.value = null;

    // Reset filters
    filterStore.resetCurrentTabFilters();

    // Load posts and events in parallel
    await Promise.all([
      postsStore.fetchPosts(),
      eventsStore.fetchEvents()
    ]);
  } catch (err) {
    console.error('Error loading community feed data:', err);
    error.value = 'Failed to load data. Please try again.';
  } finally {
    loading.value = false;
  }
}

// Function to load more posts
async function loadMorePosts() {
  try {
    // Check if the posts store has a fetchMorePosts method
    if (typeof postsStore.fetchMorePosts === 'function') {
      await postsStore.fetchMorePosts();
    } else {
      // Fallback to regular fetch with pagination
      await postsStore.fetchPosts({
        page: Math.ceil(posts.value.length / 10) + 1
      });
    }
  } catch (err) {
    console.error('Error loading more posts:', err);
  }
}

// Function to get category color
const getCategoryColor = (category: string): string => {
  if (!category) return 'primary';

  switch (category.toLowerCase()) {
    case 'events':
      return 'green';
    case 'news':
      return 'blue';
    case 'success stories':
      return 'orange';
    default:
      return 'primary';
  }
};
</script>

<style scoped>
.container {
  width: 100%;
  max-width: 1400px;
}

.feed-hero {
  padding: 60px 0;
  background-color: #f5f5f5;
}

.feed-section {
  padding: 40px 0;
}

.feed-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feed-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.text-primary {
  color: #0D8A3E !important;
}
</style>
