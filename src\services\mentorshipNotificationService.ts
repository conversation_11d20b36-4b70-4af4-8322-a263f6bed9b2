/**
 * Mentorship Notification Service
 * 
 * Handles sending mentorship-specific notifications via email and in-app notifications
 */

import { supabase } from '../lib/supabase'
import { sendEmail } from './emailService'

export interface MentorshipRequestNotificationData {
  mentorId: string
  menteeId: string
  requestId: string
  requestTitle: string
  requestMessage: string
}

export interface MentorshipRequestResponseNotificationData {
  menteeId: string
  mentorId: string
  requestId: string
  requestTitle: string
  responseStatus: 'accepted' | 'declined'
  mentorResponse?: string
}

export interface MentorshipSessionReminderNotificationData {
  sessionId: string
  mentorId: string
  menteeId: string
  sessionTitle: string
  scheduledStartTime: string
  meetingLink?: string
  meetingPlatform: string
}

export interface MentorshipEventAnnouncementNotificationData {
  eventId: string
  mentorId: string
  eventTitle: string
  eventDescription: string
  scheduledStartTime: string
  maxParticipants: number
  currentParticipants: number
}

/**
 * Get user information for notifications
 */
async function getUserInfo(userId: string) {
  const { data: user, error } = await supabase
    .from('auth.users')
    .select('email, raw_user_meta_data')
    .eq('id', userId)
    .single()

  if (error) {
    console.error('Error fetching user info:', error)
    return null
  }

  return {
    email: user.email,
    firstName: user.raw_user_meta_data?.first_name || user.raw_user_meta_data?.full_name?.split(' ')[0]
  }
}

/**
 * Check if user has enabled a specific notification type
 */
async function isNotificationEnabled(userId: string, notificationType: string): Promise<boolean> {
  const { data: preferences, error } = await supabase
    .from('email_notification_preferences')
    .select(notificationType)
    .eq('user_id', userId)
    .single()

  if (error) {
    console.error('Error checking notification preferences:', error)
    return true // Default to enabled if we can't check
  }

  return preferences?.[notificationType] !== false
}

/**
 * Send mentorship request notification to mentor
 */
export async function sendMentorshipRequestNotification(data: MentorshipRequestNotificationData): Promise<boolean> {
  try {
    // Check if mentor has this notification type enabled
    const isEnabled = await isNotificationEnabled(data.mentorId, 'mentorship_requests')
    if (!isEnabled) {
      console.log('Mentorship request notifications disabled for user:', data.mentorId)
      return true
    }

    // Get mentor and mentee info
    const [mentorInfo, menteeInfo] = await Promise.all([
      getUserInfo(data.mentorId),
      getUserInfo(data.menteeId)
    ])

    if (!mentorInfo || !menteeInfo) {
      console.error('Could not get user information for notification')
      return false
    }

    // Create in-app notification
    await supabase.from('user_notifications').insert({
      user_id: data.mentorId,
      title: `New mentorship request from ${menteeInfo.firstName || 'someone'}`,
      content: data.requestTitle,
      type: 'mentorship_request',
      related_entity_id: data.requestId,
      is_read: false
    })

    // Send email notification using the notification Edge Function directly
    const requestUrl = `${process.env.VITE_APP_URL || 'http://localhost:5173'}/dashboard/mentorship/requests/${data.requestId}`

    const { data: emailResult, error: emailError } = await supabase.functions.invoke('send-notification-email', {
      body: {
        type: 'mentorship_request',
        data: {
          to: mentorInfo.email,
          firstName: mentorInfo.firstName,
          menteeName: menteeInfo.firstName || 'Someone',
          requestTitle: data.requestTitle,
          requestMessage: data.requestMessage,
          requestUrl
        }
      }
    })

    if (emailError) {
      console.error('Error sending mentorship request email:', emailError)
      return false
    }

    return emailResult?.success || false
  } catch (error) {
    console.error('Error sending mentorship request notification:', error)
    return false
  }
}

/**
 * Send mentorship request response notification to mentee
 */
export async function sendMentorshipRequestResponseNotification(data: MentorshipRequestResponseNotificationData): Promise<boolean> {
  try {
    // Check if mentee has this notification type enabled
    const isEnabled = await isNotificationEnabled(data.menteeId, 'mentorship_request_responses')
    if (!isEnabled) {
      console.log('Mentorship request response notifications disabled for user:', data.menteeId)
      return true
    }

    // Get mentor and mentee info
    const [mentorInfo, menteeInfo] = await Promise.all([
      getUserInfo(data.mentorId),
      getUserInfo(data.menteeId)
    ])

    if (!mentorInfo || !menteeInfo) {
      console.error('Could not get user information for notification')
      return false
    }

    // Create in-app notification
    await supabase.from('user_notifications').insert({
      user_id: data.menteeId,
      title: `${mentorInfo.firstName || 'Mentor'} ${data.responseStatus} your mentorship request`,
      content: data.requestTitle,
      type: 'mentorship_request_response',
      related_entity_id: data.requestId,
      is_read: false
    })

    // Send email notification
    const dashboardUrl = `${process.env.VITE_APP_URL || 'http://localhost:5173'}/dashboard/mentorship`
    
    const emailResult = await sendEmail({
      type: 'notification',
      data: {
        to: menteeInfo.email,
        firstName: menteeInfo.firstName,
        notificationType: 'mentorship_request_response',
        mentorName: mentorInfo.firstName || 'A mentor',
        requestTitle: data.requestTitle,
        responseStatus: data.responseStatus,
        mentorResponse: data.mentorResponse || '',
        dashboardUrl
      }
    })

    return emailResult.success
  } catch (error) {
    console.error('Error sending mentorship request response notification:', error)
    return false
  }
}

/**
 * Send session reminder notifications to both mentor and mentee
 */
export async function sendMentorshipSessionReminderNotifications(data: MentorshipSessionReminderNotificationData): Promise<boolean> {
  try {
    // Get mentor and mentee info
    const [mentorInfo, menteeInfo] = await Promise.all([
      getUserInfo(data.mentorId),
      getUserInfo(data.menteeId)
    ])

    if (!mentorInfo || !menteeInfo) {
      console.error('Could not get user information for notification')
      return false
    }

    const sessionUrl = `${process.env.VITE_APP_URL || 'http://localhost:5173'}/dashboard/mentorship/sessions/${data.sessionId}`
    const sessionDate = new Date(data.scheduledStartTime).toLocaleDateString()
    const sessionTime = new Date(data.scheduledStartTime).toLocaleTimeString()

    // Send to mentor
    const mentorEnabled = await isNotificationEnabled(data.mentorId, 'mentorship_session_reminders')
    if (mentorEnabled) {
      await Promise.all([
        // In-app notification
        supabase.from('user_notifications').insert({
          user_id: data.mentorId,
          title: `Session reminder: ${data.sessionTitle}`,
          content: `Upcoming session with ${menteeInfo.firstName || 'your mentee'}`,
          type: 'mentorship_session_reminder',
          related_entity_id: data.sessionId,
          is_read: false
        }),
        // Email notification
        sendEmail({
          type: 'notification',
          data: {
            to: mentorInfo.email,
            firstName: mentorInfo.firstName,
            notificationType: 'mentorship_session_reminder',
            participantName: mentorInfo.firstName || 'You',
            otherParticipantName: menteeInfo.firstName || 'your mentee',
            sessionTitle: data.sessionTitle,
            sessionDate,
            sessionTime,
            meetingLink: data.meetingLink || '',
            meetingPlatform: data.meetingPlatform,
            sessionUrl
          }
        })
      ])
    }

    // Send to mentee
    const menteeEnabled = await isNotificationEnabled(data.menteeId, 'mentorship_session_reminders')
    if (menteeEnabled) {
      await Promise.all([
        // In-app notification
        supabase.from('user_notifications').insert({
          user_id: data.menteeId,
          title: `Session reminder: ${data.sessionTitle}`,
          content: `Upcoming session with ${mentorInfo.firstName || 'your mentor'}`,
          type: 'mentorship_session_reminder',
          related_entity_id: data.sessionId,
          is_read: false
        }),
        // Email notification
        sendEmail({
          type: 'notification',
          data: {
            to: menteeInfo.email,
            firstName: menteeInfo.firstName,
            notificationType: 'mentorship_session_reminder',
            participantName: menteeInfo.firstName || 'You',
            otherParticipantName: mentorInfo.firstName || 'your mentor',
            sessionTitle: data.sessionTitle,
            sessionDate,
            sessionTime,
            meetingLink: data.meetingLink || '',
            meetingPlatform: data.meetingPlatform,
            sessionUrl
          }
        })
      ])
    }

    return true
  } catch (error) {
    console.error('Error sending session reminder notifications:', error)
    return false
  }
}
