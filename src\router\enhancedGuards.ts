import { Router, RouteLocationNormalized } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useUserState } from '../services/userStateService'
import { useGlobalServicesStore } from '../stores/globalServices'

// Unified cache will handle authentication checks

/**
 * Unified Route Guards
 *
 * Consolidates all authentication and user state checking logic with intelligent caching
 * to prevent redundant operations and improve performance.
 */
export function setupEnhancedRouteGuards(router: Router) {
  router.beforeEach(async (to: RouteLocationNormalized, from: RouteLocationNormalized) => {
    const authStore = useAuthStore()
    const { userState, isNewUser, hasIncompleteProfile, checkUserState } = useUserState()
    const globalServices = useGlobalServicesStore()
    const cache = globalServices.cacheService

    // Skip auth check for public routes and auth callbacks
    if (to.path === '/auth/callback' || to.path === '/auth/verify' || to.path === '/') {
      return true
    }

    // Quick auth check - don't block navigation for non-critical routes
    if (!authStore.isInitialized) {
      console.log('RouteGuards: Initializing authentication...')
      // Use a timeout to prevent hanging
      const authPromise = authStore.checkSession()
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Auth timeout')), 3000)
      )

      try {
        await Promise.race([authPromise, timeoutPromise])
      } catch (error) {
        console.warn('RouteGuards: Auth check timed out, proceeding with navigation')
      }
    }

    // Handle routes that require authentication
    if (to.matched.some(record => record.meta.requiresAuth)) {
      if (!authStore.isAuthenticated) {
        console.log('RouteGuards: Authentication required, redirecting to sign-in')
        return '/sign-in'
      }

      // Cached user state check to prevent redundant database calls
      const userId = authStore.currentUser?.id
      if (userId) {
        const cacheKey = `auth:userState:${userId}`
        const cached = cache.get<boolean>(cacheKey)

        // Only check user state if not cached
        if (cached === null) {
          try {
            console.log('RouteGuards: Checking user state...')

            // Add timeout to prevent hanging
            const statePromise = checkUserState()
            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('User state timeout')), 2000)
            )

            await Promise.race([statePromise, timeoutPromise])

            // Cache the successful user state check
            cache.set(cacheKey, true, {
              ttl: 5 * 60 * 1000, // 5 minutes
              storage: 'sessionStorage' // Use session storage for auth
            })
            console.log('RouteGuards: User state cached')
          } catch (error) {
            console.warn('RouteGuards: User state check failed or timed out, allowing navigation:', error)
            // Don't block navigation on user state errors
          }
        } else {
          console.log('RouteGuards: Using cached user state')
        }
      }

      // Redirect based on user state if needed
      if (to.matched.some(record => record.meta.requiresCompleteProfile) &&
          hasIncompleteProfile.value) {
        console.log('Route requires complete profile but user has incomplete profile, redirecting to profile dashboard')
        return '/dashboard/profile'
      }

      // Redirect new users to profile creation
      if (to.matched.some(record => record.meta.requiresProfile) &&
          isNewUser.value) {
        console.log('Route requires profile but user is new, redirecting to profile creation')
        return '/dashboard/profile/create'
      }


    }

    // Check if route is guest-only (like sign-in)
    if (to.matched.some(record => record.meta.guestOnly)) {
      if (authStore.isAuthenticated) {
        console.log('Route is guest-only but user is authenticated, redirecting to dashboard')
        return '/dashboard'
      }
    }

    // Check if route requires no profile
    if (to.matched.some(record => record.meta.requiresNoProfile)) {
      if (!isNewUser.value) {
        console.log('Route requires no profile but user has a profile, redirecting to dashboard')
        return '/dashboard'
      }
    }

    return true
  })

  // After each navigation, scroll to top
  router.afterEach((to, from) => {
    // Scroll to top only if the route path changed
    if (to.path !== from.path) {
      window.scrollTo(0, 0)
    }
  })
}

/**
 * Clear authentication cache (useful for testing or when user logs out)
 */
export function clearAuthCache(): void {
  const globalServices = useGlobalServicesStore()
  const cache = globalServices.cacheService
  cache.invalidate('auth:*')
  console.log('RouteGuards: Authentication cache cleared')
}

/**
 * Get cache statistics for monitoring
 */
export function getAuthCacheStats() {
  const globalServices = useGlobalServicesStore()
  const cache = globalServices.cacheService
  const stats = cache.getStats()

  return {
    totalEntries: stats.totalEntries,
    hitRate: stats.hitRate,
    memoryUsage: stats.memoryUsage,
    sessionStorageEntries: stats.sessionStorageEntries
  }
}