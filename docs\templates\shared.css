/* Virtual Community Shared Styles — Concept 1: Glass Shelf Social */
:root{
  --color-bg:#0e1220; /* page background behind glass */
  --color-surface:#ffffff;
  --color-glass:rgba(255,255,255,.08);
  --color-border:rgba(255,255,255,.12);
  --color-text:#111827;
  --color-text-muted:#6b7280;
  --color-accent:#22c55e;
  --color-info:#3b82f6;--color-success:#10b981;--color-warning:#f59e0b;--color-danger:#ef4444;
  /* category colors */
  --category-innovator:#5b8def; --category-student:#6ee7b7; --category-mentor:#f472b6; --category-entrepreneur:#fbbf24;
  /* events */
  --event-date-bg:#111827; --event-date-text:#fefefe;
  /* radii */
  --r-xs:6px; --r-sm:10px; --r-md:14px; --r-lg:18px; --r-xl:24px;
  /* spacing */
  --sp-1:4px; --sp-2:8px; --sp-3:12px; --sp-4:16px; --sp-5:20px; --sp-6:24px; --sp-8:32px; --sp-10:40px;
}
*{box-sizing:border-box}
html,body{height:100%}
body{margin:0;font:16px/1.5 system-ui,-apple-system,Segoe UI,Roboto,Ubuntu;color:var(--color-text);background:linear-gradient(180deg,#0f172a, #111827 30%, #0b1020);}

/* Elevation */
.e1{box-shadow:0 1px 2px rgba(0,0,0,.06),0 1px 8px rgba(0,0,0,.04)}
.e2{box-shadow:0 8px 24px rgba(0,0,0,.14)}

/* App shell */
.app{display:grid;grid-template-rows:auto 1fr;min-height:100vh}
.topbar{position:sticky;top:0;z-index:50;backdrop-filter:saturate(140%) blur(12px);background:linear-gradient(180deg,rgba(255,255,255,.18),rgba(255,255,255,.06));border-bottom:1px solid var(--color-border)}
.topbar .inner{display:flex;align-items:center;gap:12px;padding:10px 16px}
.topbar .brand{display:flex;align-items:center;gap:10px}
.topbar .search{flex:1;display:flex;gap:8px}
.input{flex:1;background:rgba(255,255,255,.9);border:1px solid #e5e7eb;border-radius:999px;padding:10px 14px;outline:0}
.btn{display:inline-flex;align-items:center;gap:8px;border:1px solid #e5e7eb;background:#fff;border-radius:999px;padding:8px 12px;cursor:pointer;transition:all .12s ease}
.btn:hover{transform:translateY(-1px)}
.btn-primary{background:var(--color-accent);border-color:transparent;color:white}
.icon{width:20px;height:20px}

.main{display:grid;grid-template-columns:260px minmax(0,1fr) 320px;gap:16px;padding:16px}
.sidebar{position:sticky;top:64px;height:calc(100vh - 80px)}
.nav{background:var(--color-surface);border-radius:var(--r-lg);padding:10px;border:1px solid #e5e7eb}
.nav a{display:flex;align-items:center;gap:10px;padding:10px;border-radius:10px;color:#374151;text-decoration:none}
.nav a[aria-current="page"], .nav a:hover{background:linear-gradient(90deg,rgba(34,197,94,.12),rgba(34,197,94,.0));}
.nav .soon{opacity:.5;cursor:not-allowed}

/* Right panel */
.right{position:sticky;top:64px;height:calc(100vh - 80px);display:flex;flex-direction:column;gap:16px}
.panel{background:var(--color-surface);border-radius:var(--r-lg);border:1px solid #e5e7eb;padding:12px}
.panel h3{margin:6px 0 8px;font-size:14px;color:#111827}

/* Featured shelf */
.shelf{background:rgba(255,255,255,.7);border:1px solid #e5e7eb;border-radius:var(--r-xl);padding:10px 8px;backdrop-filter:blur(10px)}
.shelf .rail{display:flex;gap:12px;overflow:auto;scroll-snap-type:x mandatory;padding:4px}
.card{min-width:240px;max-width:280px;flex:0 0 auto;background:#fff;border:1px solid #e5e7eb;border-radius:16px;overflow:hidden;scroll-snap-align:start}
.card .media{height:120px;background:#eef2ff;position:relative}
.card .label{position:absolute;left:10px;top:10px;background:rgba(0,0,0,.6);color:white;padding:4px 8px;border-radius:999px;font-size:12px}
.card .body{padding:10px}
.card .title{font-size:14px;font-weight:600;margin:0 0 6px}
.card .meta{font-size:12px;color:#6b7280}

/* Timeline */
.feed{display:flex;flex-direction:column;gap:12px}
.post{background:#fff;border:1px solid #e5e7eb;border-radius:16px;padding:12px}
.post .head{display:flex;gap:10px;align-items:center}
.post .actions{display:flex;gap:12px;color:#6b7280}

/* Blog specific */
.blog-hero{position:relative;background:#000;color:#fff}
.blog-hero::after{content:"";position:absolute;inset:0;background:linear-gradient(180deg,rgba(0,0,0,.0),rgba(0,0,0,.6))}

/* Event badge */
.date-badge{position:absolute;top:10px;right:10px;background:var(--event-date-bg);color:var(--event-date-text);padding:6px 10px;border-top-left-radius:12px;border-bottom-left-radius:12px;border-top-right-radius:0;border-bottom-right-radius:0;font-weight:700;font-size:12px}

/* Profile category heads */
.profile-card .head{height:8px}
.profile-card[data-category="innovator"] .head{background:var(--category-innovator)}
.profile-card[data-category="student"] .head{background:var(--category-student)}
.profile-card[data-category="mentor"] .head{background:var(--category-mentor)}
.profile-card[data-category="entrepreneur"] .head{background:var(--category-entrepreneur)}

/* AI chips */
.chips{display:flex;gap:8px;flex-wrap:wrap}
.chip{border:1px solid #e5e7eb;background:#fff;border-radius:999px;padding:6px 10px;font-size:12px;cursor:pointer}

/* Empty states */
.empty{border:1px dashed #e5e7eb;border-radius:16px;padding:20px;color:#6b7280;text-align:center;background:#fff}

/* Utility */
.hidden{display:none}
.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);border:0}

