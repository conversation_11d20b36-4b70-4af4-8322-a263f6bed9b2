<template>
  <q-page class="q-pa-md">
    <!-- Global loading state for the entire page -->
    <div v-if="isLoading" class="q-pa-md flex flex-center" style="min-height: 200px;">
      <q-spinner color="primary" size="3em" />
      <div class="q-ml-md">Loading profile information...</div>
    </div>

    <div v-else-if="error" class="q-pa-md flex flex-center">
      <unified-icon name="error" color="negative" size="3em" />
      <div class="q-ml-md">{{ error }}</div>
    </div>

    <div v-else class="row q-col-gutter-md">
      <!-- Profile Completion Status (Top) -->
      <div v-if="!isProfileComplete" class="col-12">
        <q-card class="profile-completion-card">
          <q-card-section class="bg-primary text-white">
            <div class="text-h5">Profile Completion</div>
          </q-card-section>

          <q-card-section>
            <div class="row items-center q-mb-md">
              <div class="col">
                <div v-if="isNewUser">
                  <div class="text-h6">Welcome to ZbInnovation!</div>
                  <p class="text-body1">Complete your profile to get the most out of ZbInnovation and connect with like-minded innovators!</p>
                </div>
                <div v-else>
                  <div class="text-h6">Complete Your Profile</div>
                  <p class="text-body1">Your profile is incomplete. Complete it to unlock all features!</p>
                </div>
              </div>
              <div class="col-auto">
                <div class="text-subtitle1 text-weight-medium q-mr-md text-primary">
                  Your profile is incomplete
                </div>
              </div>
            </div>

            <q-linear-progress
              :value="profileCompletion / 100"
              size="10px"
              color="primary"
              class="q-mb-md"
            />

            <div class="row items-center justify-between">
              <div class="col">
                <div class="text-caption text-grey-8">
                  {{ completionSteps }}
                </div>
              </div>
              <div class="col-auto">
                <q-btn
                  color="primary"
                  :label="!profileStore.hasSpecializedProfile ? 'Create Profile' : 'Complete Profile'"
                  @click="goToProfileEdit()"
                >
                  <template v-slot:prepend>
                    <unified-icon :name="!profileStore.hasSpecializedProfile ? 'person_add' : 'edit'" class="q-mr-xs" />
                  </template>
                </q-btn>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- Personal Details Section (Left) -->
      <div class="col-12 col-md-5">
        <q-card class="personal-details-card">
          <q-card-section class="bg-primary text-white">
            <div class="text-h5">Personal Details</div>
          </q-card-section>

          <q-card-section>
            <div class="row items-center q-mb-md">
              <div class="col-auto">
                <q-avatar size="80px" class="bg-grey-3">
                  <img v-if="profileStore.currentProfile?.avatar_url" :src="profileStore.currentProfile.avatar_url" alt="Profile Avatar">
                  <div v-else class="text-primary flex flex-center full-height">
                    {{ getInitials(profileStore.currentProfile?.first_name, profileStore.currentProfile?.last_name) }}
                  </div>
                </q-avatar>
              </div>
              <div class="col q-ml-md">
                <div class="text-h6">{{ profileStore.currentProfile?.first_name || '' }} {{ profileStore.currentProfile?.last_name || '' }}</div>
                <div class="text-subtitle2">{{ profileStore.currentProfile?.email }}</div>
              </div>
            </div>

            <q-list bordered separator>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Email</q-item-label>
                  <q-item-label>{{ profileStore.currentProfile?.email }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section>
                  <q-item-label caption>Phone</q-item-label>
                  <q-item-label>
                    <span v-if="profileStore.currentProfile?.phone_country_code && profileStore.currentProfile?.phone_number">
                      {{ profileStore.currentProfile?.phone_country_code }} {{ profileStore.currentProfile?.phone_number }}
                    </span>
                    <span v-else>Not provided</span>
                  </q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section>
                  <q-item-label caption>Bio</q-item-label>
                  <q-item-label>{{ profileStore.currentProfile?.bio || 'Not provided' }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section>
                  <q-item-label caption>Profile Created</q-item-label>
                  <q-item-label>{{ formatDate(profileStore.currentProfile?.created_at) }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>

            <div class="q-mt-md">
              <q-btn color="primary" label="Edit Personal Details" @click="goToProfileEdit()">
                <template v-slot:prepend>
                  <unified-icon name="edit" class="q-mr-xs" />
                </template>
              </q-btn>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- My Profiles Section (Right) -->
      <div class="col-12 col-md-7">
        <q-card class="my-profiles-card">
          <q-card-section class="bg-primary text-white">
            <div class="row items-center">
              <div class="col">
                <div class="text-h5">My Profile</div>
              </div>
              <div class="col-auto">
                <q-btn
                  round
                  flat
                  color="white"
                  icon="refresh"
                  size="sm"
                  @click="refreshProfileData"
                  :loading="isLoading"
                >
                  <q-tooltip>Refresh profile data</q-tooltip>
                </q-btn>
              </div>
            </div>
          </q-card-section>

          <q-card-section>


            <!-- Create New Profile Button hidden for now -->
            <!-- Multiple profile functionality is kept but hidden from UI -->
            <div v-if="authStore.isAdmin" class="row items-center justify-between q-mb-md">
              <div class="col">
                <div class="text-subtitle1">{{ profileStore.userProfiles.length > 0 ? 'Your Profile' : 'No Profile Yet' }}</div>
              </div>
              <div class="col-auto">
                <q-btn
                  color="primary"
                  label="Create New Profile"
                  :disable="hasIncompleteProfile"
                  :to="{ name: 'profile-create' }"
                  icon="add"
                  size="sm"
                >
                  <q-tooltip v-if="hasIncompleteProfile">
                    Please complete your in-progress profile before creating a new one
                  </q-tooltip>
                </q-btn>
              </div>
            </div>

            <!-- Profile List -->
            <div v-if="!isLoading">
              <!-- Show empty state if no profiles exist -->
              <div v-if="isNewUser" class="text-center q-pa-xl">
                <unified-icon name="person_add" size="64px" class="text-grey q-mb-md" />
                <div class="text-h6">Create Your First Profile</div>
                <p class="text-body1 q-mb-lg">To get started, create your first profile by selecting your role in the ecosystem.</p>
                <q-btn
                  color="primary"
                  label="Create Your Profile"
                  :to="{ name: 'profile-create' }"
                  size="lg"
                >
                  <template v-slot:prepend>
                    <unified-icon name="person_add" class="q-mr-xs" />
                  </template>
                </q-btn>
              </div>

              <!-- Show profile cards if profiles exist - Only showing the primary profile -->
              <div v-else>
                <!-- Only show the first/primary profile -->
                <div v-if="profileStore.userProfiles.length > 0">
                  <q-card class="profile-card">
                    <q-card-section>
                      <div class="row items-center">
                        <div class="col">
                          <div class="text-h6">{{ profileStore.userProfiles[0].profile_name || 'My Profile' }}</div>
                          <div class="row items-center q-mt-xs">
                            <q-badge :color="getProfileTypeColor(profileStore.userProfiles[0].profile_type)" text-color="white" class="q-mr-xs">
                              {{ formatProfileType(profileStore.userProfiles[0].profile_type) }}
                            </q-badge>
                            <q-badge :color="getProfileStateColor(profileStore.userProfiles[0].profile_state)" text-color="white">
                              {{ formatProfileState(profileStore.userProfiles[0].profile_state) }}
                            </q-badge>
                          </div>
                        </div>
                        <div class="col-auto">
                          <q-circular-progress
                            :value="getProfileCompletion(profileStore.userProfiles[0])"
                            size="60px"
                            :thickness="0.2"
                            :color="getCompletionColor(profileStore.userProfiles[0])"
                            track-color="grey-3"
                            class="q-mr-md"
                          >
                            <div class="text-caption">{{ getProfileCompletion(profileStore.userProfiles[0]) }}%</div>
                          </q-circular-progress>
                        </div>
                      </div>
                    </q-card-section>

                    <!-- Profile Completion Status removed as requested -->

                    <q-card-section>
                      <div class="text-caption text-grey-8 q-mb-sm">Last updated: {{ formatDate(profileStore.userProfiles[0].updated_at) }}</div>
                    </q-card-section>

                    <q-card-actions align="right">
                      <q-btn
                        flat
                        color="primary"
                        :to="{ name: 'profile-view', params: { id: profileStore.userProfiles[0].user_id }}"
                        label="View Profile"
                      >
                        <template v-slot:prepend>
                          <unified-icon name="visibility" class="q-mr-xs" />
                        </template>
                      </q-btn>
                      <q-btn
                        color="primary"
                        :to="{ name: 'profile-edit', params: { id: profileStore.userProfiles[0].user_id }}"
                        label="Edit Profile"
                      >
                        <template v-slot:prepend>
                          <unified-icon name="edit" class="q-mr-xs" />
                        </template>
                      </q-btn>
                    </q-card-actions>
                  </q-card>

                  <!-- Hidden section for admins to see all profiles -->
                  <div v-if="authStore.isAdmin && profileStore.userProfiles.length > 1" class="q-mt-md">
                    <q-expansion-item
                      label="Additional Profiles (Admin View)"
                      caption="This section is only visible to admins"
                      header-class="text-primary"
                    >
                      <q-card>
                        <q-card-section>
                          <q-list bordered separator>
                            <q-item v-for="(profile, index) in profileStore.userProfiles.slice(1)" :key="profile.user_id" clickable :to="{ name: 'profile-view', params: { id: profile.user_id }}">
                              <q-item-section>
                                <q-item-label>{{ profile.profile_name || `Profile ${index + 2}` }}</q-item-label>
                                <q-item-label caption>
                                  <q-badge :color="getProfileTypeColor(profile.profile_type)" text-color="white" class="q-mr-xs">
                                    {{ formatProfileType(profile.profile_type) }}
                                  </q-badge>
                                  <q-badge :color="getProfileStateColor(profile.profile_state)" text-color="white">
                                    {{ formatProfileState(profile.profile_state) }}
                                  </q-badge>
                                </q-item-label>
                              </q-item-section>
                              <q-item-section side>
                                <q-circular-progress
                                  :value="getProfileCompletion(profile)"
                                  size="40px"
                                  :thickness="0.2"
                                  :color="getCompletionColor(profile)"
                                  track-color="grey-3"
                                >
                                  <div class="text-caption">{{ getProfileCompletion(profile) }}%</div>
                                </q-circular-progress>
                              </q-item-section>
                            </q-item>
                          </q-list>
                        </q-card-section>
                      </q-card>
                    </q-expansion-item>
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Profile completion popup -->
    <q-dialog v-model="showProfileCompletionPopup" persistent>
      <profile-completion-popup
        v-model="showProfileCompletionPopup"
        :is-initial="isNewUser"
        @remind-later="handleRemindLater"
      />
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useProfileStore, type BaseProfile } from '../stores/profile'
import { useScrollReset } from '../composables/useScrollReset'
import UnifiedIcon from '../components/ui/UnifiedIcon.vue'
import ProfileCompletionPopup from '../components/profile/ProfileCompletionPopup.vue'
// ProfileCompletionStatus component removed as requested

// Import simplified services
import { useUserState } from '../services/userStateService'
import { useGlobalServicesStore } from '../stores/globalServices'

const router = useRouter()
const authStore = useAuthStore()
const profileStore = useProfileStore()

// Use the scroll reset composable for navigation
useScrollReset()

// Use simplified services
const isLoading = ref(false)
const globalServices = useGlobalServicesStore()
const { isNewUser, hasIncompleteProfile, checkUserState } = useUserState()
const { isProfileComplete } = globalServices.profileCompletionService

// State
const error = ref('')

// Function to refresh profile data
async function refreshProfileData() {
  try {
    isLoading.value = true

    // Reset and reload profiles
    profileStore.reset()
    await profileStore.loadUserProfiles()

    // Check user state to update UI
    await checkUserState()
  } catch (err: any) {
    console.error('Error refreshing profile data:', err)
    error.value = err.message || 'Failed to load profile information'
  } finally {
    isLoading.value = false
  }
}

// Load profiles on mount
onMounted(async () => {
  try {
    isLoading.value = true

    // Load user profiles
    await profileStore.loadUserProfiles()

    // Check user state
    await checkUserState()
  } catch (err: any) {
    console.error('Error loading profile data:', err)
    error.value = err.message || 'Failed to load profile information'
  } finally {
    isLoading.value = false
  }
})

// Helper functions
function getInitials(firstName?: string, lastName?: string): string {
  const first = firstName ? firstName.charAt(0).toUpperCase() : ''
  const last = lastName ? lastName.charAt(0).toUpperCase() : ''
  return first + last || '?'
}

// Get profile completion percentage from the profile
function getProfileCompletion(profile: BaseProfile): number {
  // Use the stored profile_completion value for consistency
  return profile.profile_completion || 0
}

function formatDate(dateString?: string): string {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

function formatProfileType(type: string | null | undefined): string {
  if (!type) return 'No Type'

  // Convert snake_case to Title Case
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

function formatProfileState(state: string | null | undefined): string {
  if (!state) return 'Unknown'

  // Convert UPPER_CASE to Title Case
  return state
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}

function getProfileTypeColor(type: string | null | undefined): string {
  if (!type) return 'grey'

  const colors: Record<string, string> = {
    'innovator': 'purple',
    'investor': 'green',
    'mentor': 'blue',
    'professional': 'teal',
    'industry_expert': 'deep-orange',
    'academic_student': 'indigo',
    'academic_institution': 'light-blue',
    'organisation': 'amber'
  }

  return colors[type] || 'grey'
}

function getProfileStateColor(state: string | undefined): string {
  if (!state) return 'grey'

  const colors: Record<string, string> = {
    'DRAFT': 'grey',
    'IN_PROGRESS': 'blue',
    'PENDING_APPROVAL': 'orange',
    'ACTIVE': 'green',
    'DISABLED': 'red',
    'DECLINED': 'red-6'
  }

  return colors[state] || 'grey'
}

function getCompletionColor(profile: BaseProfile): string {
  const completion = getProfileCompletion(profile)
  if (completion < 30) return 'red'
  if (completion < 70) return 'orange'
  if (completion < 100) return 'blue'
  return 'green'
}

function getProfileCompletionMessage(profile: BaseProfile): string {
  const completion = getProfileCompletion(profile)
  if (completion < 30) return 'Just getting started! Complete your profile to unlock all features.'
  if (completion < 70) return 'Good progress! Continue filling out your profile details.'
  if (completion < 100) return 'Almost there! Just a few more fields to complete.'
  return 'Your profile is complete! You can always update it later.'
}

function goToProfileEdit(): void {
  if (profileStore.currentProfile && profileStore.currentProfile.user_id) {
    router.push({
      name: 'profile-edit',
      params: { id: profileStore.currentProfile.user_id }
    })
  } else {
    router.push({ name: 'profile-create' })
  }
}

function handleRemindLater() {
  // Store the current time in localStorage
  localStorage.setItem('profileCompletionRemindLater', Date.now().toString())
  // Close the popup (if we had one)
}
</script>

<style scoped>
.profile-completion-card,
.personal-details-card,
.my-profiles-card {
  border-radius: 8px;
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.profile-completion-card:hover,
.personal-details-card:hover,
.my-profiles-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.profile-card {
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}
</style>
