import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'

const SUPABASE_URL = Deno.env.get('ZB_SUPABASE_URL') || 'https://dpicnvisvxpmgjtbeicf.supabase.co'

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed. Use POST.' }),
        { 
          status: 405, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const body = await req.json()
    
    if (!body.email) {
      return new Response(
        JSON.stringify({ error: 'Email is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Testing welcome email for:', body.email)

    // Call the welcome email function
    const welcomeEmailResponse = await fetch(`${SUPABASE_URL}/functions/v1/welcome-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': req.headers.get('Authorization') || ''
      },
      body: JSON.stringify({
        email: body.email,
        firstName: body.firstName || 'Test',
        lastName: body.lastName || 'User'
      })
    })

    const welcomeEmailData = await welcomeEmailResponse.json()

    if (!welcomeEmailResponse.ok) {
      console.error('Failed to send welcome email:', welcomeEmailData)
      return new Response(
        JSON.stringify({ 
          error: 'Failed to send welcome email',
          details: welcomeEmailData 
        }),
        { 
          status: welcomeEmailResponse.status, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Welcome email sent successfully:', welcomeEmailData)

    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'Welcome email sent successfully',
        data: welcomeEmailData
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in test-welcome-email function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
