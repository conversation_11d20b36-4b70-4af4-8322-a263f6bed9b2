# ZbInnovation Platform - Product Requirements Document (PRD)

## Overview

This directory contains the comprehensive Product Requirements Document (PRD) for the ZbInnovation innovation ecosystem platform. The documentation is organized to provide clear specifications for development teams implementing the platform using the correct technology stack.

## Correct Technology Stack

Based on codebase analysis, the ZbInnovation platform uses:

### Frontend
- **Framework**: Vue.js 3 with TypeScript (Composition API)
- **UI Framework**: Quasar Framework v2.x
- **State Management**: Pinia
- **Router**: Vue Router 4
- **Build Tool**: Vite
- **Styling**: SCSS with Quasar variables

### Backend
- **Primary Backend**: Supabase (PostgreSQL + Edge Functions)
- **Database**: PostgreSQL with pg_vector extension for AI
- **Authentication**: Supabase Auth with PKCE flow
- **Storage**: Supabase Storage
- **Real-time**: Supabase Realtime subscriptions

### AI Integration
- **Primary AI**: Claude API for conversations
- **Secondary AI**: DeepSeek API for backup/specialized tasks
- **Vector Database**: Supabase pg_vector for embeddings
- **Conversation Memory**: Persistent chat history in PostgreSQL

## Directory Structure

```
docs/zbinnovation-prd/
├── README.md                           # This overview file
├── ZbInnovation-Platform-PRD.md        # Main PRD document
├── forms-and-interactions/             # Forms and UI specifications
│   ├── README.md                       # Forms documentation overview
│   ├── schemas/                        # JSON schema files for forms
│   │   ├── auth-forms.json            # Authentication form schemas
│   │   ├── profile-forms.json         # Profile creation/editing schemas
│   │   ├── post-creation-forms.json   # Content creation form schemas
│   │   └── [other form schemas]
│   ├── specifications/                 # Detailed form specifications
│   │   ├── authentication-forms.md    # Auth forms detailed specs
│   │   ├── profile-management-forms.md # Profile forms detailed specs
│   │   └── [other specifications]
│   ├── user-journeys/                 # Complete user journey documentation
│   │   ├── authentication-flows.md    # Sign-in/up/reset flows
│   │   └── [other journey maps]
│   ├── ui-process-flows/              # Visual process flow diagrams
│   │   ├── form-validation-flows.md   # Validation and error handling
│   │   └── [other process flows]
│   ├── technical-implementation/      # Technical mapping documentation
│   │   ├── component-mapping.md       # Vue.js component mappings
│   │   └── [other technical docs]
│   └── dashboard-functionality/       # Dashboard-specific documentation
│       ├── dashboard-overview.md      # Dashboard structure and layout
│       └── [other dashboard docs]
└── ai-integration/                    # AI implementation specifications
    ├── ai-requirements.md             # AI integration requirements
    ├── ai-architecture.md             # AI system architecture
    └── ai-implementation-plan.md      # Implementation roadmap
```

## Key Corrections Made

### Technology Stack Corrections
- ❌ **Incorrect**: React frontend with Java backend
- ✅ **Correct**: Vue.js 3 frontend with Supabase backend

### Framework Specifications
- **Frontend**: Vue.js 3 with Composition API and TypeScript
- **UI Components**: Quasar Framework (not React components)
- **State Management**: Pinia stores (not Redux/Context)
- **Routing**: Vue Router (not React Router)
- **Forms**: Quasar form components with Vue validation

### Backend Architecture
- **API Layer**: Supabase Edge Functions (not Java Spring Boot)
- **Database**: PostgreSQL via Supabase (not separate Java ORM)
- **Authentication**: Supabase Auth (not Java Security)
- **File Storage**: Supabase Storage (not Java file handling)

## AI Integration Context

The platform already has a strong AI foundation as documented in the existing AI implementation files:

- **pg_vector extension** already enabled for vector embeddings
- **Existing AI infrastructure** with conversation and message storage
- **Comprehensive profile system** with 8 profile types
- **Basic matchmaking system** already implemented

The PRD builds upon this existing foundation rather than starting from scratch.

## Usage Guidelines

### For Vue.js Developers
1. **Component Structure**: Follow Vue.js 3 Composition API patterns
2. **State Management**: Use Pinia stores for global state
3. **UI Components**: Leverage Quasar Framework components
4. **Validation**: Use Quasar validation rules and patterns
5. **Routing**: Implement Vue Router navigation guards

### For Supabase Developers
1. **Edge Functions**: Implement API endpoints as Supabase Edge Functions
2. **Database**: Use PostgreSQL with proper RLS policies
3. **Authentication**: Leverage Supabase Auth with PKCE flow
4. **Real-time**: Implement live updates with Supabase Realtime
5. **Storage**: Use Supabase Storage for file uploads

### For AI Integration
1. **Vector Embeddings**: Use existing pg_vector setup
2. **Conversation Memory**: Build on existing AI tables
3. **Context Awareness**: Integrate with Supabase Auth state
4. **Hybrid Approach**: Combine RAG and SQL as documented
5. **Performance**: Optimize for real-time AI responses

## Integration with Existing Documentation

This PRD complements the existing AI documentation in the main docs folder:

- **AI Implementation**: References `docs/ai-implementation-final-recommendations.md`
- **AI Architecture**: Builds on `docs/ai-enhancement-comprehensive-plan.md`
- **Database Schema**: Uses existing schema from `docs/ai-implementation-database-migrations.md`
- **Trigger Strategy**: Implements `docs/AI_TRIGGER_PLACEMENT_STRATEGY.md`

## Development Workflow

1. **Review Existing AI Docs**: Start with the AI implementation documentation
2. **Understand Current Architecture**: Review the Vue.js + Supabase setup
3. **Follow PRD Specifications**: Use this PRD for new feature development
4. **Maintain Consistency**: Ensure all implementations follow established patterns
5. **Test Integration**: Validate against existing AI and platform functionality

---

*This PRD provides accurate, implementation-ready specifications for the ZbInnovation platform using the correct Vue.js + Supabase technology stack.*
