<template>
  <q-page>
    <HeroSection />
    <CountdownSection />
    <div id="news-section" class="news-section">
      <NewsSection />
    </div>

    <div id="signup-section" class="sign-up-section q-py-md">
      <div class="container q-mx-auto q-px-md">
        <div class="row">
          <div class="col-1" />
          <div class="col-10">
            <div class="text-h3 text-weight-light q-mb-md text-center" style="color: #0D8A3E">Join Our Early Access Program</div>
            <p class="text-body1 text-center q-mb-lg" style="max-width: 800px; margin: 0 auto;">
              Be among the first to access our exclusive innovation ecosystem. Early members receive priority matchmaking with potential partners, mentors, and investors, plus special access to resources and events before our official launch. Shape the future of innovation with us!
            </p>
          </div>
          <div class="col-1" />
        </div>
        <EarlyAccessForm />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue'
import TheFooter from '../components/TheFooter.vue'

const HeroSection = defineAsyncComponent(() => import('../../components/public/HeroSection.vue'))
const CountdownSection = defineAsyncComponent(() => import('../../components/public/CountdownSection.vue'))
const NewsSection = defineAsyncComponent(() => import('../../components/public/NewsSection.vue'))
const EarlyAccessForm = defineAsyncComponent(() => import('../../components/public/EarlyAccessForm.vue'))
</script>

<style scoped>
.container {
  width: 100%;
  max-width: 1400px;
}

.sign-up-section {
  background-color: #f5f5f5;
  scroll-margin-top: 60px; /* Reduced space for the fixed header */
  scroll-behavior: smooth;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.news-section {
  scroll-margin-top: 60px; /* Reduced space for the fixed header */
  scroll-behavior: smooth;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

/* Rubik font for the entire app */
body {
  font-family: 'Rubik', sans-serif;
  scroll-behavior: smooth;
}

@media (max-width: 599px) {
  .container {
    padding: 0 16px !important;
  }

  .col-1 {
    display: none;
  }

  .col-10 {
    flex-basis: 100%;
    max-width: 100%;
  }

  .sign-up-section {
    padding-top: 15px !important;
    padding-bottom: 15px !important;
  }

  .news-section {
    padding-top: 15px !important;
    padding-bottom: 15px !important;
  }

  .q-py-xl {
    padding-top: 15px !important;
    padding-bottom: 15px !important;
  }
}
</style>