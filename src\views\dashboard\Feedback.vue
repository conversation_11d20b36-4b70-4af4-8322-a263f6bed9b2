<template>
  <q-page padding>
    <div class="row q-col-gutter-lg">
      <!-- Feedback Form -->
      <div class="col-12 col-md-8">
        <q-card>
          <q-card-section>
            <div class="text-h5 q-mb-md">Share Your Feedback</div>
            <div class="text-body1 text-grey-8 q-mb-lg">
              Your feedback helps us improve the Innovation Hub experience for everyone.
            </div>

            <q-form @submit.prevent="onSubmit" class="q-gutter-md">
              <!-- Feedback Type -->
              <div>
                <div class="text-subtitle2 q-mb-sm">What type of feedback do you have?</div>
                <div class="row q-col-gutter-sm">
                  <div v-for="type in feedbackTypes" :key="type.value" class="col-6 col-md-3">
                    <q-btn
                      :label="type.label"
                      :color="feedbackType === type.value ? 'primary' : 'grey-3'"
                      :text-color="feedbackType === type.value ? 'white' : 'black'"
                      class="full-width"
                      @click="feedbackType = type.value"
                    />
                  </div>
                </div>
              </div>

              <!-- Rating -->
              <div>
                <div class="text-subtitle2 q-mb-sm">How would you rate your experience?</div>
                <div class="row items-center q-gutter-x-md">
                  <q-rating
                    v-model="rating"
                    size="2em"
                    color="amber"
                    icon="star_border"
                    icon-selected="star"
                    :max="5"
                  />
                  <span class="text-subtitle1">{{ rating }} / 5</span>
                </div>
              </div>

              <!-- Feedback Details -->
              <q-input
                v-model="feedback"
                type="textarea"
                label="Tell us more about your experience"
                rows="4"
                outlined
                :rules="[val => !!val || 'Please provide your feedback']"
              />

              <!-- Contact Permission -->
              <q-checkbox
                v-model="canContact"
                label="You can contact me about this feedback"
              />

              <!-- Submit Button -->
              <div class="q-mt-lg">
                <q-btn
                  type="submit"
                  color="primary"
                  label="Submit Feedback"
                  :loading="feedbackStore.loading"
                  :disable="feedbackStore.loading || rating === 0 || !feedback.trim()"
                />
              </div>
            </q-form>
          </q-card-section>
        </q-card>
      </div>

      <!-- Recent Feedback -->
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">Your Recent Feedback</div>
            <q-inner-loading :showing="feedbackStore.loading">
              <q-spinner-dots size="50px" color="primary" />
            </q-inner-loading>
            <q-list separator>
              <template v-if="feedbackStore.recentFeedback.length">
                <q-item v-for="item in feedbackStore.recentFeedback" :key="item.id">
                  <q-item-section>
                    <q-item-label>{{ formatFeedbackType(item.feedback_type) }}</q-item-label>
                    <q-item-label caption>{{ formatDate(item.created_at) }}</q-item-label>
                    <div class="q-mt-xs">
                      <q-rating
                        :model-value="item.rating || 0"
                        size="1em"
                        color="amber"
                        icon="star_border"
                        icon-selected="star"
                        :max="5"
                        readonly
                      />
                    </div>
                  </q-item-section>
                  <q-item-section side>
                    <q-chip
                      :color="getStatusColor(item.status)"
                      text-color="white"
                      size="sm"
                    >
                      {{ formatStatus(item.status) }}
                    </q-chip>
                  </q-item-section>
                </q-item>
              </template>
              <q-item v-else>
                <q-item-section class="text-center text-grey">
                  No feedback submitted yet
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useFeedbackStore } from '../../stores/feedback'
import { useNotificationStore } from '../../stores/notifications'
import { date } from 'quasar'

const notifications = useNotificationStore()

const feedbackStore = useFeedbackStore()

const feedbackTypes = [
  { label: 'General', value: 'GENERAL' },
  { label: 'Bug Report', value: 'BUG_REPORT' },
  { label: 'Feature Request', value: 'FEATURE_REQUEST' },
  { label: 'Other', value: 'OTHER' }
]

const feedbackType = ref('GENERAL')
const rating = ref(0)
const feedback = ref('')
const canContact = ref(false)

const formatDate = (dateStr: string) => {
  return date.formatDate(dateStr, 'YYYY-MM-DD')
}

const formatFeedbackType = (type: string) => {
  // Convert UPPER_CASE to Title Case
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    'pending': 'warning',
    'reviewed': 'info',
    'resolved': 'positive'
  }
  return colors[status] || 'grey'
}

const formatStatus = (status: string) => {
  return status.charAt(0).toUpperCase() + status.slice(1)
}

const onSubmit = async () => {
  // Validate form
  if (rating.value === 0) {
    notifications.error('Please provide a rating')
    return
  }

  if (!feedback.value.trim()) {
    notifications.error('Please provide feedback details')
    return
  }

  try {
    console.log('Submitting feedback:', {
      feedback_type: feedbackType.value,
      rating: rating.value,
      content: feedback.value
    })

    const success = await feedbackStore.submitFeedback({
      feedback_type: feedbackType.value,
      rating: rating.value,
      content: feedback.value
    })

    if (success) {
      // Reset form
      feedbackType.value = 'GENERAL'
      rating.value = 0
      feedback.value = ''
      canContact.value = false

      // Refresh the feedback list
      await feedbackStore.fetchUserFeedback()

      notifications.success('Thank you for your feedback!')
    }
  } catch (error) {
    console.error('Error in onSubmit:', error)
  }
}

onMounted(() => {
  feedbackStore.fetchUserFeedback()
})
</script>

<style scoped>
/* Custom styles for the feedback form */
.q-rating {
  margin-bottom: 4px;
}
</style>