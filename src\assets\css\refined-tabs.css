/* 
 * Refined Tab Icons CSS
 * This file contains specific styling for the tab icons in the virtual community page
 * to make them more refined and visually appealing while maintaining the same colors.
 */

/* Base tab icon refinements - specifically for community tabs */
.community-tabs .q-tab__icon {
  /* Use SVG filter for subtle refinement */
  filter: drop-shadow(0 0.5px 0.5px rgba(0, 0, 0, 0.05));
  transition: all 0.2s ease;
  transform-origin: center;
  will-change: transform, opacity;
  font-size: 1.1rem !important; /* Refined size */
  margin-bottom: 0.25rem !important;
  opacity: 0.8; /* Subtle transparency */
}

/* Tab-specific icon refinements for community tabs */
.community-tabs .q-tab[name="feed"] .q-tab__icon {
  /* RSS feed icon */
  transform: scale(0.95); /* Slightly smaller */
}

.community-tabs .q-tab[name="profiles"] .q-tab__icon {
  /* People icon */
  transform: scale(0.95);
}

.community-tabs .q-tab[name="blog"] .q-tab__icon {
  /* Article icon */
  transform: scale(0.95);
}

.community-tabs .q-tab[name="events"] .q-tab__icon {
  /* Event icon */
  transform: scale(0.95);
}

.community-tabs .q-tab[name="groups"] .q-tab__icon {
  /* Groups icon */
  transform: scale(0.95);
}

.community-tabs .q-tab[name="marketplace"] .q-tab__icon {
  /* Storefront icon */
  transform: scale(0.95);
}

/* Active tab refinements for community tabs */
.community-tabs .q-tab--active .q-tab__icon {
  transform: scale(1) !important; /* Return to normal size when active */
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
  opacity: 1 !important; /* Full opacity for active tab */
}

/* Hover effects for community tabs */
.community-tabs .q-tab:hover:not(.q-tab--active) .q-tab__icon {
  transform: scale(0.98);
  opacity: 0.9;
}

/* Tab indicator refinements for community tabs */
.community-tabs .q-tabs__indicator {
  height: 3px !important;
  border-radius: 1.5px 1.5px 0 0;
}

/* Community tab label refinements */
.community-tabs .q-tab__label {
  font-size: 0.85rem !important;
  font-weight: 500 !important;
  letter-spacing: 0.01em;
  margin-top: 0.1rem;
}

/* Material icon specific fixes for these tabs */
.material-icons.rss_feed,
.material-icons.people,
.material-icons.article,
.material-icons.event,
.material-icons.groups,
.material-icons.storefront {
  font-size: inherit !important;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Mobile-specific adjustments for community tabs */
@media (max-width: 599px) {
  .community-tabs .q-tab__icon {
    font-size: 1rem !important;
    margin-bottom: 2px !important;
  }

  .community-tabs .q-tab {
    padding: 0.4rem 0.5rem !important;
    min-height: 44px !important;
  }

  .community-tabs .q-tab__label {
    font-size: 0.75rem !important;
  }
}

/* Tablet-specific adjustments for community tabs */
@media (min-width: 600px) and (max-width: 1023px) {
  .community-tabs .q-tab__icon {
    font-size: 1.05rem !important;
    margin-bottom: 3px !important;
  }

  .community-tabs .q-tab__label {
    font-size: 0.8rem !important;
  }
}

/* Desktop refinements for community tabs */
@media (min-width: 1024px) {
  .community-tabs .q-tab {
    padding: 0.75rem 1rem !important;
    min-height: 52px !important;
  }

  .community-tabs .q-tab:hover {
    background-color: rgba(13, 138, 62, 0.04); /* Subtle hover background */
  }
}
