# Phase 2: Frontend Performance & Bundle Optimization

## Overview
This phase focuses on optimizing component rendering, implementing advanced code splitting, reducing bundle size by 40%, and improving loading performance for better user experience.

## Current State Analysis

### Bundle Analysis Results
```bash
# Current bundle composition (estimated)
Total Bundle Size: ~1.2MB
├── Vendor Libraries: ~800KB
│   ├── Vue.js: ~150KB
│   ├── Quasar: ~400KB
│   ├── Supabase: ~100KB
│   ├── Icon Libraries: ~150KB (multiple sets loaded)
├── Application Code: ~300KB
├── Assets: ~100KB
```

### Performance Issues Identified
1. **Large Initial Bundle**: 1.2MB+ initial load
2. **Icon Library Bloat**: Loading multiple complete icon sets
3. **Inefficient Code Splitting**: Basic route-level splitting only
4. **Component Rendering**: Large lists without virtualization
5. **Asset Loading**: Unoptimized images and fonts

## Implementation Strategy

### 1. Advanced Code Splitting

#### 1.1 Route-Based Code Splitting Enhancement
```typescript
// BEFORE: Basic route splitting
{
  path: '/dashboard',
  component: () => import('../views/Dashboard.vue')
}

// AFTER: Advanced splitting with preloading and chunk naming
{
  path: '/dashboard',
  component: () => import(
    /* webpackChunkName: "dashboard" */
    /* webpackPreload: true */
    '../views/Dashboard.vue'
  ),
  children: [
    {
      path: 'profile',
      component: () => import(
        /* webpackChunkName: "profile" */
        /* webpackPrefetch: true */
        '../views/dashboard/Profile.vue'
      )
    }
  ]
}
```

#### 1.2 Component-Level Code Splitting
```vue
<template>
  <div>
    <!-- Heavy components loaded on demand -->
    <Suspense>
      <template #default>
        <AsyncChartComponent v-if="showChart" :data="chartData" />
      </template>
      <template #fallback>
        <q-skeleton type="rect" width="100%" height="300px" />
      </template>
    </Suspense>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue'

// Lazy load heavy components
const AsyncChartComponent = defineAsyncComponent({
  loader: () => import(
    /* webpackChunkName: "chart-component" */
    '../components/charts/ChartComponent.vue'
  ),
  loadingComponent: () => h('div', 'Loading chart...'),
  errorComponent: () => h('div', 'Failed to load chart'),
  delay: 200,
  timeout: 3000
})
</script>
```

#### 1.3 Store-Level Code Splitting
```typescript
// Lazy load stores
export const useLazyStore = () => {
  return import('../stores/heavyStore').then(module => module.useHeavyStore())
}

// In components
const loadHeavyFeature = async () => {
  const heavyStore = await useLazyStore()
  await heavyStore.initializeHeavyFeature()
}
```

### 2. Bundle Size Optimization

#### 2.1 Icon Library Optimization
```typescript
// BEFORE: Loading all icon sets
import "@quasar/extras/material-icons/material-icons.css"
import "@quasar/extras/fontawesome-v6/fontawesome-v6.css"
import "@quasar/extras/mdi-v6/mdi-v6.css"

// AFTER: Selective icon loading
// Create icon registry
export class IconRegistry {
  private loadedSets = new Set<string>()
  
  async loadIconSet(setName: 'material' | 'fontawesome' | 'mdi') {
    if (this.loadedSets.has(setName)) return
    
    switch (setName) {
      case 'material':
        await import("@quasar/extras/material-icons/material-icons.css")
        break
      case 'fontawesome':
        await import("@quasar/extras/fontawesome-v6/fontawesome-v6.css")
        break
      case 'mdi':
        await import("@quasar/extras/mdi-v6/mdi-v6.css")
        break
    }
    
    this.loadedSets.add(setName)
  }
}

// Use in components
const iconRegistry = new IconRegistry()
await iconRegistry.loadIconSet('material')
```

#### 2.2 Quasar Component Tree-Shaking
```typescript
// quasar.config.js - Enable tree-shaking
module.exports = {
  framework: {
    components: [
      // Only import used components
      'QBtn',
      'QCard',
      'QInput',
      'QSelect',
      'QTable',
      'QDialog'
    ],
    directives: [
      'Ripple',
      'ClosePopup'
    ],
    plugins: [
      'Dialog',
      'Notify',
      'Loading'
    ]
  }
}
```

#### 2.3 Advanced Vite Configuration
```typescript
// vite.config.ts - Optimized build configuration
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunking strategy
          if (id.includes('node_modules')) {
            if (id.includes('vue')) return 'vue-vendor'
            if (id.includes('quasar')) return 'ui-vendor'
            if (id.includes('@supabase')) return 'supabase-vendor'
            if (id.includes('openai')) return 'ai-vendor'
            return 'vendor'
          }
          
          // Application chunking
          if (id.includes('src/components/feed')) return 'feed-components'
          if (id.includes('src/components/profile')) return 'profile-components'
          if (id.includes('src/stores')) return 'stores'
          if (id.includes('src/services')) return 'services'
        }
      }
    },
    
    // Optimization settings
    target: 'es2020',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info']
      },
      mangle: {
        safari10: true
      }
    },
    
    // Performance budgets
    chunkSizeWarningLimit: 600,
    assetsInlineLimit: 4096
  },
  
  // Dependency optimization
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'quasar',
      '@supabase/supabase-js'
    ],
    exclude: [
      'heavy-optional-dependency'
    ]
  },
  
  // Development optimizations
  server: {
    hmr: {
      overlay: false
    }
  }
})
```

### 3. Component Virtualization

#### 3.1 Virtual List Implementation
```vue
<!-- VirtualList.vue -->
<template>
  <div 
    class="virtual-list" 
    ref="containerRef"
    @scroll="handleScroll"
    :style="{ height: containerHeight + 'px' }"
  >
    <!-- Phantom element for total height -->
    <div 
      class="virtual-list-phantom" 
      :style="{ height: totalHeight + 'px' }"
    ></div>
    
    <!-- Visible items container -->
    <div 
      class="virtual-list-content"
      :style="{ transform: `translateY(${offsetY}px)` }"
    >
      <div
        v-for="item in visibleItems"
        :key="getItemKey(item)"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot :item="item" :index="item.index"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  items: any[]
  itemHeight: number
  containerHeight: number
  overscan?: number
}

const props = withDefaults(defineProps<Props>(), {
  overscan: 5
})

const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)

// Computed properties
const totalHeight = computed(() => props.items.length * props.itemHeight)
const visibleCount = computed(() => Math.ceil(props.containerHeight / props.itemHeight))
const startIndex = computed(() => Math.floor(scrollTop.value / props.itemHeight))
const endIndex = computed(() => Math.min(
  startIndex.value + visibleCount.value + props.overscan,
  props.items.length
))

const visibleItems = computed(() => {
  return props.items
    .slice(startIndex.value, endIndex.value)
    .map((item, index) => ({
      ...item,
      index: startIndex.value + index
    }))
})

const offsetY = computed(() => startIndex.value * props.itemHeight)

// Event handlers
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
}

const getItemKey = (item: any) => {
  return item.id || item.index
}
</script>
```

#### 3.2 Feed Component Optimization
```vue
<!-- OptimizedFeedContainer.vue -->
<template>
  <div class="feed-container">
    <VirtualList
      :items="posts"
      :item-height="200"
      :container-height="800"
      :overscan="3"
    >
      <template #default="{ item }">
        <PostCard 
          :post="item" 
          :lazy-load-images="true"
          @visible="onPostVisible"
        />
      </template>
    </VirtualList>
  </div>
</template>

<script setup lang="ts">
import { useIntersectionObserver } from '@vueuse/core'

// Lazy loading for images
const onPostVisible = (post: Post) => {
  // Load images when post becomes visible
  if (!post.imageLoaded && post.featured_image) {
    loadImage(post.featured_image)
  }
}

// Infinite scroll optimization
const { stop } = useIntersectionObserver(
  sentinelRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting && !loading.value && hasMore.value) {
      loadMorePosts()
    }
  },
  {
    rootMargin: '100px'
  }
)
</script>
```

### 4. Asset Optimization

#### 4.1 Image Optimization
```typescript
// Image optimization service
export class ImageOptimizer {
  private cache = new Map<string, string>()
  
  async optimizeImage(
    url: string, 
    options: ImageOptions = {}
  ): Promise<string> {
    const cacheKey = `${url}-${JSON.stringify(options)}`
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!
    }
    
    const optimizedUrl = await this.processImage(url, options)
    this.cache.set(cacheKey, optimizedUrl)
    
    return optimizedUrl
  }
  
  private async processImage(url: string, options: ImageOptions): Promise<string> {
    // For Supabase Storage, add transformation parameters
    if (url.includes('supabase')) {
      const params = new URLSearchParams()
      
      if (options.width) params.set('width', options.width.toString())
      if (options.height) params.set('height', options.height.toString())
      if (options.quality) params.set('quality', options.quality.toString())
      if (options.format) params.set('format', options.format)
      
      return `${url}?${params.toString()}`
    }
    
    return url
  }
}
```

#### 4.2 Lazy Loading Implementation
```vue
<!-- LazyImage.vue -->
<template>
  <div class="lazy-image-container" ref="containerRef">
    <img
      v-if="isLoaded"
      :src="optimizedSrc"
      :alt="alt"
      class="lazy-image"
      @load="onImageLoad"
      @error="onImageError"
    />
    <div v-else-if="isLoading" class="lazy-image-placeholder">
      <q-skeleton type="rect" :width="width" :height="height" />
    </div>
    <div v-else-if="hasError" class="lazy-image-error">
      <q-icon name="broken_image" size="2em" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useIntersectionObserver } from '@vueuse/core'

interface Props {
  src: string
  alt: string
  width?: string
  height?: string
  quality?: number
}

const props = defineProps<Props>()

const containerRef = ref<HTMLElement>()
const isVisible = ref(false)
const isLoading = ref(false)
const isLoaded = ref(false)
const hasError = ref(false)

const imageOptimizer = new ImageOptimizer()

const optimizedSrc = computed(() => {
  if (!props.src) return ''
  
  return imageOptimizer.optimizeImage(props.src, {
    width: parseInt(props.width || '400'),
    height: parseInt(props.height || '300'),
    quality: props.quality || 80,
    format: 'webp'
  })
})

// Intersection observer for lazy loading
useIntersectionObserver(
  containerRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting && !isVisible.value) {
      isVisible.value = true
      loadImage()
    }
  },
  {
    rootMargin: '50px'
  }
)

const loadImage = async () => {
  if (isLoading.value || isLoaded.value) return
  
  isLoading.value = true
  
  try {
    const img = new Image()
    img.src = await optimizedSrc.value
    
    await new Promise((resolve, reject) => {
      img.onload = resolve
      img.onerror = reject
    })
    
    isLoaded.value = true
  } catch (error) {
    hasError.value = true
  } finally {
    isLoading.value = false
  }
}

const onImageLoad = () => {
  isLoaded.value = true
}

const onImageError = () => {
  hasError.value = true
}
</script>
```

## Implementation Checklist

### Week 1: Code Splitting and Bundle Optimization
- [ ] Implement advanced route-based code splitting
- [ ] Add component-level lazy loading
- [ ] Optimize icon library loading
- [ ] Configure Quasar tree-shaking
- [ ] Enhance Vite build configuration

### Week 2: Component Virtualization and Asset Optimization
- [ ] Implement virtual list component
- [ ] Optimize feed component rendering
- [ ] Add image optimization service
- [ ] Implement lazy loading for images
- [ ] Add performance monitoring

## Success Metrics

### Performance Targets
- Initial bundle size: < 600KB (from 1.2MB)
- First Contentful Paint: < 1.5s
- Largest Contentful Paint: < 2.5s
- Time to Interactive: < 3s
- Cumulative Layout Shift: < 0.1

### Bundle Analysis
```bash
# Generate bundle analysis
npm run build -- --analyze

# Expected results:
# ├── vue-vendor.js: ~120KB
# ├── ui-vendor.js: ~200KB  
# ├── supabase-vendor.js: ~80KB
# ├── app.js: ~150KB
# └── Other chunks: ~50KB
# Total: ~600KB (50% reduction)
```

## Testing Strategy

### Performance Testing
```typescript
describe('Frontend Performance', () => {
  it('should load initial bundle under 600KB', async () => {
    const bundleSize = await getBundleSize()
    expect(bundleSize).toBeLessThan(600 * 1024) // 600KB
  })
  
  it('should render 1000 items without performance issues', async () => {
    const start = performance.now()
    await renderLargeList(1000)
    const duration = performance.now() - start
    
    expect(duration).toBeLessThan(100) // 100ms
  })
})
```

---

*This implementation guide provides detailed steps for Phase 2 frontend optimization with clear performance targets and testing strategies.*
