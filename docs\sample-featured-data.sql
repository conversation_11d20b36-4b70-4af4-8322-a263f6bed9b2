-- Sample Featured Data for Testing
-- This file contains sample posts that follow the exact database schema

-- Featured Events (5 events)
INSERT INTO posts (
  post_type, sub_type, title, content, tags, status, 
  event_date, event_location, event_type, event_theme,
  featured_image, visibility
) VALUES 
(
  'platform', 'event', 'Innovation Summit 2024',
  'Join us for the biggest innovation summit of the year featuring keynote speakers, workshops, and networking opportunities.',
  ARRAY['featured', 'event', 'innovation', 'summit'],
  'published',
  '2024-02-15 09:00:00+00',
  'Convention Center, Downtown',
  'conference',
  'innovation',
  'https://picsum.photos/800/600?random=1',
  'public'
),
(
  'platform', 'event', 'FinTech Meetup',
  'Explore the latest trends in financial technology with industry experts and startup founders.',
  ARRAY['featured', 'event', 'fintech', 'meetup'],
  'published',
  '2024-02-28 18:00:00+00',
  'Tech Hub, Silicon Valley',
  'meetup',
  'fintech',
  'https://picsum.photos/800/600?random=2',
  'public'
),
(
  'platform', 'event', 'AgriTech Workshop',
  'Hands-on workshop on agricultural technology solutions for sustainable farming.',
  ARRAY['featured', 'event', 'agritech', 'workshop'],
  'published',
  '2024-03-10 10:00:00+00',
  'Agricultural Research Center',
  'workshop',
  'agriculture',
  'https://picsum.photos/800/600?random=3',
  'public'
),
(
  'platform', 'event', 'Women in Tech Conference',
  'Empowering women in technology through inspiring talks, mentorship, and networking.',
  ARRAY['featured', 'event', 'women-in-tech', 'conference'],
  'published',
  '2024-03-22 09:00:00+00',
  'University Campus, Main Hall',
  'conference',
  'diversity',
  'https://picsum.photos/800/600?random=4',
  'public'
),
(
  'platform', 'event', 'Startup Pitch Competition',
  'Present your startup idea to investors and win funding opportunities.',
  ARRAY['featured', 'event', 'startup', 'pitch', 'competition'],
  'published',
  '2024-04-10 14:00:00+00',
  'Innovation Center, Auditorium',
  'competition',
  'entrepreneurship',
  'https://picsum.photos/800/600?random=5',
  'public'
);

-- Featured Marketplace Items (5 items)
INSERT INTO posts (
  post_type, sub_type, title, content, tags, status,
  category, location, featured_image, visibility
) VALUES 
(
  'platform', 'marketplace', 'AI-Powered Agriculture System',
  '{"description": "Revolutionary AI system for precision farming with IoT sensors and machine learning analytics.", "marketplaceDetails": {"listingType": "product", "price": "$15,000", "priceType": "fixed", "condition": "new", "duration": "permanent", "contactInfo": "<EMAIL>", "location": "California, USA"}}',
  ARRAY['featured', 'marketplace', 'ai', 'agriculture', 'iot'],
  'published',
  'Technology',
  'California, USA',
  'https://picsum.photos/800/600?random=6',
  'public'
),
(
  'platform', 'marketplace', 'Mobile Payment Gateway',
  '{"description": "Secure and scalable mobile payment solution for emerging markets with blockchain integration.", "marketplaceDetails": {"listingType": "service", "price": "$5,000", "priceType": "monthly", "condition": "new", "duration": "ongoing", "contactInfo": "<EMAIL>", "location": "Remote"}}',
  ARRAY['featured', 'marketplace', 'fintech', 'payment', 'blockchain'],
  'published',
  'Financial Services',
  'Remote',
  'https://picsum.photos/800/600?random=7',
  'public'
),
(
  'platform', 'marketplace', 'E-Learning Platform',
  '{"description": "Comprehensive online learning platform with AI-powered personalized learning paths.", "marketplaceDetails": {"listingType": "product", "price": "$25,000", "priceType": "fixed", "condition": "new", "duration": "permanent", "contactInfo": "<EMAIL>", "location": "New York, USA"}}',
  ARRAY['featured', 'marketplace', 'education', 'ai', 'platform'],
  'published',
  'Education',
  'New York, USA',
  'https://picsum.photos/800/600?random=8',
  'public'
),
(
  'platform', 'marketplace', 'Solar Energy Management System',
  '{"description": "Smart solar energy management system with real-time monitoring and optimization.", "marketplaceDetails": {"listingType": "product", "price": "$8,500", "priceType": "fixed", "condition": "new", "duration": "permanent", "contactInfo": "<EMAIL>", "location": "Texas, USA"}}',
  ARRAY['featured', 'marketplace', 'renewable-energy', 'solar', 'iot'],
  'published',
  'Clean Energy',
  'Texas, USA',
  'https://picsum.photos/800/600?random=9',
  'public'
),
(
  'platform', 'marketplace', 'Healthcare Analytics Software',
  '{"description": "Advanced healthcare analytics software for hospitals and clinics with HIPAA compliance.", "marketplaceDetails": {"listingType": "software", "price": "$12,000", "priceType": "annual", "condition": "new", "duration": "subscription", "contactInfo": "<EMAIL>", "location": "Boston, USA"}}',
  ARRAY['featured', 'marketplace', 'healthcare', 'analytics', 'software'],
  'published',
  'Healthcare',
  'Boston, USA',
  'https://picsum.photos/800/600?random=10',
  'public'
);

-- Featured Blog Posts (3 posts)
INSERT INTO posts (
  post_type, sub_type, title, content, tags, status,
  blog_title, blog_category, excerpt, featured_image, visibility, slug
) VALUES 
(
  'platform', 'blog', 'The Future of AI in Agriculture',
  'Artificial Intelligence is revolutionizing agriculture through precision farming, crop monitoring, and yield optimization. This comprehensive guide explores the latest AI technologies transforming how we grow food.',
  ARRAY['featured', 'blog', 'ai', 'agriculture', 'technology'],
  'published',
  'The Future of AI in Agriculture',
  'Technology',
  'Discover how AI is transforming agriculture with precision farming and smart crop monitoring.',
  'https://picsum.photos/800/600?random=11',
  'public',
  'future-of-ai-in-agriculture'
),
(
  'platform', 'blog', 'Sustainable Innovation in Clean Energy',
  'Exploring breakthrough innovations in renewable energy technologies and their impact on global sustainability goals.',
  ARRAY['featured', 'blog', 'clean-energy', 'sustainability', 'innovation'],
  'published',
  'Sustainable Innovation in Clean Energy',
  'Sustainability',
  'Learn about the latest breakthroughs in renewable energy and sustainable technology.',
  'https://picsum.photos/800/600?random=12',
  'public',
  'sustainable-innovation-clean-energy'
),
(
  'platform', 'blog', 'Building Inclusive Tech Communities',
  'How to create diverse and inclusive technology communities that foster innovation and equal opportunities.',
  ARRAY['featured', 'blog', 'diversity', 'inclusion', 'community'],
  'published',
  'Building Inclusive Tech Communities',
  'Community',
  'Strategies for building diverse and inclusive technology communities.',
  'https://picsum.photos/800/600?random=13',
  'public',
  'building-inclusive-tech-communities'
);

-- Note: Featured profiles will be handled by updating existing profiles with featured tags
-- This should be done through the profile management interface to maintain data integrity
