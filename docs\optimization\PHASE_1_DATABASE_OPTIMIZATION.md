# Phase 1: Database Optimization Implementation Guide

## Overview
This phase focuses on optimizing database queries, implementing intelligent caching, and improving Supabase operations to achieve 60% reduction in query times and 85%+ cache hit rate.

## Current State Analysis

### Identified Performance Issues
1. **Inefficient Queries**: Multiple `SELECT *` queries without field selection
2. **Missing Joins**: Separate queries for related data causing N+1 problems
3. **No Query Caching**: Repeated identical queries without caching
4. **Suboptimal RLS Policies**: Complex policies causing performance overhead
5. **Missing Database Indexes**: Slow queries on frequently filtered fields

### Performance Baseline Metrics
- Average query time: 1-2 seconds
- Cache hit rate: 0% (no caching implemented)
- API response time: 2-3 seconds
- Database connection overhead: 200-500ms per query

## Implementation Strategy

### 1. Database Query Optimization

#### 1.1 Query Analysis and Optimization
```typescript
// BEFORE: Inefficient pattern
const posts = await supabase.from('posts').select('*')
const userProfiles = await Promise.all(
  posts.map(post => 
    supabase.from('profiles').select('*').eq('user_id', post.user_id).single()
  )
)

// AFTER: Optimized with joins and selective fields
const postsWithProfiles = await supabase
  .from('posts')
  .select(`
    id,
    title,
    content,
    created_at,
    post_type,
    sub_type,
    featured_image,
    profiles:user_id (
      id,
      name,
      avatar_url,
      profile_type
    )
  `)
  .order('created_at', { ascending: false })
  .limit(20)
```

#### 1.2 Implement Selective Field Queries
```typescript
// Create field selection constants
export const POST_FIELDS = {
  BASIC: 'id, title, content, created_at, post_type',
  WITH_AUTHOR: `
    id, title, content, created_at, post_type,
    profiles:user_id (id, name, avatar_url)
  `,
  FULL: `
    id, title, content, created_at, post_type, sub_type,
    featured_image, tags, status,
    profiles:user_id (id, name, avatar_url, profile_type)
  `
}

// Use in services
export class PostsService {
  async getPostsForFeed(limit = 20): Promise<Post[]> {
    const { data, error } = await supabase
      .from('posts')
      .select(POST_FIELDS.WITH_AUTHOR)
      .eq('status', 'published')
      .order('created_at', { ascending: false })
      .limit(limit)
    
    if (error) throw error
    return data || []
  }
}
```

#### 1.3 Database Index Optimization
```sql
-- Add indexes for frequently queried fields
CREATE INDEX CONCURRENTLY idx_posts_created_at ON posts(created_at DESC);
CREATE INDEX CONCURRENTLY idx_posts_user_id ON posts(user_id);
CREATE INDEX CONCURRENTLY idx_posts_type_status ON posts(post_type, status);
CREATE INDEX CONCURRENTLY idx_profiles_user_id ON profiles(user_id);
CREATE INDEX CONCURRENTLY idx_connections_users ON connections(user_id, connected_user_id);

-- Composite indexes for complex queries
CREATE INDEX CONCURRENTLY idx_posts_feed_query 
ON posts(status, created_at DESC, post_type) 
WHERE status = 'published';

-- Text search indexes
CREATE INDEX CONCURRENTLY idx_posts_search 
ON posts USING gin(to_tsvector('english', title || ' ' || content));
```

### 2. Intelligent Caching Implementation

#### 2.1 Enhanced Cache Service
```typescript
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  tags: string[]
}

export class EnhancedCacheService {
  private memoryCache = new Map<string, CacheEntry<any>>()
  private requestCache = new Map<string, Promise<any>>()
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0
  }

  async getOrFetch<T>(
    key: string,
    fetcher: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    // Check cache first
    const cached = this.get<T>(key)
    if (cached) {
      this.stats.hits++
      return cached
    }

    this.stats.misses++

    // Deduplicate concurrent requests
    if (this.requestCache.has(key)) {
      return this.requestCache.get(key)!
    }

    // Fetch and cache
    const promise = fetcher()
    this.requestCache.set(key, promise)

    try {
      const result = await promise
      this.set(key, result, options)
      this.stats.sets++
      return result
    } finally {
      this.requestCache.delete(key)
    }
  }

  private get<T>(key: string): T | null {
    const entry = this.memoryCache.get(key)
    if (!entry) return null

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.memoryCache.delete(key)
      return null
    }

    return entry.data
  }

  private set<T>(key: string, data: T, options: CacheOptions): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: options.ttl || 5 * 60 * 1000, // 5 minutes default
      tags: options.tags || []
    }

    this.memoryCache.set(key, entry)

    // Cleanup old entries if cache is getting large
    if (this.memoryCache.size > 1000) {
      this.cleanup()
    }
  }

  invalidateByTags(tags: string[]): void {
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.tags.some(tag => tags.includes(tag))) {
        this.memoryCache.delete(key)
      }
    }
  }

  getStats() {
    const total = this.stats.hits + this.stats.misses
    return {
      ...this.stats,
      hitRate: total > 0 ? this.stats.hits / total : 0,
      size: this.memoryCache.size
    }
  }

  private cleanup(): void {
    const now = Date.now()
    const entries = Array.from(this.memoryCache.entries())
    
    // Remove expired entries
    entries.forEach(([key, entry]) => {
      if (now - entry.timestamp > entry.ttl) {
        this.memoryCache.delete(key)
      }
    })

    // If still too large, remove oldest entries
    if (this.memoryCache.size > 800) {
      const sortedEntries = entries
        .sort((a, b) => a[1].timestamp - b[1].timestamp)
        .slice(0, this.memoryCache.size - 800)
      
      sortedEntries.forEach(([key]) => {
        this.memoryCache.delete(key)
      })
    }
  }
}
```

#### 2.2 Cache Integration with Stores
```typescript
// Enhanced posts store with caching
export const usePostsStore = defineStore('posts', () => {
  const cache = new EnhancedCacheService()
  const posts = ref<Post[]>([])
  const loading = ref(false)

  const fetchPosts = async (filters: PostFilter = {}) => {
    const cacheKey = `posts:${JSON.stringify(filters)}`
    
    loading.value = true
    try {
      const cachedPosts = await cache.getOrFetch(
        cacheKey,
        () => postsAPI.fetchPosts(filters),
        {
          ttl: 2 * 60 * 1000, // 2 minutes
          tags: ['posts', 'feed']
        }
      )
      
      posts.value = cachedPosts
    } finally {
      loading.value = false
    }
  }

  const invalidatePostsCache = () => {
    cache.invalidateByTags(['posts'])
  }

  return {
    posts: readonly(posts),
    loading: readonly(loading),
    fetchPosts,
    invalidatePostsCache
  }
})
```

### 3. Supabase Operation Optimization

#### 3.1 RLS Policy Optimization
```sql
-- BEFORE: Complex RLS policy
CREATE POLICY "Users can view published posts" ON posts
FOR SELECT USING (
  status = 'published' OR 
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM connections 
    WHERE user_id = auth.uid() 
    AND connected_user_id = posts.user_id
  )
);

-- AFTER: Optimized RLS policy with indexes
CREATE POLICY "Users can view published posts" ON posts
FOR SELECT USING (
  status = 'published' OR 
  user_id = auth.uid()
);

-- Separate policy for connected users
CREATE POLICY "Users can view connected users' posts" ON posts
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM connections 
    WHERE user_id = auth.uid() 
    AND connected_user_id = posts.user_id
    AND status = 'accepted'
  )
);
```

#### 3.2 Connection Pooling and Batching
```typescript
export class SupabaseOptimizer {
  private connectionPool: SupabaseClient[] = []
  private batchQueue: BatchOperation[] = []
  private batchTimer: NodeJS.Timeout | null = null

  async batchQuery<T>(
    operation: () => Promise<T>,
    batchKey?: string
  ): Promise<T> {
    // For identical operations, return the same promise
    if (batchKey && this.pendingOperations.has(batchKey)) {
      return this.pendingOperations.get(batchKey)!
    }

    const promise = operation()
    
    if (batchKey) {
      this.pendingOperations.set(batchKey, promise)
      promise.finally(() => {
        this.pendingOperations.delete(batchKey)
      })
    }

    return promise
  }

  private pendingOperations = new Map<string, Promise<any>>()
}
```

## Implementation Checklist

### Week 1: Query Analysis and Optimization
- [ ] Audit all database queries in stores and services
- [ ] Implement selective field queries for all major entities
- [ ] Add proper JOIN operations for related data
- [ ] Create database indexes for frequently queried fields
- [ ] Optimize RLS policies for better performance

### Week 2: Caching Implementation
- [ ] Implement enhanced cache service
- [ ] Integrate caching with all major stores
- [ ] Add cache invalidation strategies
- [ ] Implement request deduplication
- [ ] Add cache performance monitoring

## Success Metrics

### Performance Targets
- Database query time: < 500ms (from 1-2s)
- Cache hit rate: > 85%
- API response time: < 200ms (from 2-3s)
- Memory usage: < 50MB for cache

### Monitoring Implementation
```typescript
export class DatabasePerformanceMonitor {
  private queryTimes: number[] = []
  private cacheStats = { hits: 0, misses: 0 }

  recordQueryTime(duration: number): void {
    this.queryTimes.push(duration)
    
    // Alert if query is too slow
    if (duration > 1000) {
      console.warn(`Slow query detected: ${duration}ms`)
    }
  }

  getAverageQueryTime(): number {
    return this.queryTimes.reduce((a, b) => a + b, 0) / this.queryTimes.length
  }

  getCacheHitRate(): number {
    const total = this.cacheStats.hits + this.cacheStats.misses
    return total > 0 ? this.cacheStats.hits / total : 0
  }
}
```

## Testing Strategy

### Performance Testing
```typescript
describe('Database Optimization', () => {
  it('should fetch posts in under 500ms', async () => {
    const start = performance.now()
    await postsStore.fetchPosts()
    const duration = performance.now() - start
    
    expect(duration).toBeLessThan(500)
  })

  it('should achieve 85% cache hit rate', async () => {
    // Warm up cache
    await postsStore.fetchPosts()
    await postsStore.fetchPosts()
    
    const stats = cache.getStats()
    expect(stats.hitRate).toBeGreaterThan(0.85)
  })
})
```

## Rollback Plan

### Rollback Triggers
- Query performance degrades by >20%
- Cache hit rate drops below 70%
- Memory usage exceeds 100MB
- Any functionality breaks

### Rollback Steps
1. Disable caching layer
2. Revert to original query patterns
3. Remove new database indexes if causing issues
4. Restore original RLS policies
5. Monitor for 24 hours to ensure stability

---

*This implementation guide provides detailed steps for Phase 1 database optimization with clear success metrics and rollback procedures.*
