<template>
  <q-card
    class="featured-marketplace-card cursor-pointer"
    :class="{ 'featured-marketplace-card--hover': !loading }"
    @click="handleClick"
  >
    <!-- Product Showcase Header -->
    <div class="product-header">
      <div class="product-badge">
        <q-icon name="storefront" size="sm" />
        <span>Featured</span>
      </div>
      <div v-if="itemRating" class="product-rating">
        <q-icon name="star" size="sm" color="amber" />
        <span>{{ itemRating }}</span>
      </div>
    </div>

    <!-- Image Container with Product Gallery Style -->
    <div class="product-image-container">
      <q-img
        :src="itemImage"
        :ratio="1"
        class="product-image"
        loading="lazy"
        @error="handleImageError"
      >
        <template v-slot:error>
          <div class="absolute-full flex flex-center bg-gradient-to-br from-green-4 to-green-6">
            <q-icon name="storefront" size="3em" color="white" />
          </div>
        </template>

        <!-- Quick Action Buttons -->
        <div class="product-actions absolute-top-right q-ma-sm">
          <q-btn
            round
            flat
            color="white"
            icon="favorite_border"
            size="sm"
            class="action-btn"
            @click.stop
          />
          <q-btn
            round
            flat
            color="white"
            icon="share"
            size="sm"
            class="action-btn q-ml-xs"
            @click.stop
          />
        </div>

        <!-- Price Overlay -->
        <div class="price-overlay absolute-bottom-left q-ma-sm">
          <div class="price-tag">
            {{ itemPrice }}
          </div>
        </div>

        <!-- Hover Effect Overlay -->
        <div class="hover-overlay absolute-full flex flex-center">
          <q-btn
            color="green"
            text-color="white"
            icon="visibility"
            label="View Details"
            class="hover-button"
          />
        </div>
      </q-img>
    </div>

    <!-- Product Information -->
    <div class="product-info q-pa-md">
      <!-- Category Tag -->
      <div class="category-tag q-mb-xs">
        <q-chip
          color="green-1"
          text-color="green-8"
          size="sm"
          dense
          icon="category"
        >
          {{ itemCategory }}
        </q-chip>
      </div>

      <!-- Title -->
      <h4 class="product-title q-mb-xs">
        {{ itemTitle }}
      </h4>

      <!-- Description -->
      <p class="product-description text-grey-7 q-mb-sm">
        {{ itemDescription }}
      </p>

      <!-- Product Features -->
      <div class="product-features q-mb-sm">
        <div class="feature-item">
          <q-icon name="verified" size="xs" color="green" class="q-mr-xs" />
          <span class="text-caption">Verified Seller</span>
        </div>
        <div class="feature-item">
          <q-icon name="local_shipping" size="xs" color="green" class="q-mr-xs" />
          <span class="text-caption">Fast Delivery</span>
        </div>
      </div>

      <!-- Action Button -->
      <q-btn
        color="green"
        outline
        size="sm"
        label="Contact Seller"
        icon="message"
        class="full-width"
        @click.stop="handleClick"
      />
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

// Props
interface Props {
  item: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  click: [item: any];
}>();

// State
const loading = ref(false);
const imageError = ref(false);

// Computed
const itemImage = computed(() => {
  if (imageError.value) {
    return generateFallbackImage();
  }

  return props.item.featured_image || 
         props.item.media_urls?.primary || 
         props.item.images?.[0] ||
         props.item.image || 
         generateFallbackImage();
});

const itemTitle = computed(() => {
  return props.item.title || 
         props.item.name || 
         'Featured Solution';
});

const itemDescription = computed(() => {
  return props.item.content?.substring(0, 100) + '...' || 
         props.item.description?.substring(0, 100) + '...' || 
         'Innovative solution for your business needs';
});

const itemPrice = computed(() => {
  // Extract price from content or use a default pattern
  const content = props.item.content || '';
  const priceMatch = content.match(/\$[\d,]+/);
  if (priceMatch) {
    return priceMatch[0];
  }
  
  return props.item.price || 
         'Contact for pricing';
});

const itemCategory = computed(() => {
  // Extract category from tags or use default
  const tags = props.item.tags || [];
  const categoryTags = tags.filter((tag: string) => 
    !['featured'].includes(tag.toLowerCase())
  );
  
  return categoryTags[0] || 
         props.item.category || 
         'Technology';
});

const itemRating = computed(() => {
  return props.item.rating || props.item.average_rating || null;
});

// Methods
function generateFallbackImage(): string {
  const itemId = props.item.id || 'default';
  const hash = Math.abs(hashCode(itemId));
  const color = ['4CAF50', '2E7D32', '66BB6A', '388E3C'][hash % 4];
  return `https://via.placeholder.com/400x300/${color}/FFFFFF?text=${encodeURIComponent('Product')}`;
}

function hashCode(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash;
}

function handleImageError() {
  imageError.value = true;
}

function handleClick() {
  emit('click', props.item);
}
</script>

<style scoped>
.featured-marketplace-card {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1);
  height: 420px;
  display: flex;
  flex-direction: column;
  background: white;
  border: 1px solid rgba(76, 175, 80, 0.1);
}

.featured-marketplace-card--hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(76, 175, 80, 0.2);
}

/* Product Header */
.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
  border-bottom: 1px solid rgba(76, 175, 80, 0.1);
}

.product-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #4CAF50;
  font-size: 0.75rem;
  font-weight: 600;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 2px;
  color: #FF8F00;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Product Image */
.product-image-container {
  position: relative;
  flex: 0 0 200px;
  background: #f5f5f5;
}

.product-image {
  height: 100%;
  border-radius: 0;
}

.product-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.action-btn {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  width: 32px;
  height: 32px;
}

.action-btn:hover {
  background: rgba(0, 0, 0, 0.8);
}

.price-overlay {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  padding: 4px 8px;
}

.price-tag {
  color: white;
  font-weight: 700;
  font-size: 1rem;
}

/* Product Information */
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.category-tag {
  margin-bottom: 4px;
}

.product-title {
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
  color: #1a1a1a;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-description {
  font-size: 0.875rem;
  line-height: 1.3;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.product-features {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: #666;
}

.hover-overlay {
  background: rgba(76, 175, 80, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(2px);
}

.featured-marketplace-card--hover:hover .hover-overlay {
  opacity: 1;
}

.hover-button {
  transform: scale(0.9);
  transition: transform 0.3s ease;
  border-radius: 8px;
}

.featured-marketplace-card--hover:hover .hover-button {
  transform: scale(1);
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .featured-marketplace-card {
    height: 380px;
  }

  .product-image-container {
    flex: 0 0 180px;
  }

  .product-title {
    font-size: 1rem;
  }

  .product-description {
    font-size: 0.8rem;
  }

  .product-header {
    padding: 10px 12px;
  }
}
</style>
