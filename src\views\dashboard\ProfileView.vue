<template>
  <q-page class="q-pa-md">
    <!-- Full-page loading overlay -->
    <div v-if="initialLoading" class="full-page-loader">
      <q-spinner-dots color="primary" size="80px" />
      <div class="q-mt-md text-subtitle1">Loading profile data...</div>
    </div>

    <div v-else-if="error" class="q-pa-md flex flex-center column">
      <q-card class="q-pa-lg text-center" style="max-width: 500px">
        <q-card-section>
          <unified-icon name="error" color="negative" size="4em" />
          <div class="text-h5 q-mt-md">{{ error }}</div>
          <div class="text-subtitle1 q-mt-sm">
            The profile you're looking for could not be found. It may have been deleted or you may not have permission to view it.
          </div>
        </q-card-section>
        <q-card-actions align="center" class="q-gutter-md">
          <q-btn
            v-if="isCurrentUser"
            color="primary"
            label="Create Profile"
            :to="{ name: 'profile-create' }"
            class="q-mt-md"
            icon-right="person_add"
          />
          <q-btn
            color="light-green-8"
            label="Back to Dashboard"
            to="/dashboard"
            class="q-mt-md"
            icon-right="arrow_back"
          />
        </q-card-actions>
      </q-card>
    </div>

    <!-- Main content - only show when not in initial loading state -->
    <div v-if="!initialLoading && !error">
      <div v-if="editMode">
        <!-- Edit Mode -->
        <profile-edit-view
          :profile-id="profileId"
          @saved="editMode = false"
          @cancel="editMode = false"
        />
      </div>
      <div v-else>
        <!-- AI Triggers for Profile Page -->
        <div class="q-mb-md">
          <smart-ai-triggers
            page="profile"
            mode="card"
            :context="{
              isCurrentUser,
              profileType: profileStore.currentProfile?.profile_type,
              profileCompletion: profileStore.currentProfile?.profile_completion
            }"
            :max-triggers="4"
          />
        </div>

        <!-- View Mode using Unified Profile View -->
        <unified-profile-view
          :profile-id="profileId"
          context="dashboard"
          :is-current-user="isCurrentUser"
          :show-interactions="false"
          :show-activity-feed="false"
          :show-debug-info="false"
        />
      </div>
    </div>
  </q-page>

  <!-- Profile creation modal -->
  <!-- <profile-creation-modal
    v-model="showProfileCreationModal"
    @profile-created="handleProfileCreated"
  /> -->
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useProfileStore } from '../../stores/profile'
import { useNotificationStore } from '../../stores/notifications'
import { useScrollReset } from '../../composables/useScrollReset'
import { useProfileViewService } from '../../services/profileViewService'
import UnifiedProfileView from '../../components/profile/UnifiedProfileView.vue'
import ProfileEditView from '../../components/profile/ProfileEditView.vue'
import UnifiedIcon from '../../components/ui/UnifiedIcon.vue'
import ProfileCreationModal from '../../components/profile/ProfileCreationModal.vue'
import SmartAITriggers from '../../components/ai/SmartAITriggers.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const profileStore = useProfileStore()
const notifications = useNotificationStore()

// Use the profile view service
const { loadProfileData } = useProfileViewService()

// Use the scroll reset composable
const { resetScroll } = useScrollReset()

// State
const initialLoading = ref(true)
const error = ref<string | null>(null)
const profileId = ref<string>('')
const showProfileCreationModal = ref(false)
const editMode = ref(false)

// Computed properties
const isCurrentUser = computed(() => {
  return authStore.currentUser?.id === profileId.value
})

// Lifecycle
onMounted(async () => {
  if (!authStore.isAuthenticated) {
    router.push('/sign-in')
    return
  }

  initialLoading.value = true

  try {
    // Get the profile ID from the route or use the current profile
    const routeProfileId = route.params.id as string
    const currentProfileId = profileStore.currentProfile?.user_id

    if (routeProfileId) {
      profileId.value = routeProfileId
    } else if (currentProfileId) {
      profileId.value = currentProfileId
    } else {
      throw new Error('No profile selected')
    }

    // Load the profile data using the profile view service
    await loadProfileData(profileId.value)
  } catch (err: any) {
    console.error('ProfileView: Error loading profile:', err)
    error.value = err.message || 'Error loading profile'
    notifications.error(error.value)
  } finally {
    initialLoading.value = false
  }
})

// Handle profile creation events
function handleProfileCreated(profile: any) {
  // Force reload profiles to update the UI
  profileStore.loadUserProfiles()
  // Redirect to the profile edit page
  router.push({
    name: 'profile-edit',
    params: { id: profile.id }
  })
}
</script>

<style scoped>
.full-page-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}
</style>
