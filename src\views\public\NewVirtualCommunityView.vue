<template>
  <q-page class="glass-community-page">
    <!-- Glass Topbar -->
    <header class="topbar e1">
      <div class="inner">
        <div class="brand">
          <q-btn flat round dense icon="o_menu" aria-label="Open menu" @click="toggleLeft" />
          <q-btn flat dense class="brand-btn" @click="goHome" aria-label="Home">Logo</q-btn>
          <q-btn flat dense :to="{ path: '/virtual-community', query: withFlag({ tab: 'feed' }) }" class="community-btn">
            <q-icon name="o_groups" class="q-mr-xs" /> Community
            <span class="pulse" aria-hidden> </span>
          </q-btn>
        </div>
        <div class="search" role="search">
          <input class="input" :placeholder="promptPlaceholder" v-model="aiPrompt" @keyup.enter="triggerAI(aiPrompt)" />
          <div class="chips" aria-label="Quick replies">
            <button class="chip" @click="triggerAI('latest featured')">Latest featured</button>
            <button class="chip" @click="triggerAI('events this month')">Events this month</button>
            <button class="chip" @click="triggerAI('mentors')">Mentors</button>
          </div>
        </div>
        <div class="actions">
          <q-btn color="primary" icon="o_add" label="Create" @click="openCreate" />
          <q-btn flat round dense icon="o_notifications" aria-label="Notifications" />
          <q-btn flat round dense icon="o_person" aria-label="Profile" />
        </div>
      </div>
    </header>

    <!-- Body: three panes -->
    <div class="main">
      <!-- Left nav -->
      <aside class="left">
        <nav class="nav e1">
          <RouterLink :to="{ path: '/virtual-community', query: withFlag({ tab: 'feed' }) }" :aria-current="activeTab==='feed' ? 'page' : undefined">
            <q-icon name="o_home" class="q-mr-sm" /> Feed
          </RouterLink>
          <RouterLink :to="{ path: '/virtual-community', query: withFlag({ tab: 'profiles' }) }" :aria-current="activeTab==='profiles' ? 'page' : undefined">
            <q-icon name="o_badge" class="q-mr-sm" /> Profiles
          </RouterLink>
          <RouterLink :to="{ path: '/virtual-community', query: withFlag({ tab: 'events' }) }" :aria-current="activeTab==='events' ? 'page' : undefined">
            <q-icon name="o_event" class="q-mr-sm" /> Events
          </RouterLink>
          <RouterLink :to="{ path: '/virtual-community', query: withFlag({ tab: 'blog' }) }" :aria-current="activeTab==='blog' ? 'page' : undefined">
            <q-icon name="o_menu_book" class="q-mr-sm" /> Blog
          </RouterLink>
          <RouterLink :to="{ path: '/virtual-community', query: withFlag({ tab: 'marketplace' }) }" :aria-current="activeTab==='marketplace' ? 'page' : undefined">
            <q-icon name="o_store" class="q-mr-sm" /> Marketplace
          </RouterLink>
          <div class="soon" aria-disabled="true">
            <q-icon name="o_groups" class="q-mr-sm" /> Groups <small>(Soon)</small>
          </div>
          <q-separator spaced />
          <RouterLink v-if="showMentorship" :to="'/dashboard/mentorship'">
            <q-icon name="o_hub" class="q-mr-sm" /> Mentorship Hub
          </RouterLink>
        </nav>
      </aside>

      <!-- Center content -->
      <section class="center">
        <!-- Featured shelf per tab (Feed always shows multi-type shelf) -->
        <div class="shelf e2 q-mb-md">
          <FeaturedSection v-if="activeTab==='feed'" class="q-mb-none" @item-click="onFeaturedClick" />
          <FeaturedProfilesSection v-else-if="activeTab==='profiles'" class="q-mb-none" @profile-click="noop" />
          <FeaturedEventsSection v-else-if="activeTab==='events'" class="q-mb-none" @event-click="noop" />
          <FeaturedMarketplaceSection v-else-if="activeTab==='marketplace'" class="q-mb-none" @item-click="noop" />
          <div v-else class="q-pa-md text-grey-7">Explore featured content</div>
        </div>

        <!-- Composer / AI prompt area (Feed only for now) -->
        <div v-if="activeTab==='feed'" class="panel e1 q-mb-md">
          <input class="input" placeholder="Share something or ask the community…" v-model="composerText" />
          <div class="chips q-mt-sm">
            <button class="chip" @click="triggerAI('write a post about mentorship')">Post idea</button>
            <button class="chip" @click="triggerAI('find events nearby')">Find events</button>
          </div>
        </div>

        <!-- Tab content -->
        <div class="feed" v-if="activeTab==='feed'">
          <article v-for="post in posts" :key="post.id" class="q-mb-md">
            <PostCard :post="post" @viewDetails="viewPostDetails" @comment="commentOnPost" @share="sharePost" @save="savePost" />
          </article>
          <div v-if="!loading && posts.length===0" class="empty">Nothing here yet.</div>
        </div>

        <div v-else-if="activeTab==='profiles'" class="grid">
          <article v-for="p in profiles" :key="p.id||p.user_id" class="q-mb-md">
            <ProfileCard :profile="p" @view="viewProfile" @message="messageProfile" @connect="connectWithProfile" />
          </article>
          <div v-if="!loading && profiles.length===0" class="empty">There are no profiles here yet.</div>
        </div>

        <div v-else-if="activeTab==='events'" class="grid">
          <article v-for="ev in events" :key="ev.id" class="q-mb-md">
            <EventCard :event="ev" @viewDetails="viewPostDetails" @register="registerForEvent" @share="shareEvent" @save="saveEvent" />
          </article>
          <div v-if="!loading && events.length===0" class="empty">There are no events yet.</div>
        </div>

        <div v-else-if="activeTab==='blog'" class="q-mt-sm">
          <BlogLayout :initial-filters="blogFilters" />
        </div>

        <div v-else-if="activeTab==='marketplace'" class="grid">
          <article v-for="m in marketplace" :key="m.id" class="q-mb-md">
            <MarketplaceCard :listing="m" @view="viewMarketplaceItem" @share="shareMarketplaceItem" @contact="contactSeller" @save="saveMarketplaceItem" />
          </article>
          <div v-if="!loading && marketplace.length===0" class="empty">No products yet.</div>
        </div>
      </section>

      <!-- Right rail -->
      <aside class="right">
        <div class="panel e1">
          <div class="text-subtitle2">Announcements</div>
          <ul class="q-mt-sm" id="announcements"></ul>
        </div>
        <div class="panel e1">
          <div class="text-subtitle2">Upcoming Events</div>
          <ul class="q-mt-sm" id="mini-events"></ul>
          <q-btn flat dense icon="o_refresh" label="Refresh Featured" @click="reloadCurrentTab" />
        </div>
        <div v-if="showMentorship" class="panel e1">
          <div class="text-subtitle2">Mentorship</div>
          <div class="text-caption">Manage your sessions and requests.</div>
        </div>
      </aside>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter, RouterLink } from 'vue-router'
import { useFilterStore } from '../../stores/filterStore'
import { useNotificationStore } from '../../stores/notifications'
import { useContentInteractions } from '../../composables/useContentInteractions'
import { useGlobalServicesStore } from '../../stores/globalServices'
import { useProfileStore } from '../../stores/profile'
import { feedDataService } from '../../services/feedDataService'
import FeaturedSection from '../../components/feed/FeaturedSection.vue'
import FeaturedProfilesSection from '../../components/feed/FeaturedProfilesSection.vue'
import FeaturedEventsSection from '../../components/feed/FeaturedEventsSection.vue'
import FeaturedMarketplaceSection from '../../components/feed/FeaturedMarketplaceSection.vue'
import PostCard from '../../components/feed/cards/PostCard.vue'
import ProfileCard from '../../components/feed/cards/ProfileCard.vue'
import EventCard from '../../components/feed/cards/EventCard.vue'
import MarketplaceCard from '../../components/feed/cards/MarketplaceCard.vue'
import BlogLayout from '../../components/blog/BlogLayout.vue'

const route = useRoute()
const router = useRouter()

const activeTab = computed(() => {
  const t = (route.query.tab as string) || 'feed'
  return ['feed','profiles','events','blog','marketplace'].includes(t) ? t : 'feed'
})

const filterStore = useFilterStore()
const notifications = useNotificationStore()
const contentInteractions = useContentInteractions()
const globalServices = useGlobalServicesStore()
const cache = globalServices.cacheService
const profileStore = useProfileStore()

const showMentorship = computed(() => {
  const type = profileStore.currentProfile?.profile_type
  return type === 'innovator' || type === 'academic_student' || type === 'mentor'
})

const posts = ref<any[]>([])
const profiles = ref<any[]>([])
const events = ref<any[]>([])
const marketplace = ref<any[]>([])
const loading = ref(false)

const blogFilters = computed(() => ({
  searchQuery: filterStore.currentFilters?.searchQuery || '',
  category: filterStore.currentFilters?.category || '',
  tags: filterStore.currentFilters?.blogCategories || []
}))

const aiPrompt = ref('')
const composerText = ref('')
const promptPlaceholder = computed(() => `Ask anything… (AI search)`) 

function withFlag(q: Record<string, any>) {
  return { newUI: '1', ...(route.query || {}), ...q }
}

function toggleLeft() { /* optional drawer hook */ }
function goHome() { router.push('/') }
function openCreate() { notifications.info('Open create dialog from new UI') }
function noop() {}
function onFeaturedClick() {}

async function reloadCurrentTab(){ await loadTabData(activeTab.value,true) }

async function loadTabData(tab: string, force=false){
  const cacheKey = `newui:tab:${tab}`
  if (!force && cache.get<boolean>(cacheKey)) return
  loading.value = true
  try{
    switch(tab){
      case 'feed': {
        const result = await feedDataService.fetchFeedPosts({ page:1, limit:10, cursor:null, scrollPosition:0 }, filterStore.currentFilters)
        posts.value = result.posts
        break
      }
      case 'profiles': {
        const result = await feedDataService.fetchProfiles({ page:1, limit:12, cursor:null, scrollPosition:0 }, filterStore.currentFilters)
        profiles.value = result.profiles || []
        break
      }
      case 'events': {
        const result = await feedDataService.fetchEvents({ page:1, limit:12, cursor:null, scrollPosition:0 }, filterStore.currentFilters)
        events.value = result.events || []
        break
      }
      case 'marketplace': {
        const result = await feedDataService.fetchMarketplace({ page:1, limit:12, cursor:null, scrollPosition:0 }, filterStore.currentFilters)
        marketplace.value = result.marketplace || []
        break
      }
    }
  } finally {
    loading.value = false
    cache.set(cacheKey,true,{ ttl: 30*1000, storage:'memory' })
  }
}

onMounted(async ()=>{
  filterStore.setActiveTab(activeTab.value,false)
  await loadTabData(activeTab.value)
})

watch(() => route.query.tab, async (t)=>{
  filterStore.setActiveTab(activeTab.value,false)
  await loadTabData(activeTab.value,true)
})

// Interaction pass-throughs
function viewPostDetails(id:number){ const post = posts.value.find(p=>p.id===id); contentInteractions.viewContent(id,'post',post) }
function commentOnPost({ postId, comment }:{postId:number, comment:string}){ notifications.info('Comment (new UI)') }
function sharePost(postId:number){ const post = posts.value.find(p=>p.id===postId); contentInteractions.shareContent(postId,'post',post) }
function savePost(postId:number){ const post = posts.value.find(p=>p.id===postId); contentInteractions.toggleSaveContent(postId,'post',post) }

function viewProfile(id:string){ /* route handled in card */ }
async function messageProfile(id:string){ notifications.info('Message via new UI'); }
async function connectWithProfile(id:string){ notifications.info('Connect via new UI'); }

function registerForEvent(){ notifications.info('Register via new UI') }
function shareEvent(eventId:number){ const ev = events.value.find(e=>e.id===eventId); contentInteractions.shareContent(eventId,'event',ev) }
function saveEvent(eventId:number){ const ev = events.value.find(e=>e.id===eventId); contentInteractions.toggleSaveContent(eventId,'event',ev) }

function viewMarketplaceItem(){ }
function shareMarketplaceItem(id:number){ const m = marketplace.value.find(x=>x.id===id); contentInteractions.shareContent(id,'post',m) }
function contactSeller(){ }
function saveMarketplaceItem(id:number){ const m = marketplace.value.find(x=>x.id===id); contentInteractions.toggleSaveContent(id,'post',m) }

async function triggerAI(query:string){
  try{
    const { useAiChatTriggerService } = await import('../../services/aiChatTriggerService')
    const ai = useAiChatTriggerService()
    // Use general ai_search trigger when free text is provided
    if(query && query.trim().length>0){
      await ai.sendMessage(query.trim())
    }else{
      await ai.triggerChat('ai_search', `community-${activeTab.value}`)
    }
  }catch(err){
    console.error('AI trigger error (new UI):', err)
    notifications.error('Failed to trigger AI search')
  }
}
</script>

<style scoped>
/* Glass shell styles inspired by docs/templates/shared.css */
.e1{box-shadow:0 1px 2px rgba(0,0,0,.06),0 1px 8px rgba(0,0,0,.04)}
.e2{box-shadow:0 8px 24px rgba(0,0,0,.14)}
.glass-community-page{background:linear-gradient(180deg,#0f172a,#111827 30%,#0b1020);min-height:100vh}
.topbar{position:sticky;top:0;z-index:50;backdrop-filter:saturate(140%) blur(12px);background:linear-gradient(180deg,rgba(255,255,255,.18),rgba(255,255,255,.06));border-bottom:1px solid rgba(255,255,255,.12)}
.topbar .inner{display:flex;align-items:center;gap:12px;padding:10px 16px}
.brand{display:flex;align-items:center;gap:8px}
.brand-btn{background:transparent}
.community-btn{position:relative}
.community-btn .pulse{width:8px;height:8px;background:#ef4444;border-radius:999px;margin-left:6px;display:inline-block;animation:pulse 1.8s infinite}
@keyframes pulse{0%{transform:scale(.9);opacity:.9}70%{transform:scale(1.4);opacity:.2}100%{transform:scale(.9);opacity:.9}}
.search{flex:1;display:flex;gap:8px;align-items:center}
.input{flex:1;background:rgba(255,255,255,.9);border:1px solid #e5e7eb;border-radius:999px;padding:10px 14px;outline:0}
.chips{display:flex;gap:8px;flex-wrap:wrap}
.chip{border:1px solid #e5e7eb;background:#fff;border-radius:999px;padding:6px 10px;font-size:12px;cursor:pointer}
.main{display:grid;grid-template-columns:260px minmax(0,1fr) 320px;gap:16px;padding:16px}
.nav{background:#fff;border-radius:18px;padding:10px;border:1px solid #e5e7eb}
.nav a{display:flex;align-items:center;gap:10px;padding:10px;border-radius:10px;color:#374151;text-decoration:none}
.nav a[aria-current="page"], .nav a:hover{background:linear-gradient(90deg,rgba(34,197,94,.12),rgba(34,197,94,.0));}
.nav .soon{opacity:.5;cursor:not-allowed;padding:10px}
.left{position:sticky;top:64px;height:calc(100vh - 80px)}
.right{position:sticky;top:64px;height:calc(100vh - 80px);display:flex;flex-direction:column;gap:16px}
.panel{background:#fff;border-radius:18px;border:1px solid #e5e7eb;padding:12px}
.shelf{background:rgba(255,255,255,.7);border:1px solid #e5e7eb;border-radius:24px;padding:10px 8px;backdrop-filter:blur(10px)}
.feed{display:flex;flex-direction:column}
.grid{display:grid;grid-template-columns:repeat(2,minmax(0,1fr));gap:12px}
.empty{border:1px dashed #e5e7eb;border-radius:16px;padding:20px;color:#6b7280;text-align:center;background:#fff}
@media (max-width: 1024px){.main{grid-template-columns:220px 1fr;}.right{display:none}}
</style>

