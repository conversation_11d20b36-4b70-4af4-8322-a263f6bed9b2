/**
 * AI Enhanced Service
 * 
 * Provides enhanced AI functionality with action execution and context awareness
 */

import { getAiChatService } from './aiChatService'

interface EnhancedChatRequest {
  message: string
  conversation_history?: any[]
  user_context?: any
  actions?: any[]
}

interface EnhancedChatResponse {
  success: boolean
  response?: string
  message?: string
  actions?: any[]
  error?: string
}

class AIEnhancedService {
  private aiChatService = getAiChatService()

  /**
   * Send enhanced chat message with action support
   */
  async sendEnhancedChatMessage(request: EnhancedChatRequest): Promise<EnhancedChatResponse> {
    try {
      console.log('🚀 Enhanced AI service: Processing request')
      
      // Use the existing AI chat service
      const response = await this.aiChatService.sendMessage(
        request.message,
        request.conversation_history || [],
        request.user_context?.current_page
      )

      if (response.success && response.message) {
        return {
          success: true,
          response: response.message,
          message: response.message
        }
      } else {
        throw new Error(response.error || 'No response from AI service')
      }
    } catch (error) {
      console.error('❌ Enhanced AI service error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        response: 'I apologize, but I\'m experiencing technical difficulties. Please try again later.'
      }
    }
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(userId?: string): Promise<any[]> {
    try {
      // Return empty array for now - this would typically fetch from database
      return []
    } catch (error) {
      console.error('❌ Error getting conversation history:', error)
      return []
    }
  }

  /**
   * Execute AI action
   */
  async executeAction(action: any): Promise<boolean> {
    try {
      console.log('🎯 Executing AI action:', action)
      
      // Basic action execution - this would be expanded based on requirements
      switch (action.type) {
        case 'navigation':
          if (action.url) {
            window.location.href = action.url
            return true
          }
          break
        case 'dialog':
          // Trigger dialog - would integrate with UI framework
          console.log('Dialog action:', action)
          return true
        default:
          console.warn('Unknown action type:', action.type)
      }
      
      return false
    } catch (error) {
      console.error('❌ Error executing action:', error)
      return false
    }
  }

  /**
   * Test service connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      return await this.aiChatService.testConnection()
    } catch (error) {
      console.error('❌ Enhanced AI service connection test failed:', error)
      return false
    }
  }
}

// Create singleton instance
let _aiEnhancedService: AIEnhancedService | null = null

export const useAiEnhancedService = (): AIEnhancedService => {
  if (!_aiEnhancedService) {
    _aiEnhancedService = new AIEnhancedService()
  }
  return _aiEnhancedService
}

// Default export for compatibility
export default useAiEnhancedService()
