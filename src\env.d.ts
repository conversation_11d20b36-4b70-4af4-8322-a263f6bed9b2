/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'pinia' {
  export * from 'pinia/dist/pinia'
}

declare module 'vue-router' {
  export * from 'vue-router/dist/vue-router'
}

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_SUPABASE_URL: string
  readonly VITE_SUPABASE_ANON_KEY: string
  readonly MODE: string
  readonly DEV: boolean
  readonly BASE_URL: string
  // Service coordination environment variables
  readonly VITE_APP_ENV?: string
  readonly VITE_SERVICE_HEALTH_CHECK_INTERVAL?: string
  readonly VITE_SERVICE_MAX_RETRY_ATTEMPTS?: string
  readonly VITE_SERVICE_RETRY_DELAY?: string
  readonly VITE_SERVICE_ENABLE_DEV_TOOLS?: string
  readonly VITE_SERVICE_ENABLE_HEALTH_MONITORING?: string
  readonly VITE_SERVICE_ENABLE_ERROR_RECOVERY?: string
  // SECURITY: The following API keys are disabled for client-side security
  // They should be moved to server-side/Edge Functions
  readonly VITE_MAILCHIMP_API_KEY?: string // Optional - disabled for security
  readonly VITE_MAILCHIMP_LIST_ID?: string // Optional - disabled for security
  readonly VITE_MAILCHIMP_SERVER_PREFIX?: string // Optional - disabled for security
  readonly VITE_JIRA_HOST?: string // Optional - disabled for security
  readonly VITE_JIRA_API_TOKEN?: string // Optional - disabled for security
  readonly VITE_JIRA_EMAIL?: string // Optional - disabled for security
  readonly VITE_JIRA_PROJECT_KEY?: string // Optional - disabled for security
  readonly VITE_SENDGRID_API_KEY?: string // Optional - disabled for security
  readonly VITE_SENDGRID_FROM_EMAIL?: string // Optional - disabled for security
  readonly VITE_SENDGRID_FROM_NAME?: string // Optional - disabled for security
}

interface ImportMeta {
  readonly env: ImportMetaEnv
} 