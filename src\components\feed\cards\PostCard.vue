<template>
  <q-item class="post-card q-py-md">
    <q-item-section>
      <!-- Post Type and Category Badges -->
      <div class="row items-center q-mb-sm">
        <!-- Main Post Type Badge (Always displayed) -->
        <q-badge
          v-if="mainPostType"
          :color="getPostTypeColor(mainPostType)"
          class="q-mr-sm"
          size="lg"
        >
          {{ mainPostType }}
        </q-badge>

        <!-- Subcategory Badge (Second level) -->
        <q-badge
          v-if="subCategory"
          :color="getSubCategoryColor(subCategory)"
          class="q-mr-sm"
          size="lg"
        >
          {{ subCategory }}
        </q-badge>

        <!-- Category Badge (Third level) -->
        <q-badge
          v-if="post.category && post.category !== mainPostType && post.category !== subCategory"
          :color="getCategoryColor(post.category)"
          class="q-mr-sm"
          size="lg"
        >
          {{ post.category }}
        </q-badge>

        <!-- Blog Category Badge (if different from other categories) -->
        <q-badge
          v-if="post.blogCategory && !isCategoryDuplicate(post.blogCategory)"
          :color="getCategoryColor(post.blogCategory)"
          class="q-mr-sm"
          size="lg"
        >
          {{ post.blogCategory }}
        </q-badge>
      </div>

      <!-- Post Header -->
      <div class="row items-center q-mb-sm">
        <!-- User Avatar (clickable for user posts only) -->
        <user-avatar
          :name="post.author"
          :email="post.authorEmail"
          :avatar-url="post.avatar"
          :user-id="post.authorId"
          size="40px"
          :clickable="isUserPost"
          @click="isUserPost && navigateToUserProfile"
        />

        <!-- Author Info (clickable for user posts only) -->
        <div
          class="q-ml-sm user-info"
          :class="{ 'cursor-pointer': isUserPost }"
          @click="isUserPost && navigateToUserProfile"
        >
          <div class="text-weight-bold">
            {{ post.author }}
            <q-badge
              v-if="!isUserPost && post.post_type === 'admin'"
              color="red"
              class="q-ml-xs"
            >
              Admin
            </q-badge>
            <q-badge
              v-else-if="!isUserPost && post.post_type === 'automated'"
              color="grey"
              class="q-ml-xs"
            >
              System
            </q-badge>
          </div>
          <div class="text-caption">{{ post.date }}</div>
        </div>
      </div>

      <!-- Post Title (if available) -->
      <div v-if="post.title" class="text-h6 q-mb-sm">{{ post.title }}</div>

      <!-- Post Content -->
      <div class="q-mb-sm">{{ truncatedContent }}</div>

      <!-- Tags (if available) -->
      <div v-if="postTags.length > 0" class="q-mb-sm">
        <div class="text-caption text-grey q-mb-xs">Tags:</div>
        <q-chip
          v-for="tag in postTags"
          :key="tag"
          size="sm"
          outline
          :color="getTagColor(tag)"
          class="q-mr-xs"
        >
          #{{ tag }}
        </q-chip>
      </div>

      <!-- Post Image (if available) -->
      <div v-if="postImage" class="post-image-container q-mb-md">
        <q-img
          :src="postImage"
          class="post-image"
          style="height: 200px;"
          @error="handleImageError"
          no-spinner
          no-transition
        >
          <template v-slot:error>
            <div class="absolute-full flex flex-center bg-grey-3 text-grey-8">
              <div class="text-center">
                <q-icon name="broken_image" size="3em" />
                <div>Image failed to load</div>
                <q-btn
                  v-if="isImageUrlFixable"
                  flat
                  color="primary"
                  label="Try Fix URL"
                  class="q-mt-sm"
                  @click="tryFixImageUrl"
                />
              </div>
            </div>
          </template>
        </q-img>
      </div>

      <!-- Post Actions -->
      <div class="post-actions-container">
        <q-separator class="q-mb-sm" />
        <interaction-buttons
          :content-id="post.id"
          :content-type="getContentType()"
          :content-data="getContentData()"
          :is-liked="isLiked"
          :is-saved="post.isSaved || false"
          :likes-count="post.likesCount || post.likes_count || post.likes || 0"
          :comments-count="post.commentsCount || post.comments_count || post.comments || 0"
          :show-comments="showComments"
          :show-view-details="true"
          :show-contact="false"
          :dynamic-c-t-a="null"
          size="sm"

          @comment="toggleComments"
          @share="handleShare"
          @save="handleSave"
          @view-details="handleViewDetails"
          @dynamic-c-t-a="handleDynamicCTA"
        />
      </div>

      <!-- Comments Section (toggle) -->
      <div v-if="showComments" class="q-mt-md">
        <q-separator class="q-my-sm" />

        <div v-if="loadingComments" class="text-center q-pa-sm">
          <q-spinner color="primary" size="2em" />
          <p class="q-ma-none">Loading comments...</p>
        </div>

        <div v-else-if="!commentsData.length" class="text-center q-pa-sm">
          <p class="q-ma-none">No comments yet. Be the first to comment!</p>
        </div>

        <div v-else>
          <q-list>
            <q-item v-for="comment in commentsData" :key="comment.id" class="q-py-sm">
              <q-item-section avatar top>
                <user-avatar
                  :name="comment.author"
                  :email="comment.email"
                  :avatar-url="comment.avatar"
                  :user-id="comment.authorId"
                  size="32px"
                  @click="comment.authorId && navigateToUserProfile(comment.authorId)"
                />
              </q-item-section>
              <q-item-section>
                <q-item-label class="text-weight-bold">{{ comment.author }}</q-item-label>
                <q-item-label caption>{{ comment.date }}</q-item-label>
                <div class="q-mt-xs">{{ comment.content }}</div>
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <!-- Comment Form -->
        <div class="q-mt-md">
          <div class="row items-center">
            <user-avatar
              name="Current User"
              size="32px"
              class="q-mr-sm"
              :clickable="false"
            />
            <q-input
              v-model="newComment"
              dense
              outlined
              placeholder="Write a comment..."
              class="col"
              @keyup.enter="!submittingComment && submitComment()"
            >
              <template v-slot:after>
                <q-btn
                  round
                  dense
                  flat
                  color="primary"
                  icon="send"
                  @click="submitComment"
                  :disable="!newComment.trim() || submittingComment"
                  :loading="submittingComment"
                />
              </template>
            </q-input>
          </div>
        </div>
      </div>
    </q-item-section>
  </q-item>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import UserAvatar from '../../common/UserAvatar.vue';
import InteractionButtons from '../../common/InteractionButtons.vue';
import { truncateText, stripHtml } from '../../../utils/textUtils';
import { useContentInteractions } from '../../../composables/useContentInteractions';

const router = useRouter();
const contentInteractions = useContentInteractions();

const props = defineProps({
  post: {
    type: Object,
    required: true
  }
});

const emit = defineEmits([
  'like',
  'comment',
  'share',
  'save',
  'viewDetails',
  'register',
  'apply',
  'join',
  'contact',
  'connect',
  'collaborate',
  'requestMentorship',
  'applyForFunding',
  'readMore'
]);

// Local state
const showComments = ref(false);
const newComment = ref('');
const loadingComments = ref(false);
const fixedImageUrl = ref('');

// Use isLiked from post data instead of local state
const isLiked = computed(() => props.post.isLiked || false);

// Truncate post content for feed view
const truncatedContent = computed(() => {
  let content = props.post.content || '';

  // Check if content is a JSON string and parse it
  if (typeof content === 'string' && (content.startsWith('{') || content.startsWith('['))) {
    try {
      const parsedContent = JSON.parse(content);

      // Extract description based on post subType
      if (parsedContent.description) {
        content = parsedContent.description;
      } else if (parsedContent.eventDetails && props.post.subType?.toLowerCase() === 'event') {
        // For event posts with eventDetails
        content = parsedContent.eventDetails.description || content;
      } else if (parsedContent.marketplaceDetails && props.post.subType?.toLowerCase() === 'marketplace') {
        // For marketplace posts with marketplaceDetails
        content = parsedContent.marketplaceDetails.description || content;
      }
    } catch (e) {
      // If parsing fails, use the original content
      console.log('Failed to parse JSON content:', e);
    }
  }

  // Strip HTML tags if present
  if (/<[a-z][\s\S]*>/i.test(content)) {
    content = stripHtml(content);
  }

  return truncateText(content, 250);
});

// Get the post image from either featuredImage or image property
const postImage = computed(() => {
  // If we have a fixed URL from a previous fix attempt, use that
  if (fixedImageUrl.value) {
    return fixedImageUrl.value;
  }

  const imageUrl = props.post.featuredImage || props.post.image || '';

  // Reduced logging - only log when there are issues
  // if (process.env.NODE_ENV === 'development') {
  //   console.log('Post image URL for post ID:', props.post.id, 'URL:', imageUrl);
  // }

  return imageUrl;
});

// Check if the image URL is potentially fixable
const isImageUrlFixable = computed(() => {
  const url = props.post.featuredImage || props.post.image || '';

  // If it's empty, it's not fixable
  if (!url) return false;

  // If it already has the correct full Supabase URL format, it's not fixable
  if (url.includes('dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/') &&
      !url.includes('imagefiles/imagefiles/')) {
    return false;
  }

  // If it's an external URL (not Supabase), it's not fixable
  if (url.startsWith('http') && !url.includes('supabase') && !url.includes('imagefiles')) {
    return false;
  }

  // If it has the wrong format (includes imagefiles but not in the right place), it's fixable
  if (url.includes('imagefiles/') &&
      (!url.includes('storage/v1/object/public/imagefiles/') ||
       url.includes('imagefiles/imagefiles/'))) {
    return true;
  }

  // Otherwise, we might be able to fix it
  return true;
});

// Try to fix the image URL by adding the Supabase storage prefix
function tryFixImageUrl() {
  const originalUrl = props.post.featuredImage || props.post.image || '';

  // If the URL is empty, there's nothing to fix
  if (!originalUrl) return;

  console.log('Attempting to fix image URL:', originalUrl);

  // If it's a relative path or partial Supabase path
  if (!originalUrl.startsWith('http')) {
    // If it already includes the bucket name
    if (originalUrl.includes('imagefiles/')) {
      // Extract the file path after 'imagefiles/'
      const match = originalUrl.match(/imagefiles\/(.+)$/);
      if (match && match[1]) {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${match[1]}`;
      } else {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/${originalUrl}`;
      }
    } else {
      // Assume it's in the imagefiles bucket
      fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${originalUrl}`;
    }
  } else if (originalUrl.includes('supabase')) {
    if (!originalUrl.includes('/storage/v1/object/public/')) {
      // It's a Supabase URL but missing the storage path
      const parts = originalUrl.split('/');
      const fileName = parts[parts.length - 1];
      fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${fileName}`;
    } else if (originalUrl.includes('imagefiles/') && !originalUrl.includes('/storage/v1/object/public/imagefiles/')) {
      // It has the wrong format for the bucket path
      const match = originalUrl.match(/imagefiles\/(.+)$/);
      if (match && match[1]) {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${match[1]}`;
      }
    }
  }

  console.log('Fixed image URL:', fixedImageUrl.value);
}

// Process post tags to ensure they're displayed correctly
const postTags = computed(() => {
  if (!props.post.tags) return [];

  // If tags is a string (possibly from HTML), convert it to an array
  if (typeof props.post.tags === 'string') {
    // Try to parse as JSON if it looks like a JSON array
    if (props.post.tags.startsWith('[') && props.post.tags.endsWith(']')) {
      try {
        const parsedTags = JSON.parse(props.post.tags);
        return processParsedTags(parsedTags);
      } catch (e) {
        console.log('Failed to parse tags JSON:', e);

        // Check if it contains stringified objects with label/value pairs
        if (props.post.tags.includes('"label"') && props.post.tags.includes('"value"')) {
          // First try to extract values (preferred)
          const valueMatches = [...props.post.tags.matchAll(/"value":"([^"]+)"/g)];
          if (valueMatches.length > 0) {
            return valueMatches.map(match => match[1]);
          }

          // If no values found, try to extract labels
          const labelMatches = [...props.post.tags.matchAll(/"label":"([^"]+)"/g)];
          if (labelMatches.length > 0) {
            return labelMatches.map(match => match[1]);
          }
        }

        // If parsing fails, split by comma as fallback
        return props.post.tags.replace(/[\[\]"']/g, '').split(',').map((tag: string) => tag.trim());
      }
    }
    // If it's not JSON-like, split by comma
    return props.post.tags.split(',').map((tag: string) => tag.trim());
  }

  // If it's already an array, process each item to handle object tags
  return processParsedTags(props.post.tags);
});

// Helper function to process parsed tags
function processParsedTags(tags: any[]) {
  if (!Array.isArray(tags)) return [];

  return tags.map(tag => {
    // Check if the tag is an object with label/value properties (from select components)
    if (typeof tag === 'object' && tag !== null) {
      // Check if it's a JSON string that was incorrectly parsed
      if (typeof tag === 'string' && tag.includes('label') && tag.includes('value')) {
        try {
          const parsedTag = JSON.parse(tag);
          return parsedTag.value || parsedTag.label || 'Unknown Tag';
        } catch (e) {
          // If parsing fails, extract using regex as fallback
          const valueMatch = tag.match(/"value":"([^"]+)"/);
          if (valueMatch && valueMatch[1]) return valueMatch[1];

          const labelMatch = tag.match(/"label":"([^"]+)"/);
          if (labelMatch && labelMatch[1]) return labelMatch[1];
        }
      }

      // Always prioritize the value property for display
      if (tag.value) return tag.value;
      // If no value, but has a label property, use that
      if (tag.label) return tag.label;
      // Otherwise return a generic tag name
      return 'Tag';
    }
    // If it's a string or number, return as is
    return String(tag);
  });
}

// Computed properties for dynamic CTA
const showDynamicCTA = computed(() => {
  return !!getDynamicCTAConfig().label;
});

const dynamicCTA = computed(() => {
  return getDynamicCTAConfig();
});

// Determine if this is a user post (for clickable avatar/name)
const isUserPost = computed(() => {
  // If post_type is missing or empty, check if userId exists (indicating a user post)
  if (!props.post.post_type) {
    return !!props.post.userId;
  }
  // If it's a platform post but has a userId, it's a user-created post
  if (props.post.post_type.toLowerCase() === 'platform' && props.post.userId) {
    return true;
  }
  return false;
});

// Get the main post type for display at the top
const mainPostType = computed(() => {
  // Get the post type from the database
  const postType = props.post.post_type?.toLowerCase() || '';
  const subType = props.post.subType?.toLowerCase() || '';

  // For platform posts (user-created), show the special category based on subType
  if (postType === 'platform' || !postType) {
    if (subType === 'blog') {
      return 'Blog';
    } else if (subType === 'event') {
      return 'Event';
    } else if (subType === 'opportunity' || subType.includes('funding')) {
      return 'Opportunity';
    } else if (subType === 'group') {
      return 'Group';
    } else if (subType === 'marketplace') {
      return 'Marketplace';
    } else if (subType) {
      // Capitalize the first letter of subType if it exists
      return subType.charAt(0).toUpperCase() + subType.slice(1);
    }

    // Default for platform posts without subType
    return 'General';
  }

  // For admin or automated posts
  if (postType === 'admin') {
    return 'Admin Post';
  } else if (postType === 'automated') {
    return 'System Update';
  }

  // Fallback to capitalize the post_type if nothing else matches
  return postType.charAt(0).toUpperCase() + postType.slice(1);
});

// Get the subcategory for display at the top
const subCategory = computed(() => {
  // For events, check for event type or format
  if (mainPostType.value === 'Event') {
    return props.post.eventType ||
           (props.post.content?.toLowerCase().includes('virtual') ? 'Virtual' :
            props.post.content?.toLowerCase().includes('physical') ? 'Physical' : '');
  }

  // For blog posts, use the blog category
  if (mainPostType.value === 'Blog') {
    return props.post.blogCategory || '';
  }

  // For marketplace posts
  if (mainPostType.value === 'Marketplace') {
    if (props.post.content?.toLowerCase().includes('product')) {
      return 'Product';
    } else if (props.post.content?.toLowerCase().includes('service')) {
      return 'Service';
    }
  }

  // For opportunity posts
  if (mainPostType.value === 'Opportunity') {
    if (props.post.content?.toLowerCase().includes('funding')) {
      return 'Funding';
    } else if (props.post.content?.toLowerCase().includes('mentorship')) {
      return 'Mentorship';
    } else if (props.post.content?.toLowerCase().includes('collaboration')) {
      return 'Collaboration';
    }
  }

  return '';
});

// Check if a category is already displayed to avoid duplicates
function isCategoryDuplicate(category: string): boolean {
  if (!category) return false;

  const lowerCategory = category.toLowerCase();
  const lowerMainType = mainPostType.value.toLowerCase();
  const lowerSubCategory = subCategory.value.toLowerCase();
  const lowerPostCategory = props.post.category?.toLowerCase() || '';

  return lowerCategory === lowerMainType ||
         lowerCategory === lowerSubCategory ||
         lowerCategory === lowerPostCategory;
}

// Get color for the main post type badge
function getPostTypeColor(type: string): string {
  const typeColors: Record<string, string> = {
    'Blog': 'purple',
    'Event': 'orange',
    'Opportunity': 'green',
    'Group': 'blue',
    'Marketplace': 'teal',
    'Admin Post': 'red',
    'System Update': 'grey',
    'General': 'primary'
  };

  return typeColors[type] || 'primary';
}

// Get color for subcategory badge
function getSubCategoryColor(subCat: string): string {
  const subCatColors: Record<string, string> = {
    'Virtual': 'blue',
    'Physical': 'green',
    'Hybrid': 'purple',
    'Workshop': 'teal',
    'Conference': 'deep-orange',
    'Networking': 'indigo',
    'Funding': 'green-9',
    'Mentorship': 'purple-7',
    'Collaboration': 'blue-7',
    'Product': 'blue-5',
    'Service': 'teal-5'
  };

  return subCatColors[subCat] || 'grey-7';
}

// Get color for tag chips
function getTagColor(tag: string): string {
  // Check if tag matches any main post type
  const mainPostTypes = {
    'blog': 'purple',
    'event': 'orange',
    'opportunity': 'green',
    'group': 'blue',
    'marketplace': 'teal',
    'general': 'primary'
  };

  // Check if tag matches any subcategory
  const subCategories = {
    'virtual': 'blue',
    'physical': 'green',
    'hybrid': 'purple',
    'workshop': 'teal',
    'conference': 'deep-orange',
    'networking': 'indigo',
    'funding': 'green-9',
    'mentorship': 'purple-7',
    'collaboration': 'blue-7',
    'product': 'blue-5',
    'service': 'teal-5'
  };

  const lowerTag = tag.toLowerCase();

  // First check if it's a main post type
  if (mainPostTypes[lowerTag]) {
    return mainPostTypes[lowerTag];
  }

  // Then check if it's a subcategory
  if (subCategories[lowerTag]) {
    return subCategories[lowerTag];
  }

  // Default color for regular tags
  return 'primary';
}
// Comments will be loaded from the database when needed
const commentsData = ref([]);
const submittingComment = ref(false);

// Methods
// Note: Like functionality is now handled directly by InteractionButtons component

function handleShare() {
  emit('share', props.post.id);
}

function handleSave() {
  emit('save', props.post.id);
}

function handleViewDetails() {
  console.log('PostCard: handleViewDetails called for post:', props.post.id);
  console.log('PostCard: Post data:', {
    id: props.post.id,
    postType: props.post.postType,
    subType: props.post.subType,
    title: props.post.title
  });

  emit('viewDetails', props.post.id);

  try {
    // Navigate to the appropriate route based on post type
    const postType = props.post.postType?.toLowerCase() || 'general';
    const subType = props.post.subType?.toLowerCase() || '';
    const postId = props.post.id;
    const postSlug = props.post.slug || `${postType}-${postId}`;

    console.log('PostCard: Navigation details:', { postType, subType, postId, postSlug });

    // Determine the appropriate route based on post type
    let routeName = 'post-details';

    // Special handling for blog posts which use slug-based routing
    if (postType === 'blog' || subType === 'blog') {
      routeName = 'virtual-community-article';
      console.log('PostCard: Navigating to blog article:', { routeName, slug: postSlug });
      router.push({ name: routeName, params: { slug: postSlug } });
      return;
    }

    // For all other post types, use the unified post-details route
    // This ensures all posts use our enhanced dynamic SinglePostView
    console.log('PostCard: Navigating to post details:', { routeName, id: postId });
    router.push({
      name: 'post-details',
      params: { id: postId }
    });
  } catch (error) {
    console.error('PostCard: Error in handleViewDetails:', error);
  }
}

function handleDynamicCTA() {
  const postId = props.post.id;
  const category = props.post.category?.toLowerCase() || '';
  const subType = props.post.subType?.toLowerCase() || '';
  const blogCategory = props.post.blogCategory?.toLowerCase() || '';
  const content = props.post.content?.toLowerCase() || '';
  const title = props.post.title?.toLowerCase() || '';

  // Skip event posts - no longer handling register actions

  // Blog posts
  if (subType === 'blog' || category === 'blog' || blogCategory) {
    handleViewDetails();
    return;
  }

  // Mentorship posts - handle connection with mentor
  if (category === 'mentorship' || subType === 'mentorship' ||
      title.includes('mentor') || content.includes('mentor')) {
    emit('connect', postId);
    return;
  }

  // Skip funding and job opportunities - no longer handling apply actions

  // Collaboration opportunities
  if (category === 'collaboration' || subType === 'collaboration' ||
      title.includes('collab') || content.includes('partner')) {
    emit('contact', postId);
    return;
  }

  // Skip training/workshop posts - no longer handling register actions

  // Skip marketplace items - contact is handled by InteractionButtons ContactButton component

  // Group-related posts
  if (subType === 'group' || category === 'group' ||
      title.includes('group') || content.includes('join our group')) {
    emit('join', postId);
    return;
  }

  // For all other post types, just view details
  handleViewDetails();
}

// Helper functions for InteractionButtons component
function getContentType() {
  const subType = props.post.subType?.toLowerCase() || '';

  // Determine content type based on post subType
  if (subType === 'marketplace') {
    return 'listing';
  } else if (subType === 'event') {
    return 'event';
  } else if (subType === 'group') {
    return 'group';
  } else {
    return 'post';
  }
}

function getContentData() {
  return {
    id: props.post.id,
    title: props.post.title,
    content: props.post.content,
    description: props.post.description,
    postType: props.post.postType,
    subType: props.post.subType,
    userId: props.post.userId,
    author: props.post.author,
    isSaved: props.post.isSaved,
    isFavorite: props.post.isFavorite
  };
}

// Helper function to determine the appropriate CTA based on post type and category
function getDynamicCTAConfig() {
  // Get all relevant post properties for determining the CTA
  const category = props.post.category?.toLowerCase() || '';
  const subType = props.post.subType?.toLowerCase() || '';
  const blogCategory = props.post.blogCategory?.toLowerCase() || '';

  // Check content for keywords that might indicate the post's purpose
  const content = props.post.content?.toLowerCase() || '';
  const title = props.post.title?.toLowerCase() || '';

  // Default (empty) config
  const defaultConfig = { label: '', icon: '', color: 'primary', action: '' };

  // Skip event posts - no longer showing register buttons

  // Check for blog posts
  if (subType === 'blog' || category === 'blog' || blogCategory) {
    return {
      label: 'Read More',
      icon: 'menu_book',
      color: 'purple',
      action: 'view'
    };
  }

  // Check for mentorship-related posts
  if (category === 'mentorship' || subType === 'mentorship' ||
      title.includes('mentor') || content.includes('mentor')) {
    return {
      label: 'Connect with Mentor',
      icon: 'school',
      color: 'purple-7',
      action: 'connect'
    };
  }

  // Skip funding and job opportunities - no longer showing apply buttons

  // Check for collaboration opportunities
  if (category === 'collaboration' || subType === 'collaboration' ||
      title.includes('collab') || content.includes('partner')) {
    return {
      label: 'Collaborate',
      icon: 'people',
      color: 'indigo',
      action: 'collaborate'
    };
  }

  // Skip training/workshop posts - no longer showing register buttons

  // Skip marketplace items - contact button is handled by InteractionButtons ContactButton component

  // Check for group-related posts
  if (subType === 'group' || category === 'group' ||
      title.includes('group') || content.includes('join our group')) {
    return {
      label: 'Join Group',
      icon: 'group_add',
      color: 'blue',
      action: 'join'
    };
  }

  // Check for success stories
  if (category === 'success stories' ||
      title.includes('success story') || content.includes('success story')) {
    return {
      label: 'Read Full Story',
      icon: 'auto_stories',
      color: 'orange',
      action: 'view'
    };
  }

  // Check for research papers
  if (category === 'research' || subType === 'research' ||
      title.includes('research') || content.includes('paper')) {
    return {
      label: 'View Research',
      icon: 'science',
      color: 'teal',
      action: 'view'
    };
  }

  // For general posts with no specific type, show a generic "Learn More" CTA
  if (props.post.content && props.post.content.length > 250) {
    return {
      label: 'Learn More',
      icon: 'visibility',
      color: 'primary',
      action: 'view'
    };
  }

  // If no specific CTA is determined, return the default (empty) config
  return defaultConfig;
}

// Toggle comments visibility and load comments from database if needed
async function toggleComments() {
  await contentInteractions.toggleComments(
    props.post.id,
    'post',
    showComments,
    commentsData,
    loadingComments
  );
}



async function submitComment() {
  const success = await contentInteractions.submitComment(
    props.post.id,
    'post',
    newComment.value,
    commentsData,
    newComment,
    submittingComment
  );

  if (success) {
    // Note: Comment count is already updated by the posts store, no need to duplicate here
    emit('comment', {
      postId: props.post.id,
      content: newComment.value
    });
  }
}

// Navigate to user profile page
function navigateToUserProfile(specificAuthorId?: number | string) {
  // Only navigate if this is a user post or a specific author ID is provided
  if (!isUserPost.value && !specificAuthorId) {
    console.log('Not navigating: Post is not from a regular user');
    return;
  }

  // Use the specific author ID if provided, otherwise use the post author ID
  const authorId = specificAuthorId || props.post.authorId;

  if (authorId) {
    console.log('Navigating to user profile:', authorId);
    router.push({
      name: 'user-profile',
      params: { id: authorId }
    });
  } else {
    console.warn('Cannot navigate to user profile: No author ID available');
  }
}

// Handle image loading errors
function handleImageError(err: Error) {
  console.error('Image failed to load for post ID:', props.post.id, 'Error:', err);
  console.error('Failed image URL:', postImage.value);

  // Log detailed information about the post and image
  console.error('Post details:', {
    id: props.post.id,
    title: props.post.title,
    postType: props.post.postType,
    subType: props.post.subType,
    featuredImage: props.post.featuredImage,
    image: props.post.image,
    _debug_image_source: props.post._debug_image_source,
    _original_image: props.post._original_image,
    _original_image_url: props.post._original_image_url
  });

  // If the URL is from Supabase but doesn't have the full path, suggest a fix
  if (isImageUrlFixable.value) {
    console.log('This image URL might be fixable. Try clicking the "Try Fix URL" button.');
  }
}

// Helper function to get category color
function getCategoryColor(category: string): string {
  const categoryColors: Record<string, string> = {
    'Events': 'green',
    'News': 'blue',
    'Success Stories': 'orange',
    'Funding': 'green-9',
    'Collaboration': 'blue-7',
    'Mentorship': 'purple-7',
    'Innovation': 'deep-orange',
    'Research': 'teal',
    'Training': 'indigo'
  };

  return categoryColors[category] || 'primary';
}
</script>

<style scoped>
.post-card {
  transition: background-color 0.3s ease;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  width: 100%;
  display: block;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.post-card:hover {
  background-color: rgba(0, 0, 0, 0.02);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Post image styling */
.post-image-container {
  margin-left: -16px;
  margin-right: -16px;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.post-image {
  width: 100%;
  border-radius: 4px;
  transition: transform 0.3s ease;
  min-height: 200px;
  object-fit: cover;
}

.post-image:hover {
  transform: scale(1.02);
}

/* Ensure content doesn't overflow */
.post-card img {
  max-width: 100%;
  height: auto;
}

.post-card .q-chip {
  margin-bottom: 4px;
}

/* Post actions styling */
.post-actions-container {
  margin-top: 8px;
  padding-top: 4px;
  background-color: white;
  position: relative;
  z-index: 2;
}

@media (max-width: 599px) {
  .post-card {
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 12px;
    padding-left: 8px;
    padding-right: 8px;
    width: 100%;
  }

  /* Adjust image container for mobile */
  .post-image-container {
    margin-left: -8px;
    margin-right: -8px;
    margin-bottom: 12px;
  }

  /* Ensure content doesn't overflow */
  .row.q-gutter-sm {
    margin-left: 0;
  }

  /* Adjust action buttons for mobile */
  .post-actions-container .q-btn {
    padding: 4px 8px;
    min-height: 32px;
  }
}
</style>
