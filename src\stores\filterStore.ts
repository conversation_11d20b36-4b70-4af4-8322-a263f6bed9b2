import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// Traditional filter interfaces
interface FeedFilters {
  postTypes: string[];
  categories: string[];
  opportunityTypes: string[];
}

interface ProfileFilters {
  profileTypes: string[];
}

interface BlogFilters {
  blogCategories: string[];
  readTime: string | null;
}

interface EventFilters {
  eventTypes: string[];
  eventFormat: string[];
}

interface GroupFilters {
  groupCategories: string[];
}

interface MarketplaceFilters {
  listingTypes: string[];
}

export const useFilterStore = defineStore('filters', () => {
  // Base state for AI-powered filtering
  const activeTab = ref('feed');
  const aiSearchActive = ref(false);
  const lastAIQuery = ref('');
  const aiSearchResults = ref([]);
  const aiSearchContext = ref('');

  // Traditional filtering state
  const searchQuery = ref('');
  const feedFilters = ref<FeedFilters>({
    postTypes: [],
    categories: [],
    opportunityTypes: []
  });
  const profileFilters = ref<ProfileFilters>({
    profileTypes: []
  });
  const blogFilters = ref<BlogFilters>({
    blogCategories: [],
    readTime: null
  });
  const eventFilters = ref<EventFilters>({
    eventTypes: [],
    eventFormat: []
  });
  const groupFilters = ref<GroupFilters>({
    groupCategories: []
  });
  const marketplaceFilters = ref<MarketplaceFilters>({
    listingTypes: []
  });

  // Track AI trigger usage for analytics
  const aiTriggerHistory = ref<Array<{
    triggerKey: string;
    context: string;
    timestamp: Date;
    success: boolean;
  }>>([]);

  // Computed properties for AI-powered filtering
  const currentAIContext = computed(() => {
    return `community-${activeTab.value}`;
  });

  const hasActiveAISearch = computed(() => {
    return aiSearchActive.value && lastAIQuery.value.length > 0;
  });

  const recentAITriggers = computed(() => {
    return aiTriggerHistory.value
      .filter(trigger => trigger.success)
      .slice(-5) // Last 5 successful triggers
      .reverse();
  });

  // Computed property for current filters based on active tab
  const currentFilters = computed(() => {
    const base = {
      searchQuery: searchQuery.value,
      activeTab: activeTab.value
    };

    switch (activeTab.value) {
      case 'feed':
        return {
          ...base,
          ...feedFilters.value
        };
      case 'profiles':
        return {
          ...base,
          ...profileFilters.value
        };
      case 'blog':
        return {
          ...base,
          ...blogFilters.value
        };
      case 'events':
        return {
          ...base,
          ...eventFilters.value
        };
      case 'groups':
        return {
          ...base,
          ...groupFilters.value
        };
      case 'marketplace':
        return {
          ...base,
          ...marketplaceFilters.value
        };
      default:
        return base;
    }
  });

  // Actions for AI-powered filtering
  function setActiveTab(tab: string) {
    console.log(`Filter Store: Setting active tab to ${tab}`);
    activeTab.value = tab;
    // Clear any active AI search when switching tabs
    clearAISearch();
  }

  function startAISearch(query: string, context: string) {
    console.log(`AI Filter Store: Starting AI search - Query: ${query}, Context: ${context}`);
    aiSearchActive.value = true;
    lastAIQuery.value = query;
    aiSearchContext.value = context;
  }

  function setAISearchResults(results: any[]) {
    console.log(`AI Filter Store: Setting AI search results - Count: ${results.length}`);
    aiSearchResults.value = results;
  }

  function clearAISearch() {
    console.log('AI Filter Store: Clearing AI search');
    aiSearchActive.value = false;
    lastAIQuery.value = '';
    aiSearchResults.value = [];
    aiSearchContext.value = '';
  }

  function recordAITrigger(triggerKey: string, context: string, success: boolean) {
    console.log(`AI Filter Store: Recording AI trigger - ${triggerKey} (${success ? 'success' : 'failed'})`);
    aiTriggerHistory.value.push({
      triggerKey,
      context,
      timestamp: new Date(),
      success
    });

    // Keep only last 50 triggers to prevent memory issues
    if (aiTriggerHistory.value.length > 50) {
      aiTriggerHistory.value = aiTriggerHistory.value.slice(-50);
    }
  }

  function getAITriggerStats() {
    const total = aiTriggerHistory.value.length;
    const successful = aiTriggerHistory.value.filter(t => t.success).length;
    const failed = total - successful;

    return {
      total,
      successful,
      failed,
      successRate: total > 0 ? (successful / total) * 100 : 0
    };
  }

  // Reset all AI filter state
  function resetAIFilters() {
    console.log('AI Filter Store: Resetting all AI filters');
    clearAISearch();
    aiTriggerHistory.value = [];
  }

  // Traditional filter actions
  function setSearchQuery(query: string) {
    searchQuery.value = query;
  }

  function updateFeedFilters(filters: Partial<FeedFilters>) {
    feedFilters.value = { ...feedFilters.value, ...filters };
  }

  function updateProfileFilters(filters: Partial<ProfileFilters>) {
    profileFilters.value = { ...profileFilters.value, ...filters };
  }

  function updateBlogFilters(filters: Partial<BlogFilters>) {
    blogFilters.value = { ...blogFilters.value, ...filters };
  }

  function updateEventFilters(filters: Partial<EventFilters>) {
    eventFilters.value = { ...eventFilters.value, ...filters };
  }

  function updateGroupFilters(filters: Partial<GroupFilters>) {
    groupFilters.value = { ...groupFilters.value, ...filters };
  }

  function updateMarketplaceFilters(filters: Partial<MarketplaceFilters>) {
    marketplaceFilters.value = { ...marketplaceFilters.value, ...filters };
  }

  function resetCurrentTabFilters() {
    switch (activeTab.value) {
      case 'feed':
        feedFilters.value = { postTypes: [], categories: [], opportunityTypes: [] };
        break;
      case 'profiles':
        profileFilters.value = { profileTypes: [] };
        break;
      case 'blog':
        blogFilters.value = { blogCategories: [], readTime: null };
        break;
      case 'events':
        eventFilters.value = { eventTypes: [], eventFormat: [] };
        break;
      case 'groups':
        groupFilters.value = { groupCategories: [] };
        break;
      case 'marketplace':
        marketplaceFilters.value = { listingTypes: [] };
        break;
    }
  }

  function resetAllFilters() {
    searchQuery.value = '';
    feedFilters.value = { postTypes: [], categories: [], opportunityTypes: [] };
    profileFilters.value = { profileTypes: [] };
    blogFilters.value = { blogCategories: [], readTime: null };
    eventFilters.value = { eventTypes: [], eventFormat: [] };
    groupFilters.value = { groupCategories: [] };
    marketplaceFilters.value = { listingTypes: [] };
    resetAIFilters();
  }

  return {
    // AI State
    activeTab,
    aiSearchActive,
    lastAIQuery,
    aiSearchResults,
    aiSearchContext,
    aiTriggerHistory,

    // Traditional Filter State
    searchQuery,
    feedFilters,
    profileFilters,
    blogFilters,
    eventFilters,
    groupFilters,
    marketplaceFilters,

    // Computed
    currentAIContext,
    hasActiveAISearch,
    recentAITriggers,
    currentFilters,

    // AI Actions
    setActiveTab,
    startAISearch,
    setAISearchResults,
    clearAISearch,
    recordAITrigger,
    getAITriggerStats,
    resetAIFilters,

    // Traditional Filter Actions
    setSearchQuery,
    updateFeedFilters,
    updateProfileFilters,
    updateBlogFilters,
    updateEventFilters,
    updateGroupFilters,
    updateMarketplaceFilters,
    resetCurrentTabFilters,
    resetAllFilters
  };
});
