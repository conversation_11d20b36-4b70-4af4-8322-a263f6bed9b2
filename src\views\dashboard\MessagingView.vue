<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <q-card class="content-header-card bg-primary text-white">
          <q-card-section>
            <div class="text-h4">Messages</div>
            <p class="text-body1 q-mt-md">
              Communicate directly with other members of the ZbInnovation community.
            </p>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12">
        <q-card class="messaging-container" :flat="$q.screen.lt.md">
          <!-- Mobile view: Back button when viewing a conversation -->
          <div v-if="selectedConversation && $q.screen.lt.md" class="mobile-back-button q-pa-sm bg-grey-2">
            <q-btn flat round dense icon="arrow_back" color="primary" @click="selectedConversation = null" />
            <span class="q-ml-sm text-subtitle1">Back to conversations</span>
          </div>

          <q-card-section class="row no-wrap messaging-content">
            <!-- Conversations List - Hidden on mobile when a conversation is selected -->
            <div
              class="col-12 col-md-4 conversations-list-container"
              style="border-right: 1px solid #e0e0e0;"
              v-show="!selectedConversation || $q.screen.gt.sm"
            >
              <div class="text-subtitle1 q-px-md q-py-sm bg-grey-2">Conversations</div>

              <q-scroll-area :style="conversationsScrollAreaStyle">
                <div v-if="loadingConversations" class="flex flex-center q-pa-lg">
                  <q-spinner color="primary" size="3em" />
                  <div class="q-ml-sm">Loading conversations...</div>
                </div>

                <div v-else-if="conversations.length === 0" class="text-center q-pa-lg">
                  <unified-icon name="chat" size="3em" color="grey-5" />
                  <div class="text-subtitle1 q-mt-sm">No conversations yet</div>
                  <div class="text-caption q-mt-sm">
                    Start a conversation by messaging someone from their profile.
                  </div>
                </div>

                <q-list separator>
                  <q-item
                    v-for="conversation in conversations"
                    :key="conversation.id"
                    clickable
                    :active="selectedConversation?.other_user_id === conversation.other_user_id"
                    @click="selectConversation(conversation)"
                    v-ripple
                  >
                    <q-item-section avatar>
                      <user-avatar
                        :user-id="conversation.other_user_id"
                        :name="getUserName(conversation.other_user)"
                        :email="conversation.other_user?.email || ''"
                        size="48px"
                      />
                    </q-item-section>

                    <q-item-section>
                      <q-item-label>{{ getUserName(conversation.other_user) }}</q-item-label>
                      <q-item-label caption lines="1" class="ellipsis">
                        {{ conversation.content }}
                      </q-item-label>
                      <q-item-label caption>
                        {{ formatDate(conversation.created_at) }}
                      </q-item-label>
                    </q-item-section>

                    <q-item-section side v-if="!conversation.is_read && conversation.recipient_id === currentUserId">
                      <q-badge color="primary" floating />
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-scroll-area>
            </div>

            <!-- Message Thread - Full width on mobile when a conversation is selected -->
            <div
              class="col-12 col-md-8 message-thread-container"
              v-show="selectedConversation || $q.screen.gt.sm"
            >
              <div v-if="!selectedConversation" class="flex flex-center full-height column">
                <unified-icon name="chat" size="4em" color="grey-5" />
                <div class="text-subtitle1 q-mt-md">Select a conversation</div>
                <div class="text-caption q-mt-sm">
                  Choose a conversation from the list to view messages
                </div>
              </div>

              <template v-else>
                <!-- Conversation Header -->
                <div class="conversation-header q-px-md q-py-sm bg-grey-2 row items-center">
                  <user-avatar
                    :user-id="selectedConversation.other_user_id"
                    :name="getUserName(selectedConversation.other_user)"
                    :email="selectedConversation.other_user?.email || ''"
                    size="36px"
                    class="q-mr-sm"
                  />
                  <div>
                    <div class="text-subtitle1">{{ getUserName(selectedConversation.other_user) }}</div>
                    <div class="text-caption">
                      <q-btn
                        flat
                        dense
                        no-caps
                        padding="none"
                        color="primary"
                        :to="{ name: 'user-profile', params: { id: selectedConversation.other_user_id } }"
                      >
                        View Profile
                      </q-btn>
                    </div>
                  </div>
                </div>

                <!-- Messages List -->
                <q-scroll-area ref="messagesScrollArea" :style="messagesScrollAreaStyle">
                  <div class="q-pa-md">
                    <div v-if="loadingMessages" class="flex flex-center q-pa-lg">
                      <q-spinner color="primary" size="2em" />
                      <div class="q-ml-sm">Loading messages...</div>
                    </div>

                    <div v-else-if="messages.length === 0" class="text-center q-pa-lg">
                      <div class="text-subtitle1">No messages yet</div>
                      <div class="text-caption q-mt-sm">
                        Start the conversation by sending a message below.
                      </div>
                    </div>

                    <div v-else>
                      <div
                        v-for="message in sortedMessages"
                        :key="message.id"
                        class="message-item q-mb-md"
                        :class="{ 'sent': message.sender_id === currentUserId, 'received': message.sender_id !== currentUserId }"
                      >
                        <div class="message-bubble">
                          {{ message.content }}
                        </div>
                        <div class="message-meta text-caption">
                          {{ formatDate(message.created_at) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </q-scroll-area>

                <!-- Message Input -->
                <div class="message-input q-px-md q-py-sm">
                  <q-form @submit="sendMessage">
                    <div class="row items-center">
                      <div class="col">
                        <q-input
                          v-model="newMessage"
                          outlined
                          dense
                          placeholder="Type your message..."
                          autofocus
                          :disable="sendingMessage"
                          @keydown.enter.prevent="sendMessage"
                        />
                      </div>
                      <div class="col-auto q-ml-sm">
                        <q-btn
                          color="primary"
                          icon="send"
                          round
                          type="submit"
                          :loading="sendingMessage"
                          :disable="!newMessage.trim()"
                        />
                      </div>
                    </div>
                  </q-form>
                </div>
              </template>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, onUnmounted } from 'vue';
import { date, useQuasar } from 'quasar';
import { useMessagingStore, type Conversation } from '../../stores/messaging';
import { useAuthStore } from '../../stores/auth';
import { useNotificationStore } from '../../stores/notifications';
import UnifiedIcon from '../../components/ui/UnifiedIcon.vue';
import UserAvatar from '../../components/common/UserAvatar.vue';
import { getUniversalUsername } from '../../utils/userUtils';
import { useGlobalServicesStore } from '../../stores/globalServices';

// Define a local Message interface that doesn't require avatar_url
interface Message {
  id: string;
  sender_id: string;
  recipient_id: string;
  content: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
  sender?: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    profile_name?: string;
  };
  recipient?: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    profile_name?: string;
  };
}

// Services and stores
const messagingStore = useMessagingStore();
const authStore = useAuthStore();
const notifications = useNotificationStore();
const globalServices = useGlobalServicesStore();
const cache = globalServices.cacheService;
const realtime = globalServices.realtimeService;
const $q = useQuasar();

// State
const conversations = ref<Conversation[]>([]);
const selectedConversation = ref<Conversation | null>(null);
const messages = ref<Message[]>([]);
const newMessage = ref('');
const loadingConversations = ref(false);
const loadingMessages = ref(false);
const sendingMessage = ref(false);
const messagesScrollArea = ref<any>(null);

// Computed
const currentUserId = computed(() => authStore.currentUser?.id || '');
const sortedMessages = computed(() => {
  return [...messages.value].sort((a, b) => {
    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
  });
});

// Computed property for conversations scroll area height
const conversationsScrollAreaStyle = computed(() => {
  return {
    height: $q.screen.lt.md ? 'calc(100vh - 250px)' : 'calc(100% - 50px)'
  };
});

// Computed property for messages scroll area height
const messagesScrollAreaStyle = computed(() => {
  return {
    height: $q.screen.lt.md ? 'calc(100vh - 250px)' : 'calc(100% - 120px)'
  };
});

// Track retry attempts for component-level operations
const retryAttempts = ref({
  initialLoad: 0,
  maxRetries: 3
});

// Load conversations on mount and set up real-time updates
onMounted(async () => {
  console.log('MessagingView mounted');

  try {
    // Initialize messaging system
    await messagingStore.initializeMessaging();

    // Load initial conversations with retry limit
    try {
      if (retryAttempts.value.initialLoad < retryAttempts.value.maxRetries) {
        retryAttempts.value.initialLoad++;
        await loadConversations();
      } else {
        console.warn('Maximum retry attempts exceeded for initial conversations load');
        notifications.warning('Could not load conversations. Please try again later.');
      }
    } catch (loadError) {
      console.error('Error during initial conversations load:', loadError);
      if (retryAttempts.value.initialLoad < retryAttempts.value.maxRetries) {
        retryAttempts.value.initialLoad++;
        await loadConversations();
      } else {
        notifications.error('Failed to load conversations after multiple attempts');
      }
    }

    // Real-time updates will handle data refresh automatically
    // No need for manual refresh intervals with unified services
    console.log('MessagingView: Using unified real-time services for automatic updates');
  } catch (error) {
    console.error('Error initializing messaging view:', error);
    notifications.error('Failed to initialize messaging. Please try again later.');
  }
});

// Cleanup when component unmounts
onUnmounted(() => {
  console.log('MessagingView: Component unmounting, cleaning up resources');
  // Unified services handle their own cleanup automatically
  // No manual subscription cleanup needed
});

// Function to handle conversation selection
function selectConversation(conversation: Conversation) {
  selectedConversation.value = conversation;

  // Load messages for the selected conversation
  if (conversation) {
    loadMessages(conversation.other_user_id);
  } else {
    messages.value = [];
  }
}

// Methods
// Simplified loading with unified cache - no retry tracking needed

async function loadConversations(showLoading = true) {
  // Prevent duplicate calls - check if we're already loading conversations
  if (loadingConversations.value) {
    console.log('Already loading conversations, skipping duplicate call');
    return;
  }

  // Check unified cache first
  const cacheKey = 'messaging:conversations';
  const cached = cache.get<Conversation[]>(cacheKey);
  if (cached && !showLoading) {
    console.log('MessagingView: Using cached conversations');
    conversations.value = cached;
    return;
  }

  if (showLoading) {
    loadingConversations.value = true;
  }

  try {
    console.log('Loading conversations');
    const result = await messagingStore.loadConversations();

    if (result && result.length > 0) {
      console.log(`Loaded ${result.length} conversations`);

      // Cache the conversations with unified cache
      cache.set(cacheKey, result, {
        ttl: 60 * 1000, // 1 minute
        storage: 'memory'
      });

      // Update the conversations list
      conversations.value = result;

      // If we have a selected conversation, make sure it's updated with the latest data
      if (selectedConversation.value) {
        const updatedConversation = result.find(
          (conv: Conversation) => conv.other_user_id === selectedConversation.value?.other_user_id
        );

        if (updatedConversation) {
          selectedConversation.value = updatedConversation;
        }
      }
      // If no conversation is selected and we have conversations, select the first one
      else if (!selectedConversation.value && result.length > 0) {
        selectedConversation.value = result[0];

        // Load messages for the selected conversation
        if (showLoading) {
          loadMessages(result[0].other_user_id);
        }
      }

    } else {
      console.log('No conversations found');
      conversations.value = [];
    }
  } catch (err: any) {
    console.error('Error loading conversations:', err);
    if (showLoading) {
      notifications.error('Failed to load conversations');
    }
  } finally {
    if (showLoading) {
      loadingConversations.value = false;
    }
  }
}

// Simplified message loading with unified cache

async function loadMessages(userId: string, showLoading = true) {
  // Prevent duplicate calls - check if we're already loading messages for this user
  if (loadingMessages.value) {
    console.log(`Already loading messages for a user, skipping duplicate call for ${userId}`);
    return;
  }

  // Check unified cache first
  const cacheKey = `messaging:messages:${userId}`;
  const cached = cache.get<Message[]>(cacheKey);
  if (cached && !showLoading) {
    console.log(`MessagingView: Using cached messages for user ${userId}`);
    messages.value = cached;
    return;
  }

  if (showLoading) {
    loadingMessages.value = true;
  }

  try {
    console.log('Loading messages for user:', userId);

    // Only clear messages if this is a new conversation
    if (showLoading) {
      messages.value = [];
    }

    // Try to load messages
    try {
      const result = await messagingStore.loadMessages(userId);

      if (result && result.length > 0) {
        console.log(`Loaded ${result.length} messages`);

        // Cache the messages with unified cache
        cache.set(cacheKey, result, {
          ttl: 30 * 1000, // 30 seconds
          storage: 'memory'
        });

        // First load, set all messages
        messages.value = result;

        // Mark all messages from this user as read
        try {
          await messagingStore.markAllMessagesAsRead(userId);

          // Update the unread count in the UI
          await messagingStore.getUnreadCount();
        } catch (markError) {
          console.error('Error marking messages as read:', markError);
          // Continue even if marking as read fails
        }

      } else if (showLoading) {
        // Only clear messages if this is a full reload
        console.log('No messages found');
        messages.value = [];
      }

      // Scroll to bottom after messages load
      await nextTick();
      scrollToBottom();
    } catch (loadError) {
      console.error('Error loading messages:', loadError);
      if (showLoading) {
        notifications.error('Failed to load messages');
      }
      // Continue with current messages array
    }

    // We don't need to reload conversations here anymore
    // This was causing circular database calls
  } catch (err: any) {
    console.error('Unexpected error in loadMessages:', err);
    if (showLoading) {
      notifications.error('An unexpected error occurred');
    }
  } finally {
    if (showLoading) {
      loadingMessages.value = false;
    }
  }
}

async function sendMessage() {
  if (!newMessage.value.trim() || !selectedConversation.value) return;

  sendingMessage.value = true;

  try {
    // Store the message content and recipient before clearing the input
    const messageContent = newMessage.value;
    const recipientId = selectedConversation.value.other_user_id;

    // Clear the input field immediately for better UX
    newMessage.value = '';

    // Add the message to the local UI immediately for better UX
    const tempId = `temp-${Date.now()}`;
    const tempMessage = {
      id: tempId,
      sender_id: currentUserId.value,
      recipient_id: recipientId,
      content: messageContent,
      is_read: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sender: {
        id: currentUserId.value,
        email: authStore.currentUser?.email || '',
        first_name: authStore.currentUser?.user_metadata?.first_name || '',
        last_name: authStore.currentUser?.user_metadata?.last_name || '',
        profile_name: authStore.currentUser?.user_metadata?.profile_name || ''
      }
    };

    // Add to local messages array
    messages.value.push(tempMessage);

    // Scroll to bottom immediately
    await nextTick();
    scrollToBottom();

    // Send the message
    console.log('Sending message to:', recipientId);
    const success = await messagingStore.sendMessage(recipientId, messageContent);

    if (success) {
      console.log('Message sent successfully:', messageContent);

      // Invalidate message cache for this conversation
      cache.invalidate(`messaging:messages:${recipientId}`);
      cache.invalidate('messaging:conversations');

      // Real-time subscription will handle updating the UI
      console.log('MessagingView: Cache invalidated for message updates');
    } else {
      // Remove the temporary message if sending failed
      messages.value = messages.value.filter(msg => msg.id !== tempId);

      notifications.error('Failed to send message. Please try again.');
      // Restore the message content if sending failed
      newMessage.value = messageContent;
    }
  } catch (err) {
    console.error('Error sending message:', err);
    notifications.error('An error occurred while sending your message.');
  } finally {
    sendingMessage.value = false;
  }
}

// Function is now defined above

function scrollToBottom() {
  if (messagesScrollArea.value) {
    const scrollArea = messagesScrollArea.value.$el.querySelector('.scroll');
    if (scrollArea) {
      scrollArea.scrollTop = scrollArea.scrollHeight;
    }
  }
}

function getUserName(user: any): string {
  return getUniversalUsername(user);
}

function formatDate(dateStr: string): string {
  return date.formatDate(dateStr, 'MMM D, YYYY h:mm A');
}
</script>

<style scoped>
.messaging-container {
  height: 600px;
}

.messaging-content {
  height: 600px;
}

.mobile-back-button {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.message-item {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.message-item.sent {
  align-self: flex-end;
  margin-left: auto;
}

.message-item.received {
  align-self: flex-start;
  margin-right: auto;
}

.message-bubble {
  padding: 10px 15px;
  border-radius: 18px;
  word-break: break-word;
}

.sent .message-bubble {
  background-color: #0D8A3E;
  color: white;
  border-bottom-right-radius: 4px;
}

.received .message-bubble {
  background-color: #f2f2f2;
  border-bottom-left-radius: 4px;
}

.message-meta {
  margin-top: 4px;
  opacity: 0.7;
  align-self: flex-end;
}

.sent .message-meta {
  text-align: right;
}

/* Responsive styles for mobile */
@media (max-width: 767px) {
  .messaging-container {
    height: calc(100vh - 120px);
    margin-bottom: 0;
  }

  .messaging-content {
    height: calc(100vh - 160px);
  }

  .message-item {
    max-width: 90%;
  }

  .q-page {
    padding: 8px !important;
  }
}
</style>
