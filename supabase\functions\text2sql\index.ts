import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

// Types for text2sql
interface Text2SQLRequest {
  query: string;
  max_results?: number;
  user_context?: {
    user_id?: string;
    profile_type?: string;
  };
}

interface Text2SQLResponse {
  success: boolean;
  sql_query?: string;
  explanation?: string;
  results?: any[];
  result_count?: number;
  processing_time_ms: number;
  error?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// DeepSeek API configuration for SQL generation
const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY');
const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

// Database schema information for SQL generation
const DATABASE_SCHEMA = {
  tables: {
    personal_details: {
      columns: ['user_id', 'first_name', 'last_name', 'email', 'profile_type', 'created_at'],
      description: 'Basic user information and profile types'
    },
    innovator_profiles: {
      columns: ['innovator_profile_id', 'user_id', 'profile_name', 'bio', 'innovation_area', 'current_stage', 'created_at'],
      description: 'Innovator-specific profile information'
    },
    investor_profiles: {
      columns: ['investor_profile_id', 'user_id', 'profile_name', 'bio', 'investment_focus', 'investment_range', 'created_at'],
      description: 'Investor-specific profile information'
    },
    mentor_profiles: {
      columns: ['mentor_profile_id', 'user_id', 'profile_name', 'bio', 'areas_of_expertise', 'mentoring_approach', 'created_at'],
      description: 'Mentor-specific profile information'
    },
    posts: {
      columns: ['id', 'title', 'content', 'author_id', 'tags', 'created_at', 'updated_at'],
      description: 'User-generated posts and content'
    }
  },
  relationships: [
    'personal_details.user_id -> innovator_profiles.user_id',
    'personal_details.user_id -> investor_profiles.user_id',
    'personal_details.user_id -> mentor_profiles.user_id',
    'personal_details.user_id -> posts.author_id'
  ]
};

/**
 * Generate SQL query from natural language using AI
 */
async function generateSQLQuery(query: string, userContext?: any): Promise<{
  sql: string;
  explanation: string;
}> {
  const systemPrompt = `You are a SQL query generator for a professional networking platform. 

DATABASE SCHEMA:
${JSON.stringify(DATABASE_SCHEMA, null, 2)}

RULES:
1. Generate SAFE, READ-ONLY SELECT queries only
2. Use proper JOINs when querying across tables
3. Always include LIMIT clauses (max 50 rows)
4. Use proper WHERE clauses for filtering
5. Return actual column names, not aliases
6. Focus on the most relevant data for the query
7. Use ILIKE for case-insensitive text searches
8. Always include created_at for temporal context

RESPONSE FORMAT:
Return a JSON object with:
{
  "sql": "SELECT ... FROM ... WHERE ... LIMIT 20;",
  "explanation": "This query finds..."
}

USER QUERY: ${query}`;

  try {
    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: query }
        ],
        max_tokens: 500,
        temperature: 0.1 // Low temperature for consistent SQL generation
      })
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error('No response from AI model');
    }

    // Try to parse JSON response
    try {
      const parsed = JSON.parse(content);
      return {
        sql: parsed.sql,
        explanation: parsed.explanation
      };
    } catch {
      // Fallback: extract SQL from text response
      const sqlMatch = content.match(/SELECT[\s\S]*?;/i);
      return {
        sql: sqlMatch ? sqlMatch[0] : 'SELECT COUNT(*) FROM personal_details LIMIT 1;',
        explanation: 'Generated basic query due to parsing issues'
      };
    }
  } catch (error) {
    console.error('Error generating SQL:', error);
    // Fallback to predefined queries based on keywords
    return generateFallbackQuery(query);
  }
}

/**
 * Fallback query generation for common requests
 */
function generateFallbackQuery(query: string): { sql: string; explanation: string } {
  const lowerQuery = query.toLowerCase();

  if (lowerQuery.includes('innovator')) {
    return {
      sql: `SELECT pd.first_name, pd.last_name, ip.profile_name, ip.innovation_area, ip.current_stage, pd.created_at 
            FROM personal_details pd 
            JOIN innovator_profiles ip ON pd.user_id = ip.user_id 
            ORDER BY pd.created_at DESC LIMIT 20;`,
      explanation: 'Lists innovators on the platform with their innovation areas'
    };
  }

  if (lowerQuery.includes('investor')) {
    return {
      sql: `SELECT pd.first_name, pd.last_name, inv.profile_name, inv.investment_focus, pd.created_at 
            FROM personal_details pd 
            JOIN investor_profiles inv ON pd.user_id = inv.user_id 
            ORDER BY pd.created_at DESC LIMIT 20;`,
      explanation: 'Lists investors on the platform with their investment focus'
    };
  }

  if (lowerQuery.includes('mentor')) {
    return {
      sql: `SELECT pd.first_name, pd.last_name, mp.profile_name, mp.areas_of_expertise, pd.created_at 
            FROM personal_details pd 
            JOIN mentor_profiles mp ON pd.user_id = mp.user_id 
            ORDER BY pd.created_at DESC LIMIT 20;`,
      explanation: 'Lists mentors on the platform with their expertise areas'
    };
  }

  if (lowerQuery.includes('count') || lowerQuery.includes('how many')) {
    return {
      sql: `SELECT profile_type, COUNT(*) as count 
            FROM personal_details 
            WHERE profile_type IS NOT NULL 
            GROUP BY profile_type 
            ORDER BY count DESC;`,
      explanation: 'Shows count of users by profile type'
    };
  }

  // Default fallback
  return {
    sql: `SELECT profile_type, COUNT(*) as total_users 
          FROM personal_details 
          GROUP BY profile_type 
          ORDER BY total_users DESC LIMIT 10;`,
    explanation: 'Shows user distribution by profile type'
  };
}

/**
 * Execute SQL query safely
 */
async function executeSQLQuery(sql: string, maxResults: number = 20): Promise<any[]> {
  try {
    // Additional safety check - ensure it's a SELECT query
    if (!sql.trim().toLowerCase().startsWith('select')) {
      throw new Error('Only SELECT queries are allowed');
    }

    // Execute the query
    const { data, error } = await supabase.rpc('exec_sql_direct', {
      sql_query: sql
    });

    if (error) {
      console.error('SQL execution error:', error);
      throw new Error(`Database error: ${error.message}`);
    }

    // Limit results for safety
    return Array.isArray(data) ? data.slice(0, maxResults) : [];
  } catch (error) {
    console.error('Error executing SQL:', error);
    throw error;
  }
}

// Main handler
Deno.serve(async (req) => {
  const startTime = Date.now();

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { query, max_results = 20, user_context }: Text2SQLRequest = await req.json();

    if (!query || typeof query !== 'string') {
      throw new Error('Query is required and must be a string');
    }

    console.log('Text2SQL request:', { query: query.substring(0, 100) + '...', max_results });

    // Generate SQL query from natural language
    const { sql, explanation } = await generateSQLQuery(query, user_context);
    console.log('Generated SQL:', sql);

    // Execute the SQL query
    const results = await executeSQLQuery(sql, max_results);
    console.log('Query results:', results.length, 'rows');

    const response: Text2SQLResponse = {
      success: true,
      sql_query: sql,
      explanation,
      results,
      result_count: results.length,
      processing_time_ms: Date.now() - startTime
    };

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Text2SQL error:', error);

    const errorResponse: Text2SQLResponse = {
      success: false,
      error: error.message,
      processing_time_ms: Date.now() - startTime
    };

    return new Response(
      JSON.stringify(errorResponse),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
