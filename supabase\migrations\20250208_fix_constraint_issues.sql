-- Fix constraint issues for ON CONFLICT clauses
-- This migration adds missing unique constraints that are referenced in ON CONFLICT clauses

-- 1. Fix embeddings table constraints
-- First, clean up duplicate entries, then add unique constraint
DO $$
BEGIN
    -- Check if the constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'embeddings_source_unique'
        AND table_name = 'embeddings'
        AND table_schema = 'public'
    ) THEN
        -- Remove duplicate entries, keeping only the most recent one
        DELETE FROM public.embeddings
        WHERE id NOT IN (
            SELECT DISTINCT ON (source_table, source_id) id
            FROM public.embeddings
            ORDER BY source_table, source_id, created_at DESC NULLS LAST, id DESC
        );

        RAISE NOTICE 'Cleaned up duplicate embeddings entries';

        -- Add the unique constraint
        ALTER TABLE public.embeddings
        ADD CONSTRAINT embeddings_source_unique
        UNIQUE (source_table, source_id);

        RAISE NOTICE 'Added unique constraint embeddings_source_unique';
    ELSE
        RAISE NOTICE 'Constraint embeddings_source_unique already exists';
    END IF;
END $$;

-- 2. Fix embedding_generation_log table constraints
-- First, clean up duplicate entries, then add unique constraint
DO $$
BEGIN
    -- Check if the constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'embedding_generation_log_unique'
        AND table_name = 'embedding_generation_log'
        AND table_schema = 'public'
    ) THEN
        -- Check if the table exists first
        IF EXISTS (
            SELECT 1 FROM information_schema.tables
            WHERE table_name = 'embedding_generation_log'
            AND table_schema = 'public'
        ) THEN
            -- Remove duplicate entries, keeping only the most recent one
            DELETE FROM public.embedding_generation_log
            WHERE id NOT IN (
                SELECT DISTINCT ON (table_name, record_id, embedding_type) id
                FROM public.embedding_generation_log
                ORDER BY table_name, record_id, embedding_type, updated_at DESC NULLS LAST, id DESC
            );

            RAISE NOTICE 'Cleaned up duplicate embedding_generation_log entries';

            -- Add the unique constraint
            ALTER TABLE public.embedding_generation_log
            ADD CONSTRAINT embedding_generation_log_unique
            UNIQUE (table_name, record_id, embedding_type);

            RAISE NOTICE 'Added unique constraint embedding_generation_log_unique';
        ELSE
            RAISE NOTICE 'Table embedding_generation_log does not exist, skipping constraint';
        END IF;
    ELSE
        RAISE NOTICE 'Constraint embedding_generation_log_unique already exists';
    END IF;
END $$;

-- 3. Fix matchmaking_results table constraints (if needed)
-- Add unique constraint for user_id, matched_entity_id, and entity_type combination
DO $$
BEGIN
    -- Check if the constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'matchmaking_results_unique' 
        AND table_name = 'matchmaking_results'
        AND table_schema = 'public'
    ) THEN
        -- Check if the table exists first
        IF EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'matchmaking_results' 
            AND table_schema = 'public'
        ) THEN
            -- Add the unique constraint
            ALTER TABLE public.matchmaking_results 
            ADD CONSTRAINT matchmaking_results_unique 
            UNIQUE (user_id, matched_entity_id, entity_type);
            
            RAISE NOTICE 'Added unique constraint matchmaking_results_unique';
        ELSE
            RAISE NOTICE 'Table matchmaking_results does not exist, skipping constraint';
        END IF;
    ELSE
        RAISE NOTICE 'Constraint matchmaking_results_unique already exists';
    END IF;
END $$;

-- 4. Verify constraints were added successfully
DO $$
DECLARE
    constraint_count INTEGER;
BEGIN
    -- Count the constraints we just added
    SELECT COUNT(*) INTO constraint_count
    FROM information_schema.table_constraints 
    WHERE constraint_name IN ('embeddings_source_unique', 'embedding_generation_log_unique', 'matchmaking_results_unique')
    AND table_schema = 'public';
    
    RAISE NOTICE 'Successfully added % constraint(s)', constraint_count;
END $$;
