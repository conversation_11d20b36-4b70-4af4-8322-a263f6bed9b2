<template>
  <NewVirtualCommunityView v-if="useNewUI" />
  <q-page v-else class="virtual-community-page">
    <feed-container />
  </q-page>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import FeedContainer from '../../components/feed/FeedContainer.vue'
import NewVirtualCommunityView from './NewVirtualCommunityView.vue'

const route = useRoute()
const router = useRouter()

const useNewUI = computed(()=> route.query.newUI === '1')

onMounted(() => {
  // Ensure tab param exists for both UIs
  if (!route.query.tab) {
    router.replace({ query: { ...route.query, tab: 'feed' } })
  }
})
</script>

<style scoped>
.virtual-community-page { background-color: #f5f5f5; min-height: 100vh; padding: 0; width: 100%; max-width: 100%; }
@media (max-width: 767px) {
  .virtual-community-page { margin: 0; display: flex; justify-content: center; }
  .virtual-community-page > div { width: 100%; max-width: 100%; }
}
</style>
