<template>
  <q-page class="flex flex-center bg-grey-2">
    <q-card class="password-reset-card q-pa-lg">
      <q-card-section class="text-center">
        <div class="text-h4 q-mb-md">Set New Password</div>
        <div class="text-subtitle1 text-grey-7">
          Please enter your new password
        </div>
      </q-card-section>

      <q-card-section>
        <div v-if="passwordChanged" class="text-center q-pa-md">
          <q-icon name="check_circle" color="positive" size="48px" />
          <p class="text-body1 q-mb-md">
            Your password has been successfully changed. You can now sign in with your new password.
          </p>
          <q-btn
            flat
            color="primary"
            label="Go to Sign In"
            class="q-mt-md"
            to="/sign-in"
          />
        </div>

        <div v-else-if="!hasValidToken" class="text-center q-pa-md">
          <q-icon name="error" color="warning" size="48px" />
          <p class="text-body1 q-mb-md">
            Invalid or expired password reset link. Please request a new one.
          </p>
          <q-btn
            flat
            color="primary"
            label="Request New Link"
            class="q-mt-md"
            to="/password-reset"
          />
        </div>

        <div v-else>
          <q-form @submit="handlePasswordChange" class="q-gutter-md">
            <q-input
              v-model="password"
              label="New Password"
              :type="isPwd ? 'password' : 'text'"
              outlined
              :rules="[
                (val) => !!val || 'Password is required',
                (val) => val.length >= 8 || 'Password must be at least 8 characters'
              ]"
            >
              <template v-slot:prepend>
                <q-icon name="lock" />
              </template>
              <template v-slot:append>
                <q-icon
                  :name="isPwd ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="isPwd = !isPwd"
                />
              </template>
            </q-input>

            <q-input
              v-model="confirmPassword"
              label="Confirm Password"
              :type="isConfirmPwd ? 'password' : 'text'"
              outlined
              :rules="[
                (val) => !!val || 'Please confirm your password',
                (val) => val === password || 'Passwords do not match'
              ]"
            >
              <template v-slot:prepend>
                <q-icon name="lock" />
              </template>
              <template v-slot:append>
                <q-icon
                  :name="isConfirmPwd ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="isConfirmPwd = !isConfirmPwd"
                />
              </template>
            </q-input>

            <div class="q-mt-lg">
              <q-btn
                type="submit"
                color="primary"
                label="Change Password"
                class="full-width"
                :loading="loading"
              />
            </div>

            <div class="text-center q-mt-md">
              <q-btn
                flat
                color="primary"
                label="Back to Sign In"
                to="/sign-in"
                :disable="loading"
              />
            </div>
          </q-form>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { supabase } from '../../../lib/supabase'
import { useNotificationStore } from '../../../stores/notifications'

const router = useRouter()
const route = useRoute()
const notificationStore = useNotificationStore()
const password = ref('')
const confirmPassword = ref('')
const isPwd = ref(true)
const isConfirmPwd = ref(true)
const loading = ref(false)
const passwordChanged = ref(false)
const hasValidToken = ref(false)
const tokenData = ref<{ email: string; token: string } | null>(null)

// Simplified token validation for Edge Function approach
onMounted(async () => {
  try {
    console.log('Current URL:', window.location.href)
    console.log('Route query params:', route.query)

    const token = route.query.token as string

    if (!token) {
      console.log('No token found in URL')
      notificationStore.warning('Invalid or expired password reset link. Please request a new one.')
      setTimeout(() => {
        router.push('/password-reset')
      }, 3000)
      return
    }

    console.log('Validating password reset token...')

    // Validate the token with our database
    const { data: tokenRecord, error: tokenError } = await supabase
      .from('password_reset_tokens')
      .select('email, expires_at, used_at')
      .eq('token', token)
      .maybeSingle()

    if (tokenError) {
      console.error('Error validating token:', tokenError)
      notificationStore.error('Error validating your reset link. Please request a new one.')
      setTimeout(() => {
        router.push('/password-reset')
      }, 3000)
      return
    }

    if (!tokenRecord) {
      console.log('Token not found in database')
      notificationStore.error('Invalid password reset link. Please request a new one.')
      setTimeout(() => {
        router.push('/password-reset')
      }, 3000)
      return
    }

    // Check if token is expired
    const now = new Date()
    const expiresAt = new Date(tokenRecord.expires_at)

    if (now > expiresAt) {
      console.log('Token has expired')
      notificationStore.error('Your password reset link has expired. Please request a new one.')
      setTimeout(() => {
        router.push('/password-reset')
      }, 3000)
      return
    }

    // Check if token has already been used
    if (tokenRecord.used_at) {
      console.log('Token has already been used')
      notificationStore.error('This password reset link has already been used. Please request a new one.')
      setTimeout(() => {
        router.push('/password-reset')
      }, 3000)
      return
    }

    // Token is valid
    console.log('Token is valid for email:', tokenRecord.email)
    hasValidToken.value = true
    tokenData.value = {
      email: tokenRecord.email,
      token: token
    }

  } catch (error) {
    console.error('Error processing reset password confirmation:', error)
    notificationStore.error('An error occurred while processing your password reset. Please try again.')
    setTimeout(() => {
      router.push('/password-reset')
    }, 3000)
  }
})

const handlePasswordChange = async () => {
  try {
    loading.value = true

    // Basic validation
    if (!password.value) {
      throw new Error('Password is required')
    }

    if (password.value.length < 8) {
      throw new Error('Password must be at least 8 characters')
    }

    if (password.value !== confirmPassword.value) {
      throw new Error('Passwords do not match')
    }

    if (!tokenData.value) {
      throw new Error('Invalid reset token. Please request a new password reset link.')
    }

    console.log('Updating password for email:', tokenData.value.email)

    // Use RPC function to update password with token validation
    const { data: updateResult, error: updateError } = await supabase.rpc('update_user_password', {
      user_email: tokenData.value.email,
      new_password: password.value,
      reset_token: tokenData.value.token
    })

    if (updateError) {
      console.error('Error calling update_user_password RPC:', updateError)
      throw new Error('Failed to update password. Please try again.')
    }

    // Check the result from the RPC function
    if (!updateResult.success) {
      console.error('RPC function returned error:', updateResult.error)
      throw new Error(updateResult.error || 'Failed to update password.')
    }

    console.log('Password updated successfully:', updateResult.message)

    // Show success message
    passwordChanged.value = true
    notificationStore.success('Password changed successfully')

    // Clear URL parameters
    if (window.history && window.history.replaceState) {
      window.history.replaceState({}, document.title, '/reset-password-confirm')
    }

  } catch (error: any) {
    console.error('Password change error:', error)
    notificationStore.error(error.message || 'Failed to change password. Please try again.')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.password-reset-card {
  width: 100%;
  max-width: 400px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .password-reset-card {
    width: 90%;
  }
}
</style>
