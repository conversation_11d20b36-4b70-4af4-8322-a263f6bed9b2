import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/lib/supabase';
import { useGlobalServicesStore } from './globalServices';
import { useNotificationStore } from './notifications';

export interface ActivityNotification {
  id: string;
  type: string;
  read: boolean;
  timestamp: string;
  details: any;
}

export const useActivityNotificationsStore = defineStore('activityNotifications', () => {
  // State
  const notifications = ref<ActivityNotification[]>([]);
  const connectionRequests = ref<number>(0);
  const unreadActivities = ref<number>(0);
  const loading = ref(false);
  const lastChecked = ref<string | null>(null);

  // Services
  const globalServices = useGlobalServicesStore();
  const activityService = globalServices.activityService;
  const notificationStore = useNotificationStore();

  // Computed properties
  const totalUnreadCount = computed(() => {
    return connectionRequests.value + unreadActivities.value;
  });

  // Get unread connection requests count
  async function fetchConnectionRequestsCount() {
    try {
      loading.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        return 0;
      }

      // Check if the user_connections table exists
      const { error: tableCheckError } = await supabase
        .from('user_connections')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      if (tableCheckError) {
        console.warn('The user_connections table does not exist yet:', tableCheckError.message);
        return 0;
      }

      // Count pending connection requests
      const { data, count, error } = await supabase
        .from('user_connections')
        .select('*', { count: 'exact' })
        .eq('connected_user_id', user.id)
        .eq('connection_status', 'pending');

      // Debug: Log the actual connection requests
      if (data && data.length > 0) {
        console.log('Found connection requests:', data.length);
        console.log('Connection request IDs:', data.map(req => req.id));
      } else {
        console.log('No connection requests found');
      }

      if (error) {
        console.error('Error fetching connection requests count:', error);
        return 0;
      }

      connectionRequests.value = count || 0;
      return count || 0;
    } catch (error) {
      console.error('Error in fetchConnectionRequestsCount:', error);
      return 0;
    } finally {
      loading.value = false;
    }
  }

  // Get unread activities count
  async function fetchUnreadActivitiesCount() {
    try {
      loading.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        return 0;
      }

      // Get the last checked timestamp or default to 7 days ago
      const lastCheckedTime = lastChecked.value ||
        new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();

      // Check if the user_activity table exists (cache the result)
      const tableExistsKey = 'userActivityTableExists'
      let tableExists = sessionStorage.getItem(tableExistsKey)

      if (tableExists === null) {
        const { error: tableCheckError } = await supabase
          .from('user_activity')
          .select('id', { count: 'exact', head: true })
          .limit(1);

        if (tableCheckError) {
          console.warn('The user_activity table does not exist yet:', tableCheckError.message);
          sessionStorage.setItem(tableExistsKey, 'false')
          return 0;
        }
        sessionStorage.setItem(tableExistsKey, 'true')
      } else if (tableExists === 'false') {
        return 0;
      }

      // Count new activities since last check
      const { count, error } = await supabase
        .from('user_activity')
        .select('*', { count: 'exact', head: true })
        .gt('timestamp', lastCheckedTime)
        .not('user_id', 'eq', user.id); // Exclude the user's own activities

      if (error) {
        console.error('Error fetching unread activities count:', error);
        return 0;
      }

      unreadActivities.value = count || 0;
      return count || 0;
    } catch (error) {
      console.error('Error in fetchUnreadActivitiesCount:', error);
      return 0;
    } finally {
      loading.value = false;
    }
  }

  // Mark all activities as read
  function markActivitiesAsRead() {
    lastChecked.value = new Date().toISOString();
    unreadActivities.value = 0;

    // Store the last checked timestamp in localStorage
    localStorage.setItem('lastActivityCheck', lastChecked.value);
  }

  // Mark connection requests as viewed
  function markConnectionRequestsAsViewed() {
    console.log('Marking connection requests as viewed');
    connectionRequests.value = 0;
    localStorage.setItem('lastConnectionRequestCheck', new Date().toISOString());

    // Also update the UI badge in the navigation
    const connectionBadge = document.querySelector('.connections-badge');
    if (connectionBadge) {
      connectionBadge.classList.add('hidden');
    }
  }

  // Initialize the store
  async function initialize() {
    // Skip initialization if already done recently (within 2 minutes)
    const lastInit = sessionStorage.getItem('activityNotificationsLastInit')
    const now = Date.now()
    const twoMinutes = 2 * 60 * 1000

    if (lastInit && (now - parseInt(lastInit)) < twoMinutes) {
      console.log('Activity notifications already initialized recently, skipping')
      return
    }

    // Get the last checked timestamp from localStorage
    lastChecked.value = localStorage.getItem('lastActivityCheck');

    // Fetch counts (but don't wait for both if one fails)
    try {
      await Promise.allSettled([
        fetchConnectionRequestsCount(),
        fetchUnreadActivitiesCount()
      ]);
      sessionStorage.setItem('activityNotificationsLastInit', now.toString())
    } catch (error) {
      console.error('Error initializing activity notifications:', error)
    }
  }

  // Return the store
  return {
    notifications,
    connectionRequests,
    unreadActivities,
    totalUnreadCount,
    loading,
    fetchConnectionRequestsCount,
    fetchUnreadActivitiesCount,
    markActivitiesAsRead,
    markConnectionRequestsAsViewed,
    initialize
  };
});
