import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Types for embedding request
interface EmbedRequest {
  input: string;
  model?: string;
  dimensions?: number;
}

interface EmbedResponse {
  success: boolean;
  embedding: number[];
  model: string;
  dimensions: number;
  processing_time_ms: number;
  error?: string;
}

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

/**
 * Fast local embedding generation
 * Uses deterministic text features to create consistent embeddings
 */
function generateFastEmbedding(text: string, dimensions: number = 384): number[] {
  const embedding = new Array(dimensions).fill(0);
  
  // Preprocess text
  const cleanText = text.toLowerCase().trim();
  const words = cleanText.split(/\s+/).filter(w => w.length > 0);
  const chars = cleanText.split('');
  
  // Generate features based on text characteristics
  for (let i = 0; i < dimensions; i++) {
    let value = 0;
    
    // Character-based features
    if (i < chars.length) {
      const char = chars[i];
      value += char.charCodeAt(0) / 127.0; // ASCII normalization
    }
    
    // Word-based features
    const wordIndex = i % words.length;
    if (words[wordIndex]) {
      const word = words[wordIndex];
      value += word.length / 15.0; // Word length feature
      value += word.charCodeAt(0) / 127.0; // First char feature
    }
    
    // Position-based features
    value += Math.sin(i * 0.1) * 0.1; // Positional encoding
    value += Math.cos(i * 0.05) * 0.1;
    
    // Text hash features
    const textHash = simpleHash(cleanText + i.toString());
    value += (textHash % 100) / 100.0;
    
    // Semantic features based on common words
    const semanticWords = ['innovation', 'technology', 'startup', 'investor', 'mentor', 'ai', 'business'];
    semanticWords.forEach((semWord, idx) => {
      if (cleanText.includes(semWord)) {
        value += Math.sin((i + idx) * 0.2) * 0.2;
      }
    });
    
    // Normalize to [-1, 1] range
    embedding[i] = Math.tanh(value); // Tanh provides good normalization
  }
  
  // L2 normalize the vector
  const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
  if (magnitude > 0) {
    for (let i = 0; i < embedding.length; i++) {
      embedding[i] /= magnitude;
    }
  }
  
  return embedding;
}

/**
 * Simple hash function for text
 */
function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

/**
 * Enhanced embedding with better semantic understanding
 */
function generateSemanticEmbedding(text: string, dimensions: number = 384): number[] {
  const embedding = new Array(dimensions).fill(0);
  
  // Text preprocessing
  const cleanText = text.toLowerCase().trim();
  const words = cleanText.split(/\s+/).filter(w => w.length > 2); // Filter short words
  const sentences = cleanText.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  // Semantic categories for better embeddings
  const categories = {
    innovation: ['innovation', 'innovative', 'startup', 'technology', 'tech', 'ai', 'artificial', 'intelligence'],
    business: ['business', 'company', 'enterprise', 'commercial', 'market', 'industry', 'revenue'],
    investment: ['investment', 'investor', 'funding', 'capital', 'finance', 'money', 'fund'],
    mentorship: ['mentor', 'guidance', 'advice', 'experience', 'expertise', 'coaching', 'support'],
    collaboration: ['collaboration', 'partnership', 'network', 'connect', 'team', 'together']
  };
  
  // Calculate category scores
  const categoryScores = {};
  Object.keys(categories).forEach(category => {
    categoryScores[category] = 0;
    categories[category].forEach(keyword => {
      if (cleanText.includes(keyword)) {
        categoryScores[category] += 1;
      }
    });
  });
  
  // Generate embedding dimensions
  for (let i = 0; i < dimensions; i++) {
    let value = 0;
    
    // Base text features
    if (i < words.length) {
      const word = words[i];
      value += word.length / 10.0;
      value += word.charCodeAt(0) / 100.0;
    }
    
    // Category-based features
    const categoryKeys = Object.keys(categoryScores);
    const categoryIndex = i % categoryKeys.length;
    const category = categoryKeys[categoryIndex];
    value += categoryScores[category] * 0.3;
    
    // Sentence structure features
    const sentenceIndex = i % sentences.length;
    if (sentences[sentenceIndex]) {
      value += sentences[sentenceIndex].length / 50.0;
    }
    
    // Positional and frequency features
    value += Math.sin(i * 0.1) * 0.2;
    value += Math.cos(i * 0.2) * 0.1;
    
    // Text diversity features
    const uniqueChars = new Set(cleanText).size;
    value += (uniqueChars / 26.0) * 0.1; // Alphabet diversity
    
    // Hash-based randomness for consistency
    const hash = simpleHash(cleanText + i.toString() + category);
    value += (hash % 50) / 100.0;
    
    // Apply activation function
    embedding[i] = Math.tanh(value);
  }
  
  // Normalize the embedding vector
  const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
  if (magnitude > 0) {
    for (let i = 0; i < embedding.length; i++) {
      embedding[i] /= magnitude;
    }
  }
  
  return embedding;
}

/**
 * Main Edge Function handler
 */
Deno.serve(async (req: Request) => {
  const startTime = Date.now();
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        }
      }
    );
  }

  try {
    const body = await req.json() as EmbedRequest;
    const { input, model = 'fast-semantic', dimensions = 384 } = body;

    if (!input || typeof input !== 'string') {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid input: text string required'
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders,
          },
        }
      );
    }

    console.log(`Generating fast embedding for: "${input.substring(0, 50)}..." (${dimensions}D)`);

    // Generate embedding based on model type
    let embedding: number[];
    if (model === 'fast-semantic') {
      embedding = generateSemanticEmbedding(input, dimensions);
    } else {
      embedding = generateFastEmbedding(input, dimensions);
    }

    const processingTime = Date.now() - startTime;

    const response: EmbedResponse = {
      success: true,
      embedding,
      model,
      dimensions,
      processing_time_ms: processingTime
    };

    console.log(`Generated ${dimensions}D embedding in ${processingTime}ms`);

    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );

  } catch (error) {
    console.error('Fast embedding error:', error);
    
    const processingTime = Date.now() - startTime;
    
    return new Response(
      JSON.stringify({
        success: false,
        processing_time_ms: processingTime,
        error: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }
});
