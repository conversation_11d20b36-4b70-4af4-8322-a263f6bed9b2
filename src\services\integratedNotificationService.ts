/**
 * Integrated Notification Service
 * 
 * This service integrates the new notification system with existing functionality.
 * It provides a bridge between the old user_notifications table and the new notifications system.
 */

import { supabase } from '@/lib/supabase'
import { useNotificationStore } from '@/stores/notifications'

export interface NotificationData {
  userId: string
  type: 'connection_request' | 'connection_accepted' | 'post_liked' | 'post_commented' | 'message_received' | 'welcome'
  title: string
  message: string
  data?: Record<string, any>
}

export class IntegratedNotificationService {
  private notificationStore = useNotificationStore()

  /**
   * Create a connection request notification
   */
  async createConnectionRequestNotification(
    requesterId: string,
    recipientId: string,
    connectionId: string
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('handle_connection_request_notification', {
        p_requester_id: requesterId,
        p_recipient_id: recipientId,
        p_connection_id: connectionId
      })

      if (error) {
        console.error('Error creating connection request notification:', error)
        return false
      }

      console.log('Connection request notification created:', data)
      return true
    } catch (error) {
      console.error('Failed to create connection request notification:', error)
      return false
    }
  }

  /**
   * Create a connection accepted notification
   */
  async createConnectionAcceptedNotification(
    requesterId: string,
    acceptorId: string,
    connectionId: string
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('handle_connection_accepted_notification', {
        p_requester_id: requesterId,
        p_acceptor_id: acceptorId,
        p_connection_id: connectionId
      })

      if (error) {
        console.error('Error creating connection accepted notification:', error)
        return false
      }

      console.log('Connection accepted notification created:', data)
      return true
    } catch (error) {
      console.error('Failed to create connection accepted notification:', error)
      return false
    }
  }

  /**
   * Create a post like notification
   */
  async createPostLikeNotification(
    postAuthorId: string,
    likerId: string,
    postId: string,
    postTitle: string = 'your post'
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('handle_post_like_notification', {
        p_post_author_id: postAuthorId,
        p_liker_id: likerId,
        p_post_id: postId,
        p_post_title: postTitle
      })

      if (error) {
        console.error('Error creating post like notification:', error)
        return false
      }

      console.log('Post like notification created:', data)
      return true
    } catch (error) {
      console.error('Failed to create post like notification:', error)
      return false
    }
  }

  /**
   * Create a generic notification
   */
  async createNotification(notificationData: NotificationData): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('create_notification_with_email', {
        p_user_id: notificationData.userId,
        p_type: notificationData.type,
        p_title: notificationData.title,
        p_message: notificationData.message,
        p_data: notificationData.data || {}
      })

      if (error) {
        console.error('Error creating notification:', error)
        return false
      }

      console.log('Notification created:', data)
      return true
    } catch (error) {
      console.error('Failed to create notification:', error)
      return false
    }
  }

  /**
   * Get user notifications with pagination
   */
  async getUserNotifications(
    limit: number = 10,
    offset: number = 0,
    unreadOnly: boolean = false
  ) {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (unreadOnly) {
        query = query.eq('read', false)
      }

      const { data, error } = await query

      if (error) throw error

      return data || []
    } catch (error) {
      console.error('Error fetching notifications:', error)
      return []
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<boolean> {
    try {
      const { error } = await supabase.rpc('mark_notification_read', {
        p_notification_id: notificationId
      })

      if (error) {
        console.error('Error marking notification as read:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
      return false
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<number> {
    try {
      const { data, error } = await supabase.rpc('mark_all_notifications_read')

      if (error) {
        console.error('Error marking all notifications as read:', error)
        return 0
      }

      return data || 0
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
      return 0
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount(): Promise<number> {
    try {
      const { data, error } = await supabase.rpc('get_unread_notification_count')

      if (error) {
        console.error('Error getting unread count:', error)
        return 0
      }

      return data || 0
    } catch (error) {
      console.error('Failed to get unread count:', error)
      return 0
    }
  }

  /**
   * Get user notification preferences
   */
  async getNotificationPreferences() {
    try {
      const { data, error } = await supabase.rpc('get_notification_preferences')

      if (error) throw error

      return data
    } catch (error) {
      console.error('Error getting notification preferences:', error)
      return null
    }
  }

  /**
   * Update user notification preferences
   */
  async updateNotificationPreferences(preferences: Record<string, boolean>) {
    try {
      const { data, error } = await supabase.rpc('update_notification_preferences', {
        p_email_enabled: preferences.email_enabled,
        p_push_enabled: preferences.push_enabled,
        p_connection_requests: preferences.connection_requests,
        p_likes: preferences.likes,
        p_comments: preferences.comments,
        p_messages: preferences.messages,
        p_profile_views: preferences.profile_views,
        p_event_reminders: preferences.event_reminders,
        p_system_announcements: preferences.system_announcements
      })

      if (error) throw error

      return data
    } catch (error) {
      console.error('Error updating notification preferences:', error)
      throw error
    }
  }

  /**
   * Subscribe to real-time notifications
   */
  subscribeToNotifications(userId: string, callback: (notification: any) => void) {
    return supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe()
  }

  /**
   * Show toast notification
   */
  showToast(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') {
    this.notificationStore.add({
      type,
      message,
      timeout: 5000
    })
  }
}

// Export singleton instance
export const integratedNotificationService = new IntegratedNotificationService()
