import { ref, computed } from 'vue'
import { useProfileStore } from '../stores/profile'
import { supabase } from '../lib/supabase'
import { useAuthStore } from '../stores/auth'
import { useGlobalServicesStore } from '../stores/globalServices'

/**
 * Simplified User State Service
 *
 * This service provides a way to determine user state with minimal overhead:
 * - NEW_USER: User has signed up but has no profile
 * - INCOMPLETE_PROFILE: User has a profile but it's incomplete
 * - COMPLETE_PROFILE: User has a complete profile
 */
export function useUserState() {
  const profileStore = useProfileStore()
  const authStore = useAuthStore()
  const globalServices = useGlobalServicesStore()
  const cache = globalServices.cacheService

  // State
  const isLoading = ref(false)
  const userState = ref('LOADING')
  const profileData = ref(null)

  /**
   * Check user state directly from the database
   * This is more efficient than loading all profiles
   */
  async function checkUserState() {
    if (!authStore.isAuthenticated || !authStore.currentUser?.id) {
      userState.value = 'NEW_USER'
      return
    }

    // Check unified cache for user state
    const cacheKey = `userState:${authStore.currentUser.id}`
    const cached = cache.get<{ state: string; data: any }>(cacheKey)
    if (cached) {
      console.log('UserStateService: Using cached user state')
      userState.value = cached.state
      profileData.value = cached.data
      return
    }

    isLoading.value = true

    try {
      // Single direct query to get profile status
      const { data, error } = await supabase
        .from('personal_details')
        .select('user_id, profile_completion, profile_type, first_name, last_name')
        .eq('user_id', authStore.currentUser.id)
        .maybeSingle()

      if (error) throw error

      // Store the profile data for later use
      profileData.value = data

      // Determine user state based on query result
      if (!data) {
        userState.value = 'NEW_USER'
      } else if (!data.profile_type) {
        // Consider users without a profile type as new users
        userState.value = 'NEW_USER'
      } else if (data.profile_completion < 100) {
        userState.value = 'INCOMPLETE_PROFILE'
      } else {
        userState.value = 'COMPLETE_PROFILE'
      }

      // Cache the result with unified cache
      cache.set(cacheKey, {
        state: userState.value,
        data: profileData.value
      }, {
        ttl: 5 * 60 * 1000, // 5 minutes
        storage: 'sessionStorage'
      })
      console.log('UserStateService: Cached user state')
    } catch (error) {
      console.error('Error checking user state:', error)
      // Default to NEW_USER on error
      userState.value = 'NEW_USER'
    } finally {
      isLoading.value = false
    }
  }

  // Computed properties based on the current state
  const isNewUser = computed(() => userState.value === 'NEW_USER')
  const hasIncompleteProfile = computed(() => userState.value === 'INCOMPLETE_PROFILE')
  const hasCompleteProfile = computed(() => userState.value === 'COMPLETE_PROFILE')

  // Check if user has any profile data with a type
  const hasAnyProfileData = computed(() => {
    // If we have profile data from direct query with a type
    if (profileData.value && profileData.value.profile_type) {
      return true
    }

    // Fallback to store if available and has a type
    if (profileStore.currentProfile && profileStore.currentProfile.profile_type) {
      return true
    }

    return false
  })

  return {
    userState: computed(() => userState.value),
    isLoading: computed(() => isLoading.value),
    isNewUser,
    hasIncompleteProfile,
    hasCompleteProfile,
    hasAnyProfileData,
    checkUserState,
    profileData: computed(() => profileData.value)
  }
}