# ZB Innovation Hub - Comprehensive Notification System

## Overview

This document describes the comprehensive notification system implemented for ZB Innovation Hub. The system provides both in-app notifications and email notifications for key user actions, with user-configurable preferences and real-time updates.

## Architecture

### Database Schema

The notification system uses the following tables:

#### 1. `notifications` Table
- **Purpose**: Stores all in-app notifications
- **Key Fields**:
  - `id`: UUID primary key
  - `user_id`: Reference to auth.users
  - `type`: Notification type enum
  - `title`: Notification title
  - `message`: Notification content
  - `data`: JSONB for additional context
  - `read`: <PERSON><PERSON>an read status
  - `created_at`, `updated_at`: Timestamps

#### 2. `notification_preferences` Table
- **Purpose**: User notification preferences
- **Key Fields**:
  - `user_id`: Reference to auth.users
  - `email_enabled`: Global email toggle
  - `push_enabled`: Push notification toggle
  - `connection_requests`: Connection request notifications
  - `likes`, `comments`, `messages`: Content interaction notifications
  - `profile_views`, `event_reminders`, `system_announcements`: Other notification types

#### 3. `email_queue` Table
- **Purpose**: Email delivery queue with retry logic
- **Key Fields**:
  - `user_id`: Target user
  - `email_type`: Type of email
  - `recipient_email`: Email address
  - `subject`, `body`: Email content
  - `status`: pending/sent/failed/cancelled
  - `attempts`: Retry counter
  - `scheduled_at`: When to send

#### 4. `connections` Table
- **Purpose**: User connections and requests
- **Key Fields**:
  - `requester_id`, `recipient_id`: User references
  - `status`: pending/accepted/rejected
  - `message`: Optional connection message

### Notification Types

The system supports the following notification types:

1. **welcome** - Welcome emails for new users
2. **connection_request** - Connection request notifications
3. **connection_accepted** - Connection acceptance notifications
4. **connection_rejected** - Connection rejection notifications
5. **message_received** - Direct message notifications
6. **post_liked** - Post like notifications
7. **post_commented** - Post comment notifications
8. **post_shared** - Post share notifications
9. **profile_viewed** - Profile view notifications
10. **event_reminder** - Event reminder notifications
11. **system_announcement** - System announcements

## Implementation Status

### ✅ Completed Features

#### Database Layer
- [x] Complete database schema with all tables
- [x] Row Level Security (RLS) policies
- [x] Database functions for notification management
- [x] Integration functions for existing systems
- [x] Automatic notification preferences creation for new users

#### Backend Services
- [x] Supabase Edge Functions for email sending
- [x] Email queue processing with retry logic
- [x] Welcome email automation on user signup
- [x] Connection request/acceptance notification handlers

#### Frontend Components
- [x] NotificationPreferences.vue - User preference management
- [x] NotificationsList.vue - Notification display
- [x] Toast notification system
- [x] Real-time notification subscriptions

#### Email System
- [x] HTML email templates with responsive design
- [x] Template engine for dynamic content
- [x] SendGrid integration for email delivery
- [x] Unsubscribe and preference management links

#### Integration
- [x] Connection service integration with new notification system
- [x] Existing notification stores compatibility
- [x] User authentication integration

### 🔄 In Progress

#### Frontend Integration
- [ ] Post like/comment notification triggers
- [ ] Message notification integration
- [ ] Profile view tracking and notifications
- [ ] Event reminder system

#### Testing & Validation
- [ ] Unit tests for notification functions
- [ ] Integration tests for email delivery
- [ ] End-to-end testing with Playwright
- [ ] Performance testing for high-volume notifications

## Usage Guide

### For Developers

#### Creating Notifications

Use the `IntegratedNotificationService` for all notification operations:

```typescript
import { integratedNotificationService } from '@/services/integratedNotificationService'

// Connection request notification
await integratedNotificationService.createConnectionRequestNotification(
  requesterId,
  recipientId,
  connectionId
)

// Generic notification
await integratedNotificationService.createNotification({
  userId: 'user-uuid',
  type: 'post_liked',
  title: 'Post Liked',
  message: 'Someone liked your post',
  data: { postId: 'post-uuid', likerId: 'liker-uuid' }
})
```

#### Database Functions

Use database functions for server-side operations:

```sql
-- Create notification with email
SELECT create_notification_with_email(
  'user-uuid',
  'connection_request',
  'New Connection Request',
  'John Doe wants to connect with you',
  '{"requester_id": "requester-uuid"}'::jsonb
);

-- Check if email should be sent
SELECT should_send_email_notification('user-uuid', 'connection_request');
```

### For Users

#### Notification Preferences

Users can manage their notification preferences through:
1. Dashboard → Settings → Notifications
2. Email preference links in notification emails
3. Unsubscribe links in emails

#### Real-time Notifications

- In-app notifications appear as toast messages
- Notification bell shows unread count
- Real-time updates via Supabase realtime subscriptions

## Email Templates

### Available Templates

1. **welcome-email.html** - Welcome message for new users
2. **connection-request.html** - Connection request notifications
3. **connection-accepted.html** - Connection acceptance notifications
4. **new-message.html** - Direct message notifications
5. **post-interaction.html** - Post likes and comments

### Template Features

- Responsive design for all devices
- Consistent branding with ZB Innovation Hub
- Dynamic content with template variables
- Unsubscribe and preference management links
- Social media links and contact information

## Configuration

### Environment Variables

Required environment variables for Edge Functions:

```
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=ZB Innovation Hub
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Email Service Setup

1. Configure SendGrid account
2. Set up domain authentication
3. Configure webhook endpoints for delivery tracking
4. Set up email templates in SendGrid (optional)

## Monitoring and Analytics

### Metrics to Track

1. **Email Delivery Rates**
   - Delivery success rate
   - Bounce rate
   - Open rate
   - Click-through rate

2. **Notification Engagement**
   - Read rates for in-app notifications
   - Response rates for connection requests
   - User preference changes

3. **System Performance**
   - Email queue processing time
   - Notification creation latency
   - Database query performance

### Logging

All notification activities are logged for debugging and analytics:
- Notification creation events
- Email sending attempts and results
- User preference changes
- Error conditions and retries

## Troubleshooting

### Common Issues

1. **Emails not being sent**
   - Check SendGrid API key configuration
   - Verify user email preferences
   - Check email queue for failed attempts

2. **Notifications not appearing**
   - Verify RLS policies
   - Check user authentication
   - Confirm real-time subscription setup

3. **Performance issues**
   - Monitor database query performance
   - Check email queue processing
   - Verify proper indexing

### Debug Tools

- Database function logs in Supabase dashboard
- Edge Function logs for email processing
- Browser console for frontend notification issues
- SendGrid dashboard for email delivery tracking

## Future Enhancements

### Planned Features

1. **Push Notifications**
   - Browser push notifications
   - Mobile app notifications (when available)

2. **Advanced Email Features**
   - Email digest options (daily/weekly summaries)
   - Rich HTML templates with images
   - A/B testing for email content

3. **Analytics Dashboard**
   - Notification engagement metrics
   - User preference analytics
   - Email performance dashboard

4. **Advanced Notification Types**
   - Scheduled notifications
   - Conditional notifications based on user behavior
   - Notification campaigns for announcements

## Support

For technical support or questions about the notification system:
- Email: <EMAIL>
- Documentation: [Internal Wiki Link]
- Issue Tracking: [GitHub Issues Link]
