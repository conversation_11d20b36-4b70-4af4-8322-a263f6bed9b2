<template>
  <div
    v-if="shouldShowTicker"
    :class="[
      'news-ticker-container',
      { 'minimized': isMinimized, 'expanded': !isMinimized }
    ]"
    ref="tickerContainer"
  >
    <!-- Toggle Button -->
    <button
      @click="toggleMinimized"
      class="ticker-toggle-btn"
      :aria-label="isMinimized ? 'Expand news ticker' : 'Minimize news ticker'"
    >
      <q-icon
        :name="isMinimized ? 'expand_less' : 'expand_more'"
        size="20px"
      />
    </button>

    <!-- News Content -->
    <div v-if="!isMinimized" class="ticker-content">
      <div class="ticker-header">
        <q-icon name="campaign" size="20px" class="news-icon" />
        <span class="ticker-title">Community Feed</span>
      </div>

      <div class="ticker-scroll-container" ref="scrollContainer">
        <div
          class="ticker-scroll-content"
          :style="{ animationDuration: `${scrollDuration}s` }"
          @animationend="onAnimationEnd"
        >
          <div
            v-for="(item, index) in activeNewsItems"
            :key="item.id"
            class="news-item"
            @click="onNewsItemClick(item)"
          >
            <span class="news-category" :class="`category-${item.category}`">
              {{ item.category.toUpperCase() }}
            </span>
            <span class="news-title">{{ item.title }}</span>
            <span class="news-separator">•</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Minimized View -->
    <div v-else class="ticker-minimized">
      <q-icon name="campaign" size="16px" class="news-icon-mini" />
      <span class="ticker-mini-text">{{ activeNewsItems.length }} news updates</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useRoute, useRouter } from 'vue-router';
import { usePostsStore } from '../../stores/posts';
import { supabase } from '../../lib/supabase';
import type { Post } from '../../types/post';

// Composables
const $q = useQuasar();
const route = useRoute();
const router = useRouter();
const postsStore = usePostsStore();

// Reactive state
const isMinimized = ref(false);
const tickerContainer = ref<HTMLElement>();
const scrollContainer = ref<HTMLElement>();
const scrollDuration = ref(30);
const isLoading = ref(true);
const error = ref<string | null>(null);

// Hide ticker for now (as requested)
const shouldShowTicker = computed(() => {
  return false; // Temporarily hidden
});

// Get recent posts from the posts store for the ticker
const activeNewsItems = computed(() => {
  // Get the most recent posts from the community feed
  const recentPosts = postsStore.posts
    .filter(post => post.status === 'published')
    .sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())
    .slice(0, 10); // Show only the 10 most recent posts

  // Transform posts to match the ticker display format
  return recentPosts.map(post => ({
    id: post.id.toString(),
    title: post.title || post.excerpt || 'Untitled Post',
    content: post.content || post.excerpt || '',
    category: post.subType || post.postType || 'general',
    priority: 1,
    is_active: true,
    start_date: post.createdAt || new Date().toISOString(),
    created_at: post.createdAt || new Date().toISOString(),
    // Keep reference to original post for navigation
    originalPost: post
  }));
});

// Local storage key for user preferences
const STORAGE_KEY = 'smilefactory_news_ticker_minimized';

// Methods
const loadUserPreferences = () => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (saved !== null) {
      isMinimized.value = JSON.parse(saved);
    }
  } catch (error) {
    console.warn('Failed to load news ticker preferences:', error);
  }
};

const saveUserPreferences = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(isMinimized.value));
  } catch (error) {
    console.warn('Failed to save news ticker preferences:', error);
  }
};

const toggleMinimized = () => {
  isMinimized.value = !isMinimized.value;
  userHasManuallyToggled = true; // Mark that user has manually interacted
  saveUserPreferences();
};

const fetchNewsItems = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    // Fetch posts from the posts store
    await postsStore.fetchPosts();

    // If no posts found, the computed property will handle the empty state
    console.log('News ticker: Loaded', postsStore.posts.length, 'posts for ticker');
  } catch (err: any) {
    console.error('Error fetching news items:', err);
    error.value = err.message || 'Failed to load news';

    // No fallback content - show empty state instead
    newsItems.value = [];

    // Only show notification in development or if it's a critical error
    if (process.env.NODE_ENV === 'development' || err.code === 'PGRST116') {
      $q.notify({
        type: 'negative',
        message: 'Failed to load news updates',
        caption: 'Using fallback content',
        position: 'top',
        timeout: 3000
      });
    }
  } finally {
    isLoading.value = false;
  }
};

const onNewsItemClick = (item: any) => {
  try {
    // Get the original post from the item
    const post = item.originalPost;

    if (!post) {
      console.error('No original post found for news item:', item);
      return;
    }

    // Navigate to the appropriate route based on post type
    const postType = post.postType?.toLowerCase() || 'general';
    const subType = post.subType?.toLowerCase() || '';
    const postId = post.id;
    const postSlug = post.slug || `${postType}-${postId}`;

    console.log('News ticker: Navigation details:', { postType, subType, postId, postSlug });

    // Special handling for blog posts which use slug-based routing
    if (postType === 'blog' || subType === 'blog') {
      console.log('News ticker: Navigating to blog article:', { slug: postSlug });
      router.push({ name: 'virtual-community-article', params: { slug: postSlug } });
      return;
    }

    // For all other post types, use the unified post-details route
    console.log('News ticker: Navigating to post details:', { id: postId });
    router.push({
      name: 'post-details',
      params: { id: postId }
    });
  } catch (error) {
    console.error('News ticker: Error in onNewsItemClick:', error);
    // Fallback to showing a dialog if navigation fails
    $q.dialog({
      title: item.title,
      message: item.content,
      html: true,
      ok: 'Close'
    });
  }
};

const onAnimationEnd = () => {
  // Restart animation for continuous scrolling
  if (scrollContainer.value) {
    const scrollContent = scrollContainer.value.querySelector('.ticker-scroll-content') as HTMLElement;
    if (scrollContent) {
      scrollContent.style.animation = 'none';
      scrollContent.offsetHeight; // Trigger reflow
      scrollContent.style.animation = '';
    }
  }
};

// Calculate scroll duration based on content length
const updateScrollDuration = () => {
  if (activeNewsItems.value.length > 0) {
    const baseSpeed = 50; // pixels per second
    const contentLength = activeNewsItems.value.reduce((total, item) =>
      total + item.title.length + item.category.length + 20, 0
    );
    scrollDuration.value = Math.max(15, contentLength / baseSpeed);
  }
};

// Enhanced scroll detection for auto-minimize
let intersectionObserver: IntersectionObserver | null = null;
let lastScrollY = 0;
let scrollTimeout: NodeJS.Timeout | null = null;
let userHasManuallyToggled = false;

const setupScrollObserver = () => {
  if (!tickerContainer.value) return;

  // Check if user has manually toggled the ticker
  const savedPreference = localStorage.getItem(STORAGE_KEY);
  userHasManuallyToggled = savedPreference !== null;

  // Intersection Observer for visibility detection
  intersectionObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Ticker is visible - restore to user preference or default expanded
          if (!userHasManuallyToggled) {
            isMinimized.value = false;
          }
        }
      });
    },
    {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    }
  );

  intersectionObserver.observe(tickerContainer.value);

  // Enhanced scroll direction detection
  const handleScroll = () => {
    const currentScrollY = window.scrollY;
    const scrollDirection = currentScrollY > lastScrollY ? 'down' : 'up';

    // Clear existing timeout
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }

    // Only auto-minimize if user hasn't manually set preference
    // For bottom ticker: minimize when scrolling up (reading), expand when scrolling down (browsing)
    if (!userHasManuallyToggled) {
      if (scrollDirection === 'up' && currentScrollY > 200) {
        // User is scrolling up (reading content) - minimize to give more space
        isMinimized.value = true;
      } else if (scrollDirection === 'down' || currentScrollY < 100) {
        // User is scrolling down (browsing) or near top - show ticker
        isMinimized.value = false;
      }
    }

    // Set timeout to reset scroll detection after user stops scrolling
    scrollTimeout = setTimeout(() => {
      // Optional: Add any post-scroll behavior here
    }, 150);

    lastScrollY = currentScrollY;
  };

  // Add scroll listener with throttling
  let ticking = false;
  const throttledScrollHandler = () => {
    if (!ticking) {
      requestAnimationFrame(() => {
        handleScroll();
        ticking = false;
      });
      ticking = true;
    }
  };

  window.addEventListener('scroll', throttledScrollHandler, { passive: true });

  // Store the cleanup function
  const cleanup = () => {
    window.removeEventListener('scroll', throttledScrollHandler);
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }
  };

  // Return cleanup function for use in onUnmounted
  return cleanup;
};

// Realtime subscription for posts
let realtimeSubscription: any = null;

const setupRealtimeSubscription = () => {
  realtimeSubscription = supabase
    .channel('posts_changes')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'posts'
      },
      (payload) => {
        console.log('Posts changed:', payload);
        fetchNewsItems(); // Refresh posts for ticker
      }
    )
    .subscribe();
};

// Lifecycle
let scrollCleanup: (() => void) | null = null;

onMounted(async () => {
  loadUserPreferences();
  await fetchNewsItems();
  scrollCleanup = setupScrollObserver();
  setupRealtimeSubscription();
});

onUnmounted(() => {
  if (intersectionObserver) {
    intersectionObserver.disconnect();
  }
  if (realtimeSubscription) {
    realtimeSubscription.unsubscribe();
  }
  if (scrollCleanup) {
    scrollCleanup();
  }
});

// Watchers
watch(activeNewsItems, updateScrollDuration, { immediate: true });
</script>

<style scoped>
.news-ticker-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(5px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  font-family: 'Rubik', sans-serif;
}

.news-ticker-container.expanded {
  height: auto;
  min-height: 30px;
}

.news-ticker-container.minimized {
  height: 21px;
}

.ticker-toggle-btn {
  position: absolute;
  right: 10px;
  top: -15px;
  background: rgba(13, 138, 62, 0.8);
  border: 1px solid rgba(164, 202, 57, 0.6);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #a4ca39;
  transition: all 0.2s ease;
  z-index: 1001;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ticker-toggle-btn:hover {
  background: rgba(13, 138, 62, 0.9);
  border-color: rgba(164, 202, 57, 0.8);
  color: #b8d946;
  transform: scale(1.05);
}

.ticker-content {
  display: flex;
  align-items: center;
  padding: 6px 50px 6px 15px;
  min-height: 30px;
}

.ticker-header {
  display: flex;
  align-items: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.news-icon {
  color: #a4ca39;
  margin-right: 8px;
}

.ticker-title {
  color: #a4ca39;
  font-weight: 400;
  font-size: 14px;
  white-space: nowrap;
}

.ticker-scroll-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 18px;
}

.ticker-scroll-content {
  display: flex;
  align-items: center;
  animation: scroll-left linear infinite;
  white-space: nowrap;
}

@keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.news-item {
  display: inline-flex;
  align-items: center;
  margin-right: 40px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.news-item:hover {
  opacity: 0.8;
}

.news-category {
  background: rgba(164, 202, 57, 0.8);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  margin-right: 10px;
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.category-announcement {
  background: rgba(245, 166, 35, 0.8);
}

.category-event {
  background: rgba(156, 39, 176, 0.8);
}

.category-partnership {
  background: rgba(33, 150, 243, 0.8);
}

.news-title {
  color: #b8d946;
  font-size: 14px;
  font-weight: 300;
}

.news-separator {
  color: rgba(164, 202, 57, 0.8);
  margin: 0 15px;
  font-weight: 400;
}

.ticker-minimized {
  display: flex;
  align-items: center;
  padding: 8px 50px 8px 15px;
  height: 35px;
}

.news-icon-mini {
  color: white;
  margin-right: 8px;
}

.ticker-mini-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ticker-content {
    padding: 5px 40px 5px 10px;
  }

  .ticker-header {
    margin-right: 15px;
  }

  .ticker-title {
    font-size: 12px;
  }

  .news-title {
    font-size: 12px;
  }

  .news-category {
    font-size: 9px;
    padding: 1px 6px;
  }
}

@media (max-width: 480px) {
  .ticker-header .ticker-title {
    display: none;
  }

  .ticker-content {
    padding: 5px 35px 5px 35px;
  }

  .news-item {
    margin-right: 25px;
  }
}
</style>