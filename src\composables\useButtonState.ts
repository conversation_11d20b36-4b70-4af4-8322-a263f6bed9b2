import { ref, computed } from 'vue'

/**
 * Button State Management Composable
 * 
 * Provides loading states and click protection for buttons to prevent
 * multiple rapid clicks that can cause navigation issues.
 */
export function useButtonState(initialLoading = false) {
  const loading = ref(initialLoading)
  const lastClickTime = ref(0)
  const minClickInterval = 500 // Minimum time between clicks in ms

  /**
   * Check if button is disabled (loading or too soon after last click)
   */
  const isDisabled = computed(() => {
    const now = Date.now()
    return loading.value || (now - lastClickTime.value < minClickInterval)
  })

  /**
   * Execute an async action with loading state and click protection
   */
  async function executeAction<T>(
    action: () => Promise<T> | T,
    options: {
      showLoadingFor?: number // Minimum loading time in ms
      preventRapidClicks?: boolean // Whether to prevent rapid clicks
    } = {}
  ): Promise<T | null> {
    const now = Date.now()
    
    // Prevent rapid clicks if enabled
    if (options.preventRapidClicks !== false && now - lastClickTime.value < minClickInterval) {
      console.log('useButtonState: Preventing rapid click')
      return null
    }

    lastClickTime.value = now
    loading.value = true

    try {
      const startTime = Date.now()
      const result = await action()
      
      // Ensure minimum loading time if specified
      if (options.showLoadingFor) {
        const elapsed = Date.now() - startTime
        if (elapsed < options.showLoadingFor) {
          await new Promise(resolve => setTimeout(resolve, options.showLoadingFor! - elapsed))
        }
      }

      return result
    } catch (error) {
      console.error('useButtonState: Action failed:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * Set loading state manually
   */
  function setLoading(state: boolean) {
    loading.value = state
  }

  /**
   * Reset the button state
   */
  function reset() {
    loading.value = false
    lastClickTime.value = 0
  }

  return {
    loading: computed(() => loading.value),
    isDisabled,
    executeAction,
    setLoading,
    reset
  }
}

/**
 * Navigation Button Composable
 * 
 * Specialized composable for navigation buttons that combines
 * button state management with navigation service.
 */
export function useNavigationButton() {
  const buttonState = useButtonState()

  /**
   * Navigate with button loading state
   */
  async function navigateWithLoading(
    destination: string | { name: string; params?: any; query?: any },
    options: {
      closeMenus?: boolean
      showLoadingFor?: number
    } = {}
  ): Promise<boolean> {
    return buttonState.executeAction(async () => {
      const { useNavigation } = await import('../services/navigationService')
      const { navigateTo, navigateWithMenuClose } = useNavigation()
      
      const navigationFn = options.closeMenus ? navigateWithMenuClose : navigateTo
      const success = await navigationFn(destination)
      
      if (!success) {
        throw new Error('Navigation failed')
      }
      
      return success
    }, {
      showLoadingFor: options.showLoadingFor || 200,
      preventRapidClicks: true
    }) ?? false
  }

  return {
    ...buttonState,
    navigateWithLoading
  }
}
