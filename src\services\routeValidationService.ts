/**
 * Route Validation Service
 * 
 * Validates routes before generating CTAs to prevent broken links and ensure
 * proper authentication requirements are met.
 */

import router from '../router';
import { useAuthStore } from '../stores/auth';

export interface RouteValidationResult {
  isValid: boolean;
  requiresAuth: boolean;
  exists: boolean;
  error?: string;
  suggestion?: string;
}

export interface ValidatedRoute {
  path: string;
  name?: string;
  params?: Record<string, string>;
  query?: Record<string, string>;
  hash?: string;
}

/**
 * Validate a route path and check authentication requirements
 */
export function validateRoute(path: string, params?: Record<string, string>): RouteValidationResult {
  try {
    // Handle empty or invalid paths
    if (!path || typeof path !== 'string') {
      return {
        isValid: false,
        requiresAuth: false,
        exists: false,
        error: 'Invalid route path provided',
        suggestion: 'Provide a valid route path string'
      };
    }

    // Clean the path
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    
    // Try to resolve the route
    const resolved = router.resolve({
      path: cleanPath,
      params: params || {}
    });

    // Check if route exists (not a catch-all route)
    const routeExists = resolved.name !== undefined && !resolved.path.includes('*');
    
    if (!routeExists) {
      return {
        isValid: false,
        requiresAuth: false,
        exists: false,
        error: `Route '${cleanPath}' does not exist`,
        suggestion: 'Check available routes or use a valid route path'
      };
    }

    // Check authentication requirements
    const requiresAuth = checkAuthRequirement(resolved.meta);
    
    // Get current auth state
    const authStore = useAuthStore();
    const isAuthenticated = authStore.isAuthenticated;

    // Validate auth requirements
    if (requiresAuth && !isAuthenticated) {
      return {
        isValid: false,
        requiresAuth: true,
        exists: true,
        error: `Route '${cleanPath}' requires authentication`,
        suggestion: 'User must be authenticated to access this route'
      };
    }

    // Check guest-only routes
    const guestOnly = resolved.meta?.guestOnly === true;
    if (guestOnly && isAuthenticated) {
      return {
        isValid: false,
        requiresAuth: false,
        exists: true,
        error: `Route '${cleanPath}' is for guests only`,
        suggestion: 'This route is not accessible to authenticated users'
      };
    }

    return {
      isValid: true,
      requiresAuth,
      exists: true
    };

  } catch (error) {
    console.error('Route validation error:', error);
    return {
      isValid: false,
      requiresAuth: false,
      exists: false,
      error: `Route validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      suggestion: 'Check route syntax and try again'
    };
  }
}

/**
 * Check if a route requires authentication
 */
function checkAuthRequirement(meta: any): boolean {
  if (!meta) return false;
  
  // Check direct requiresAuth flag
  if (meta.requiresAuth === true) return true;
  
  // Check nested meta properties
  if (meta.meta?.requiresAuth === true) return true;
  
  // Check for dashboard routes (convention)
  return false;
}

/**
 * Get a list of available public routes
 */
export function getPublicRoutes(): string[] {
  const routes = router.getRoutes();
  return routes
    .filter(route => !checkAuthRequirement(route.meta) && route.name)
    .map(route => route.path)
    .filter(path => !path.includes(':') && !path.includes('*'));
}

/**
 * Get a list of available authenticated routes
 */
export function getAuthenticatedRoutes(): string[] {
  const routes = router.getRoutes();
  return routes
    .filter(route => checkAuthRequirement(route.meta) && route.name)
    .map(route => route.path)
    .filter(path => !path.includes(':') && !path.includes('*'));
}

/**
 * Suggest alternative routes if validation fails
 */
export function suggestAlternativeRoutes(failedPath: string): string[] {
  const allRoutes = router.getRoutes();
  const suggestions: string[] = [];
  
  // Simple similarity matching
  const pathParts = failedPath.toLowerCase().split('/').filter(Boolean);
  
  for (const route of allRoutes) {
    if (!route.name || route.path.includes('*')) continue;
    
    const routeParts = route.path.toLowerCase().split('/').filter(Boolean);
    const commonParts = pathParts.filter(part => routeParts.includes(part));
    
    if (commonParts.length > 0) {
      suggestions.push(route.path);
    }
  }
  
  return suggestions.slice(0, 3); // Return top 3 suggestions
}

/**
 * Validate and sanitize a route for CTA generation
 */
export function validateRouteForCTA(path: string, params?: Record<string, string>): {
  isValid: boolean;
  validatedPath?: string;
  error?: string;
  requiresAuth?: boolean;
} {
  const validation = validateRoute(path, params);
  
  if (!validation.isValid) {
    return {
      isValid: false,
      error: validation.error,
      requiresAuth: validation.requiresAuth
    };
  }
  
  // Build the validated path
  let validatedPath = path;
  if (params) {
    for (const [key, value] of Object.entries(params)) {
      validatedPath = validatedPath.replace(`:${key}`, value);
    }
  }
  
  return {
    isValid: true,
    validatedPath,
    requiresAuth: validation.requiresAuth
  };
}

/**
 * Check if a route is safe for navigation
 */
export function isRouteSafe(path: string): boolean {
  // Block potentially dangerous routes
  const dangerousPatterns = [
    /javascript:/i,
    /data:/i,
    /vbscript:/i,
    /<script/i,
    /on\w+=/i
  ];
  
  return !dangerousPatterns.some(pattern => pattern.test(path));
}

/**
 * Log route validation failures for monitoring
 */
export function logRouteValidationFailure(path: string, error: string, context?: any): void {
  console.warn('Route validation failed:', {
    path,
    error,
    context,
    timestamp: new Date().toISOString()
  });
  
  // In production, this could send to analytics/monitoring service
  // Example: analytics.track('route_validation_failed', { path, error, context });
}

export default {
  validateRoute,
  validateRouteForCTA,
  getPublicRoutes,
  getAuthenticatedRoutes,
  suggestAlternativeRoutes,
  isRouteSafe,
  logRouteValidationFailure
};
