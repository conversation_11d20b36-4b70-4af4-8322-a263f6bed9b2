# AI Implementation: Database Migrations & Schema Enhancements

## Overview

This document provides the specific database migrations required to enhance the existing ZbInnovation platform with AI capabilities, based on the current Supabase database structure analysis.

## Current State Assessment

### ✅ **Already Available**
- pg_vector extension (v0.8.0) ✅
- ai_conversations table with vector embeddings ✅
- ai_messages table with content embeddings ✅
- Comprehensive profile system (8 profile types) ✅
- Rich content and interaction system ✅

### **Required Enhancements**

## Migration 1: Add Vector Embeddings to Profile Tables

```sql
-- Migration: Add profile embeddings for semantic matching
-- File: supabase/migrations/20250116_add_profile_embeddings.sql

-- Add embedding columns to all profile types
ALTER TABLE innovator_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(1536),
ADD COLUMN IF NOT EXISTS interests_embedding vector(1536),
ADD COLUMN IF NOT EXISTS goals_embedding vector(1536);

ALTER TABLE mentor_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(1536),
ADD COLUMN IF NOT EXISTS expertise_embedding vector(1536),
ADD COLUMN IF NOT EXISTS approach_embedding vector(1536);

ALTER TABLE investor_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(1536),
ADD COLUMN IF NOT EXISTS criteria_embedding vector(1536),
ADD COLUMN IF NOT EXISTS focus_embedding vector(1536);

ALTER TABLE professional_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(1536),
ADD COLUMN IF NOT EXISTS skills_embedding vector(1536),
ADD COLUMN IF NOT EXISTS services_embedding vector(1536);

ALTER TABLE industry_expert_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(1536),
ADD COLUMN IF NOT EXISTS expertise_embedding vector(1536),
ADD COLUMN IF NOT EXISTS domain_embedding vector(1536);

ALTER TABLE academic_student_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(1536),
ADD COLUMN IF NOT EXISTS research_embedding vector(1536),
ADD COLUMN IF NOT EXISTS career_embedding vector(1536);

ALTER TABLE academic_institution_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(1536),
ADD COLUMN IF NOT EXISTS research_embedding vector(1536),
ADD COLUMN IF NOT EXISTS programs_embedding vector(1536);

ALTER TABLE organisation_profiles 
ADD COLUMN IF NOT EXISTS profile_embedding vector(1536),
ADD COLUMN IF NOT EXISTS focus_embedding vector(1536),
ADD COLUMN IF NOT EXISTS collaboration_embedding vector(1536);

-- Create indexes for efficient vector similarity search
CREATE INDEX IF NOT EXISTS innovator_profiles_embedding_idx 
ON innovator_profiles USING ivfflat (profile_embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS mentor_profiles_embedding_idx 
ON mentor_profiles USING ivfflat (profile_embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS investor_profiles_embedding_idx 
ON investor_profiles USING ivfflat (profile_embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS professional_profiles_embedding_idx 
ON professional_profiles USING ivfflat (profile_embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS industry_expert_profiles_embedding_idx 
ON industry_expert_profiles USING ivfflat (profile_embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS academic_student_profiles_embedding_idx 
ON academic_student_profiles USING ivfflat (profile_embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS academic_institution_profiles_embedding_idx 
ON academic_institution_profiles USING ivfflat (profile_embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS organisation_profiles_embedding_idx 
ON organisation_profiles USING ivfflat (profile_embedding vector_cosine_ops);
```

## Migration 2: Add Content Embeddings

```sql
-- Migration: Add content embeddings for intelligent discovery
-- File: supabase/migrations/20250116_add_content_embeddings.sql

-- Add embedding columns to posts table
ALTER TABLE posts 
ADD COLUMN IF NOT EXISTS content_embedding vector(1536),
ADD COLUMN IF NOT EXISTS title_embedding vector(1536),
ADD COLUMN IF NOT EXISTS tags_embedding vector(1536);

-- Add embedding columns to marketplace listings
ALTER TABLE marketplace_listings 
ADD COLUMN IF NOT EXISTS listing_embedding vector(1536),
ADD COLUMN IF NOT EXISTS description_embedding vector(1536);

-- Add embedding columns to groups
ALTER TABLE groups 
ADD COLUMN IF NOT EXISTS group_embedding vector(1536),
ADD COLUMN IF NOT EXISTS description_embedding vector(1536);

-- Create indexes for content similarity search
CREATE INDEX IF NOT EXISTS posts_content_embedding_idx 
ON posts USING ivfflat (content_embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS posts_title_embedding_idx 
ON posts USING ivfflat (title_embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS marketplace_listings_embedding_idx 
ON marketplace_listings USING ivfflat (listing_embedding vector_cosine_ops);

CREATE INDEX IF NOT EXISTS groups_embedding_idx 
ON groups USING ivfflat (group_embedding vector_cosine_ops);
```

## Migration 3: AI User Interaction Tracking

```sql
-- Migration: Create AI user interaction tracking system
-- File: supabase/migrations/20250116_create_ai_interaction_tracking.sql

-- Track user interactions for AI learning and personalization
CREATE TABLE IF NOT EXISTS ai_user_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Interaction details
  interaction_type VARCHAR(50) NOT NULL, -- 'view', 'like', 'connect', 'message', 'bookmark', 'share'
  target_type VARCHAR(50) NOT NULL, -- 'profile', 'post', 'event', 'group', 'marketplace', 'blog'
  target_id UUID NOT NULL,
  
  -- Context information
  page_context VARCHAR(100), -- 'dashboard', 'community', 'profiles', 'events', 'groups', 'marketplace'
  section_context VARCHAR(100), -- 'feed', 'blog', 'events', 'recommended', 'search_results'
  filters_applied JSONB DEFAULT '{}',
  search_query TEXT,
  
  -- Interaction metadata
  interaction_strength FLOAT DEFAULT 1.0,
  session_id UUID,
  device_type VARCHAR(50),
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX ai_user_interactions_user_id_idx ON ai_user_interactions(user_id);
CREATE INDEX ai_user_interactions_type_idx ON ai_user_interactions(interaction_type, target_type);
CREATE INDEX ai_user_interactions_created_at_idx ON ai_user_interactions(created_at DESC);
CREATE INDEX ai_user_interactions_target_idx ON ai_user_interactions(target_type, target_id);

-- Enable RLS for privacy
ALTER TABLE ai_user_interactions ENABLE ROW LEVEL SECURITY;

-- Create RLS policy
CREATE POLICY "Users can only access their own interactions"
ON ai_user_interactions
FOR ALL TO authenticated
USING (user_id = auth.uid());
```

## Migration 4: AI User Insights & Preferences

```sql
-- Migration: Create AI-generated user insights system
-- File: supabase/migrations/20250116_create_ai_user_insights.sql

-- Store AI-generated insights about users
CREATE TABLE IF NOT EXISTS ai_user_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Insight details
  insight_type VARCHAR(50) NOT NULL, -- 'interests', 'goals', 'compatibility', 'behavior_pattern'
  insight_category VARCHAR(50) NOT NULL, -- 'profile', 'content', 'networking', 'engagement'
  insight_data JSONB NOT NULL,
  
  -- AI metadata
  confidence_score FLOAT DEFAULT 0.0,
  embedding vector(1536),
  model_version VARCHAR(50),
  
  -- Lifecycle
  generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  last_validated TIMESTAMP WITH TIME ZONE
);

-- Create indexes
CREATE INDEX ai_user_insights_user_id_idx ON ai_user_insights(user_id);
CREATE INDEX ai_user_insights_type_idx ON ai_user_insights(insight_type, insight_category);
CREATE INDEX ai_user_insights_embedding_idx ON ai_user_insights 
USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX ai_user_insights_active_idx ON ai_user_insights(is_active, expires_at);

-- Enable RLS
ALTER TABLE ai_user_insights ENABLE ROW LEVEL SECURITY;

-- Create RLS policy
CREATE POLICY "Users can only access their own insights"
ON ai_user_insights
FOR ALL TO authenticated
USING (user_id = auth.uid());

-- User preference learning table
CREATE TABLE IF NOT EXISTS ai_user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Preference details
  preference_type VARCHAR(50) NOT NULL, -- 'content', 'profile', 'communication', 'notification'
  preference_key VARCHAR(100) NOT NULL,
  preference_value JSONB NOT NULL,
  
  -- Learning metadata
  confidence_score FLOAT DEFAULT 0.0,
  source VARCHAR(50) NOT NULL, -- 'explicit', 'implicit', 'ai_inferred'
  evidence_count INTEGER DEFAULT 1,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, preference_type, preference_key)
);

-- Create indexes
CREATE INDEX ai_user_preferences_user_id_idx ON ai_user_preferences(user_id);
CREATE INDEX ai_user_preferences_type_idx ON ai_user_preferences(preference_type);
```

## Migration 5: Enhanced AI Matchmaking

```sql
-- Migration: Create enhanced AI matchmaking system
-- File: supabase/migrations/20250116_create_ai_matchmaking_enhanced.sql

-- AI-calculated matchmaking scores (enhances existing matchmaking_results)
CREATE TABLE IF NOT EXISTS ai_matchmaking_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  target_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Match details
  match_type VARCHAR(50) NOT NULL, -- 'profile', 'content', 'collaboration', 'mentorship', 'investment'
  match_category VARCHAR(50) NOT NULL, -- 'compatibility', 'opportunity', 'learning', 'networking'
  
  -- Scoring
  overall_score FLOAT NOT NULL,
  score_breakdown JSONB NOT NULL, -- detailed scoring components
  confidence_level FLOAT DEFAULT 0.0,
  
  -- Context
  calculation_context JSONB DEFAULT '{}',
  model_version VARCHAR(50),
  
  -- Lifecycle
  calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  is_current BOOLEAN DEFAULT true,
  
  UNIQUE(source_user_id, target_user_id, match_type)
);

-- Create indexes
CREATE INDEX ai_matchmaking_scores_source_idx ON ai_matchmaking_scores(source_user_id);
CREATE INDEX ai_matchmaking_scores_target_idx ON ai_matchmaking_scores(target_user_id);
CREATE INDEX ai_matchmaking_scores_score_idx ON ai_matchmaking_scores(overall_score DESC);
CREATE INDEX ai_matchmaking_scores_type_idx ON ai_matchmaking_scores(match_type, match_category);
CREATE INDEX ai_matchmaking_scores_current_idx ON ai_matchmaking_scores(is_current, expires_at);

-- Enable RLS
ALTER TABLE ai_matchmaking_scores ENABLE ROW LEVEL SECURITY;

-- Create RLS policy
CREATE POLICY "Users can access their own matchmaking scores"
ON ai_matchmaking_scores
FOR SELECT TO authenticated
USING (source_user_id = auth.uid() OR target_user_id = auth.uid());

-- Content recommendation scores
CREATE TABLE IF NOT EXISTS ai_content_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Content details
  content_type VARCHAR(50) NOT NULL, -- 'post', 'event', 'group', 'marketplace', 'blog'
  content_id UUID NOT NULL,
  
  -- Recommendation details
  recommendation_type VARCHAR(50) NOT NULL, -- 'interest_based', 'behavior_based', 'collaborative'
  relevance_score FLOAT NOT NULL,
  reasoning JSONB DEFAULT '{}',
  
  -- Context
  generated_for_context VARCHAR(100), -- 'dashboard', 'feed', 'discovery', 'search'
  model_version VARCHAR(50),
  
  -- Lifecycle
  generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true
);

-- Create indexes
CREATE INDEX ai_content_recommendations_user_id_idx ON ai_content_recommendations(user_id);
CREATE INDEX ai_content_recommendations_content_idx ON ai_content_recommendations(content_type, content_id);
CREATE INDEX ai_content_recommendations_score_idx ON ai_content_recommendations(relevance_score DESC);
CREATE INDEX ai_content_recommendations_active_idx ON ai_content_recommendations(is_active, expires_at);

-- Enable RLS
ALTER TABLE ai_content_recommendations ENABLE ROW LEVEL SECURITY;

-- Create RLS policy
CREATE POLICY "Users can only access their own content recommendations"
ON ai_content_recommendations
FOR ALL TO authenticated
USING (user_id = auth.uid());
```

## Database Functions for AI Operations

```sql
-- Migration: Create database functions for AI operations
-- File: supabase/migrations/20250116_create_ai_functions.sql

-- Function to find similar profiles using vector similarity
CREATE OR REPLACE FUNCTION find_similar_profiles(
  profile_type TEXT,
  query_embedding vector(1536),
  match_threshold FLOAT DEFAULT 0.7,
  max_results INT DEFAULT 20,
  exclude_user_id UUID DEFAULT NULL
)
RETURNS TABLE (
  user_id UUID,
  profile_id UUID,
  similarity_score FLOAT,
  profile_data JSONB
) AS $$
BEGIN
  CASE profile_type
    WHEN 'innovator' THEN
      RETURN QUERY
      SELECT 
        ip.user_id,
        ip.id as profile_id,
        1 - (ip.profile_embedding <=> query_embedding) as similarity_score,
        jsonb_build_object(
          'profile_name', ip.profile_name,
          'innovation_area', ip.innovation_area,
          'innovation_stage', ip.innovation_stage,
          'goals', ip.short_term_goals,
          'challenges', ip.current_challenges
        ) as profile_data
      FROM innovator_profiles ip
      WHERE ip.user_id != COALESCE(exclude_user_id, '00000000-0000-0000-0000-000000000000'::UUID)
        AND ip.is_public = true
        AND ip.profile_embedding IS NOT NULL
        AND (ip.profile_embedding <=> query_embedding) < (1 - match_threshold)
      ORDER BY similarity_score DESC
      LIMIT max_results;
    
    WHEN 'mentor' THEN
      RETURN QUERY
      SELECT 
        mp.user_id,
        mp.mentor_profile_id as profile_id,
        1 - (mp.profile_embedding <=> query_embedding) as similarity_score,
        jsonb_build_object(
          'profile_name', mp.profile_name,
          'areas_of_expertise', mp.areas_of_expertise,
          'industry_experience', mp.industry_experience,
          'mentoring_approach', mp.mentoring_approach
        ) as profile_data
      FROM mentor_profiles mp
      WHERE mp.user_id != COALESCE(exclude_user_id, '00000000-0000-0000-0000-000000000000'::UUID)
        AND mp.is_public = true
        AND mp.profile_embedding IS NOT NULL
        AND (mp.profile_embedding <=> query_embedding) < (1 - match_threshold)
      ORDER BY similarity_score DESC
      LIMIT max_results;
    
    -- Add similar cases for other profile types
    ELSE
      RAISE EXCEPTION 'Unsupported profile type: %', profile_type;
  END CASE;
END;
$$ LANGUAGE plpgsql;

-- Function to find similar content
CREATE OR REPLACE FUNCTION find_similar_content(
  content_type TEXT,
  query_embedding vector(1536),
  match_threshold FLOAT DEFAULT 0.7,
  max_results INT DEFAULT 20,
  user_id UUID DEFAULT NULL
)
RETURNS TABLE (
  content_id BIGINT,
  similarity_score FLOAT,
  content_data JSONB
) AS $$
BEGIN
  IF content_type = 'posts' THEN
    RETURN QUERY
    SELECT 
      p.id as content_id,
      1 - (p.content_embedding <=> query_embedding) as similarity_score,
      jsonb_build_object(
        'title', p.title,
        'post_type', p.post_type,
        'sub_type', p.sub_type,
        'content', LEFT(p.content, 200),
        'tags', p.tags,
        'created_at', p.created_at
      ) as content_data
    FROM posts p
    WHERE p.status = 'published'
      AND p.content_embedding IS NOT NULL
      AND (p.content_embedding <=> query_embedding) < (1 - match_threshold)
    ORDER BY similarity_score DESC
    LIMIT max_results;
  ELSE
    RAISE EXCEPTION 'Unsupported content type: %', content_type;
  END IF;
END;
$$ LANGUAGE plpgsql;
```

## Next Steps

1. **Apply Migrations Sequentially**
   - Run migrations in order to avoid dependency issues
   - Test each migration in development environment first

2. **Generate Initial Embeddings**
   - Create Edge Function to generate embeddings for existing data
   - Process profiles and content in batches

3. **Implement AI Services**
   - Build services that utilize these new database structures
   - Create API endpoints for AI functionality

4. **Testing & Validation**
   - Test vector similarity searches
   - Validate RLS policies
   - Performance test with realistic data volumes

## Implementation Priority

### **Phase 1: Core Database Enhancements (Week 1)**
1. Apply Migration 1: Profile embeddings
2. Apply Migration 2: Content embeddings
3. Apply Migration 3: Interaction tracking

### **Phase 2: AI Intelligence Layer (Week 2)**
1. Apply Migration 4: User insights & preferences
2. Apply Migration 5: Enhanced matchmaking
3. Create database functions for AI operations

### **Phase 3: Data Population (Week 3)**
1. Generate embeddings for existing profiles
2. Generate embeddings for existing content
3. Populate initial user insights

### **Phase 4: Service Integration (Week 4)**
1. Build AI services using new database structure
2. Implement bidirectional UI integration
3. Test and optimize performance

This database enhancement provides the foundation for intelligent AI features while preserving all existing functionality and data.
