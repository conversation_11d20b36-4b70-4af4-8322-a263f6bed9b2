<template>
  <q-card class="mentorship-event-card" :class="cardClasses">
    <!-- Event Image/Header -->
    <div class="event-header" :style="headerStyle">
      <div class="event-type-badge">
        <q-chip
          :color="getEventTypeColor(event.event_type)"
          text-color="white"
          size="sm"
          :label="formatEventType(event.event_type)"
        />
      </div>
      <div class="event-status-badge" v-if="event.status !== 'published'">
        <q-chip
          :color="getStatusColor(event.status)"
          text-color="white"
          size="sm"
          :label="event.status?.toUpperCase()"
        />
      </div>
    </div>

    <q-card-section>
      <!-- Title and Category -->
      <div class="text-h6 q-mb-sm event-title" @click="$emit('view', event)">
        {{ event.title }}
      </div>
      <div v-if="event.category" class="text-caption text-grey-6 q-mb-sm">
        <unified-icon name="category" size="sm" class="q-mr-xs" />
        {{ event.category }}
      </div>

      <!-- Description -->
      <div class="text-body2 text-grey-8 q-mb-md event-description">
        {{ truncatedDescription }}
      </div>

      <!-- Event Details -->
      <div class="event-details q-mb-md">
        <!-- Date and Time -->
        <div class="detail-item">
          <unified-icon name="schedule" color="primary" class="q-mr-sm" />
          <div>
            <div class="text-body2 text-weight-medium">
              {{ formatDate(event.scheduled_start_time) }}
            </div>
            <div class="text-caption text-grey-6">
              {{ formatTime(event.scheduled_start_time) }} - {{ formatTime(event.scheduled_end_time) }}
            </div>
          </div>
        </div>

        <!-- Participants -->
        <div class="detail-item">
          <unified-icon name="people" color="secondary" class="q-mr-sm" />
          <div>
            <div class="text-body2">
              {{ event.current_participants || 0 }} / {{ event.max_participants }} participants
            </div>
            <q-linear-progress
              :value="participationRate"
              color="secondary"
              size="4px"
              class="q-mt-xs"
            />
          </div>
        </div>

        <!-- Platform -->
        <div v-if="event.meeting_platform" class="detail-item">
          <unified-icon name="videocam" color="accent" class="q-mr-sm" />
          <div class="text-body2">
            {{ formatPlatform(event.meeting_platform) }}
          </div>
        </div>

        <!-- Price -->
        <div class="detail-item">
          <unified-icon name="attach_money" color="positive" class="q-mr-sm" />
          <div class="text-body2 text-weight-medium">
            {{ event.is_free ? 'Free' : `$${event.price}` }}
          </div>
        </div>
      </div>

      <!-- Learning Objectives -->
      <div v-if="event.learning_objectives?.length > 0" class="q-mb-md">
        <div class="text-caption text-grey-6 q-mb-xs">Learning Objectives:</div>
        <div class="learning-objectives">
          <q-chip
            v-for="(objective, index) in event.learning_objectives.slice(0, 2)"
            :key="index"
            size="sm"
            color="blue-1"
            text-color="blue-8"
            :label="objective"
            class="q-mr-xs q-mb-xs"
          />
          <q-chip
            v-if="event.learning_objectives.length > 2"
            size="sm"
            color="grey-3"
            text-color="grey-7"
            :label="`+${event.learning_objectives.length - 2} more`"
            class="q-mr-xs q-mb-xs"
          />
        </div>
      </div>
    </q-card-section>

    <!-- Actions -->
    <q-card-actions align="between" class="q-pa-md">
      <div class="event-meta">
        <q-avatar size="24px" class="q-mr-xs">
          <unified-icon name="person" />
        </q-avatar>
        <span class="text-caption text-grey-6">
          {{ event.mentor_name || 'Mentor' }}
        </span>
      </div>

      <div class="action-buttons">
        <!-- View/Register Button -->
        <q-btn
          v-if="!showActions"
          :color="canRegister ? 'primary' : 'grey-5'"
          :label="getActionLabel()"
          :disable="!canRegister"
          size="sm"
          @click="handlePrimaryAction"
        />

        <!-- Admin Actions -->
        <template v-if="showActions">
          <q-btn
            flat
            icon="edit"
            size="sm"
            @click="$emit('edit', event)"
            class="q-mr-xs"
          >
            <q-tooltip>Edit Event</q-tooltip>
          </q-btn>
          <q-btn
            flat
            icon="delete"
            size="sm"
            color="negative"
            @click="$emit('delete', event)"
          >
            <q-tooltip>Delete Event</q-tooltip>
          </q-btn>
        </template>
      </div>
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { date } from 'quasar'
import UnifiedIcon from '../ui/UnifiedIcon.vue'

// Props
const props = defineProps<{
  event: any
  showActions?: boolean
}>()

// Emits
const emit = defineEmits<{
  view: [event: any]
  edit: [event: any]
  delete: [event: any]
  register: [event: any]
}>()

// Computed
const cardClasses = computed(() => ({
  'event-past': isPastEvent.value,
  'event-full': isFull.value,
  'event-draft': props.event.status === 'draft'
}))

const headerStyle = computed(() => ({
  background: props.event.event_image_url 
    ? `linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url(${props.event.event_image_url})`
    : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  backgroundSize: 'cover',
  backgroundPosition: 'center'
}))

const truncatedDescription = computed(() => {
  const desc = props.event.description || ''
  return desc.length > 120 ? desc.substring(0, 120) + '...' : desc
})

const participationRate = computed(() => {
  const current = props.event.current_participants || 0
  const max = props.event.max_participants || 1
  return current / max
})

const isPastEvent = computed(() => {
  return new Date(props.event.scheduled_start_time) < new Date()
})

const isFull = computed(() => {
  return (props.event.current_participants || 0) >= (props.event.max_participants || 0)
})

const canRegister = computed(() => {
  return props.event.status === 'published' && 
         !isPastEvent.value && 
         !isFull.value
})

// Methods
function getEventTypeColor(type: string): string {
  const colors: Record<string, string> = {
    'workshop': 'blue',
    'webinar': 'purple',
    'group-session': 'green',
    'networking': 'orange',
    'panel': 'red',
    'masterclass': 'indigo',
    'q-and-a': 'teal'
  }
  return colors[type] || 'grey'
}

function getStatusColor(status: string): string {
  const colors: Record<string, string> = {
    'draft': 'grey',
    'published': 'green',
    'cancelled': 'red',
    'completed': 'blue'
  }
  return colors[status] || 'grey'
}

function formatEventType(type: string): string {
  return type.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

function formatPlatform(platform: string): string {
  const platforms: Record<string, string> = {
    'zoom': 'Zoom',
    'google-meet': 'Google Meet',
    'teams': 'Microsoft Teams',
    'in-person': 'In Person',
    'other': 'Other Platform'
  }
  return platforms[platform] || platform
}

function formatDate(dateString: string): string {
  return date.formatDate(new Date(dateString), 'MMM DD, YYYY')
}

function formatTime(dateString: string): string {
  return date.formatDate(new Date(dateString), 'h:mm A')
}

function getActionLabel(): string {
  if (isPastEvent.value) return 'Past Event'
  if (isFull.value) return 'Full'
  if (props.event.status !== 'published') return 'Not Available'
  return 'Register'
}

function handlePrimaryAction() {
  if (canRegister.value) {
    emit('register', props.event)
  } else {
    emit('view', props.event)
  }
}
</script>

<style scoped>
.mentorship-event-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
}

.mentorship-event-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.event-header {
  height: 120px;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
}

.event-type-badge {
  position: absolute;
  top: 12px;
  left: 12px;
}

.event-status-badge {
  position: absolute;
  top: 12px;
  right: 12px;
}

.event-title {
  cursor: pointer;
  transition: color 0.2s;
}

.event-title:hover {
  color: var(--q-primary);
}

.event-description {
  line-height: 1.4;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.learning-objectives {
  display: flex;
  flex-wrap: wrap;
}

.event-meta {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  align-items: center;
}

/* Card states */
.event-past {
  opacity: 0.7;
}

.event-full .event-header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
}

.event-draft {
  border: 2px dashed #ccc;
}
</style>
