<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Resend Email (Working)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-form {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #0D8A3E;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0a6b31;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .note {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Test Resend Email (Working)</h1>
    
    <div class="note">
        <strong>🔧 Fixed Issues:</strong>
        <ul>
            <li>✅ Proper function syntax with correct imports</li>
            <li>✅ Fixed CORS headers</li>
            <li>✅ Proper error handling</li>
            <li>✅ Using RESEND_KEY environment variable</li>
        </ul>
    </div>
    
    <div class="test-form">
        <h3>Working Resend Function Test</h3>
        <p>This function has proper syntax and CORS configuration.</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" name="email" required placeholder="<EMAIL>" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="firstName">First Name:</label>
                <input type="text" id="firstName" name="firstName" placeholder="Developer" value="Developer">
            </div>
            
            <button type="submit" id="submitBtn">Send Welcome Email</button>
        </form>
    </div>
    
    <div id="result" class="result"></div>

    <script type="module">
        // Import Supabase client
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        // Initialize Supabase client
        const supabaseUrl = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const firstName = document.getElementById('firstName').value;
            const resultDiv = document.getElementById('result');
            const submitBtn = document.getElementById('submitBtn');
            
            // Show loading
            submitBtn.disabled = true;
            submitBtn.textContent = 'Sending...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = 'Sending welcome email...';
            
            try {
                // Call the working resend function
                const { data, error } = await supabase.functions.invoke('resend-email-working', {
                    body: {
                        email: email,
                        firstName: firstName
                    }
                });
                
                if (error) {\n                    resultDiv.className = 'result error';\n                    resultDiv.innerHTML = `❌ Supabase Error: ${JSON.stringify(error, null, 2)}`;\n                    return;\n                }\n                \n                if (data && data.success) {\n                    resultDiv.className = 'result success';\n                    resultDiv.innerHTML = `✅ Success! ${data.message}`;\n                    \n                    // Show email ID if available\n                    if (data.emailId) {\n                        const detailDiv = document.createElement('div');\n                        detailDiv.className = 'result info';\n                        detailDiv.style.marginTop = '10px';\n                        detailDiv.innerHTML = `Email ID: ${data.emailId}`;\n                        resultDiv.appendChild(detailDiv);\n                    }\n                } else {\n                    resultDiv.className = 'result error';\n                    resultDiv.innerHTML = `❌ Function Error: ${data?.error || 'Unknown error occurred'}`;\n                }\n            } catch (error) {\n                resultDiv.className = 'result error';\n                resultDiv.innerHTML = `❌ Network Error: ${error.message}`;\n            } finally {\n                // Reset button\n                submitBtn.disabled = false;\n                submitBtn.textContent = 'Send Welcome Email';\n            }\n        });\n    </script>\n</body>\n</html>
