<template>
  <div class="mentorship-event-list">
    <!-- Header -->
    <div class="row items-center justify-between q-mb-md">
      <div class="text-h6">
        <unified-icon name="event" class="q-mr-sm" />
        Mentorship Events
      </div>
      <q-btn
        v-if="showCreateButton"
        color="primary"
        icon="add"
        label="Create Event"
        @click="$emit('create-event')"
      />
    </div>

    <!-- Filters -->
    <q-card flat bordered class="q-mb-md">
      <q-card-section class="q-pa-sm">
        <div class="row q-gutter-sm items-center">
          <q-select
            v-model="filters.eventType"
            :options="eventTypeOptions"
            label="Event Type"
            outlined
            dense
            clearable
            emit-value
            map-options
            style="min-width: 150px"
          />
          <q-select
            v-model="filters.status"
            :options="statusOptions"
            label="Status"
            outlined
            dense
            clearable
            emit-value
            map-options
            style="min-width: 120px"
          />
          <q-input
            v-model="filters.search"
            label="Search events"
            outlined
            dense
            clearable
            style="min-width: 200px"
          >
            <template v-slot:prepend>
              <unified-icon name="search" />
            </template>
          </q-input>
          <q-btn
            flat
            icon="refresh"
            @click="refreshEvents"
            :loading="loading"
          />
        </div>
      </q-card-section>
    </q-card>

    <!-- Loading State -->
    <div v-if="loading" class="flex flex-center q-pa-lg">
      <q-spinner color="primary" size="3em" />
      <div class="q-ml-md">Loading events...</div>
    </div>

    <!-- Empty State -->
    <q-card v-else-if="filteredEvents.length === 0" flat bordered class="text-center q-pa-lg">
      <unified-icon name="event_busy" size="4em" color="grey-5" />
      <div class="text-h6 q-mt-md text-grey-7">
        {{ filters.search || filters.eventType || filters.status ? 'No events match your filters' : 'No events found' }}
      </div>
      <div class="text-subtitle1 q-mt-sm text-grey-6">
        {{ showCreateButton ? 'Create your first mentorship event to get started!' : 'Check back later for upcoming events.' }}
      </div>
      <q-btn
        v-if="showCreateButton && !filters.search && !filters.eventType && !filters.status"
        color="primary"
        label="Create Event"
        class="q-mt-md"
        @click="$emit('create-event')"
      />
    </q-card>

    <!-- Events Grid -->
    <div v-else class="row q-gutter-md">
      <div
        v-for="event in filteredEvents"
        :key="event.id"
        class="col-12 col-md-6 col-lg-4"
      >
        <mentorship-event-card
          :event="event"
          :show-actions="showActions"
          @register="$emit('register', $event)"
          @edit="$emit('edit-event', $event)"
          @delete="$emit('delete-event', $event)"
          @view="$emit('view-event', $event)"
        />
      </div>
    </div>

    <!-- Load More -->
    <div v-if="hasMore && !loading" class="text-center q-mt-lg">
      <q-btn
        flat
        color="primary"
        label="Load More Events"
        @click="$emit('load-more')"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import UnifiedIcon from '../ui/UnifiedIcon.vue'
import MentorshipEventCard from './MentorshipEventCard.vue'

// Props
const props = defineProps<{
  events: any[]
  loading?: boolean
  showCreateButton?: boolean
  showActions?: boolean
  hasMore?: boolean
}>()

// Emits
const emit = defineEmits<{
  'create-event': []
  'edit-event': [event: any]
  'delete-event': [event: any]
  'view-event': [event: any]
  'register': [event: any]
  'load-more': []
}>()

// State
const filters = ref({
  search: '',
  eventType: '',
  status: ''
})

// Options
const eventTypeOptions = [
  { label: 'All Types', value: '' },
  { label: 'Workshop', value: 'workshop' },
  { label: 'Webinar', value: 'webinar' },
  { label: 'Group Session', value: 'group-session' },
  { label: 'Networking', value: 'networking' },
  { label: 'Panel Discussion', value: 'panel' },
  { label: 'Masterclass', value: 'masterclass' },
  { label: 'Q&A Session', value: 'q-and-a' }
]

const statusOptions = [
  { label: 'All Status', value: '' },
  { label: 'Published', value: 'published' },
  { label: 'Draft', value: 'draft' },
  { label: 'Completed', value: 'completed' },
  { label: 'Cancelled', value: 'cancelled' }
]

// Computed
const filteredEvents = computed(() => {
  let filtered = [...props.events]

  // Search filter
  if (filters.value.search) {
    const search = filters.value.search.toLowerCase()
    filtered = filtered.filter(event =>
      event.title?.toLowerCase().includes(search) ||
      event.description?.toLowerCase().includes(search) ||
      event.category?.toLowerCase().includes(search)
    )
  }

  // Event type filter
  if (filters.value.eventType) {
    filtered = filtered.filter(event => event.event_type === filters.value.eventType)
  }

  // Status filter
  if (filters.value.status) {
    filtered = filtered.filter(event => event.status === filters.value.status)
  }

  return filtered
})

// Methods
function refreshEvents() {
  emit('load-more')
}
</script>

<style scoped>
.mentorship-event-list {
  width: 100%;
}
</style>
