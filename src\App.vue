<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRoute, RouteLocationNormalizedLoaded } from 'vue-router'
import NotificationList from './components/NotificationList.vue'
import { useSEO } from './utils/seo'
import { useMessagingStore } from './stores/messaging'
import { useAuthStore } from './stores/auth'
import { useActivityNotificationsStore } from './stores/activityNotifications'
import { useGlobalServicesStore } from './stores/globalServices'

const route = useRoute() as RouteLocationNormalizedLoaded
const { updateMeta } = useSEO()
const messagingStore = useMessagingStore()
const authStore = useAuthStore()
const activityNotificationsStore = useActivityNotificationsStore()
const globalServicesStore = useGlobalServicesStore()

// Update meta tags on route changes
route.path && route.meta && onMounted(() => {
  updateMeta({
    title: `${(route.meta.title as string) || 'Your Platform'} | Your Platform Name`,
    description: (route.meta.description as string) || 'Default page description',
    keywords: (route.meta.keywords as string[]) || ['default', 'keywords'],
    ogTitle: (route.meta.ogTitle as string) || (route.meta.title as string) || 'Your Platform',
    ogDescription: (route.meta.description as string) || 'Default page description',
  })
})

// Initial SEO setup and authentication initialization
onMounted(async () => {
  // Add robots meta tag
  const robotsMeta = document.createElement('meta')
  robotsMeta.setAttribute('name', 'robots')
  robotsMeta.setAttribute('content', 'index, follow')
  document.head.appendChild(robotsMeta)

  // Add viewport meta tag
  const viewportMeta = document.createElement('meta')
  viewportMeta.setAttribute('name', 'viewport')
  viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1')
  document.head.appendChild(viewportMeta)

  // Initialize authentication first to restore session state
  console.log('App.vue: Checking authentication session...')
  await authStore.checkSession()
  console.log('App.vue: Authentication session checked, isAuthenticated:', authStore.isAuthenticated)

  // Initialize global services after authentication
  console.log('App.vue: Initializing global services...')
  await globalServicesStore.initializeAllServices()
  console.log('App.vue: Global services initialized')

  // Initialize messaging store if user is authenticated
  if (authStore.isAuthenticated) {
    try {
      await messagingStore.initializeMessaging()
      const count = await messagingStore.getUnreadCount()
      console.log('App.vue: Initialized messaging store, unread count:', count)

      // Initialize activity notifications
      await activityNotificationsStore.initialize()
      console.log('App.vue: Initialized activity notifications store')

      // Add a global click event listener to mark notifications as viewed when clicked
      document.addEventListener('click', (event) => {
        // Check if the clicked element is a notification or contains a notification
        const target = event.target as HTMLElement;
        const notificationElement = target.closest('.notification-badge, .q-badge');

        if (notificationElement) {
          console.log('App.vue: Notification clicked, marking as viewed');
          // Mark both types of notifications as viewed
          activityNotificationsStore.markConnectionRequestsAsViewed();
          activityNotificationsStore.markActivitiesAsRead();
        }
      })
    } catch (error) {
      console.error('Error initializing messaging store:', error)
    }
  }
})

// Clean up messaging when component is unmounted
onUnmounted(() => {
  if (authStore.isAuthenticated) {
    messagingStore.cleanupMessaging()
  }
})
</script>

<template>
  <NotificationList />
  <router-view />
</template>

<style>
#app {
  min-height: 100vh;
  font-family: Arial, sans-serif;
}
</style>
