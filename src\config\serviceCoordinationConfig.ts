/**
 * Service Coordination Configuration
 * 
 * Environment-specific configurations for the service coordination plugin
 * to optimize performance and behavior for different deployment environments.
 */

import { ServiceCoordinationConfig } from '../plugins/serviceCoordination';

/**
 * Development environment configuration
 * - Enhanced debugging and monitoring
 * - Faster health checks for development feedback
 * - Detailed logging and dev tools
 */
export const developmentConfig: ServiceCoordinationConfig = {
  enableAutoInitialization: true,
  enableHealthMonitoring: false, // DISABLED - Unnecessary in development
  enableErrorRecovery: true,
  enableDevTools: true,
  healthCheckInterval: 300000, // 5 minutes - Much slower to reduce load
  maxRetryAttempts: 3, // Reduced retry attempts
  retryDelay: 2000 // 2 seconds - standard retry delay
};

/**
 * Production environment configuration
 * - Optimized for performance and stability
 * - Conservative health check intervals
 * - Robust error recovery
 */
export const productionConfig: ServiceCoordinationConfig = {
  enableAutoInitialization: true,
  enableHealthMonitoring: true,
  enableErrorRecovery: true,
  enableDevTools: false, // Disabled in production
  healthCheckInterval: 60000, // 1 minute - conservative for production
  maxRetryAttempts: 3, // Standard retry attempts
  retryDelay: 3000 // 3 seconds - conservative retry delay
};

/**
 * Testing environment configuration
 * - Minimal overhead for test performance
 * - Quick initialization and recovery
 * - Reduced logging
 */
export const testingConfig: ServiceCoordinationConfig = {
  enableAutoInitialization: true,
  enableHealthMonitoring: false, // Disabled for test performance
  enableErrorRecovery: true,
  enableDevTools: false,
  healthCheckInterval: 30000, // 30 seconds
  maxRetryAttempts: 2, // Minimal retries for test speed
  retryDelay: 500 // 500ms - fast retries for tests
};

/**
 * Staging environment configuration
 * - Production-like but with enhanced monitoring
 * - Balanced between development and production
 */
export const stagingConfig: ServiceCoordinationConfig = {
  enableAutoInitialization: true,
  enableHealthMonitoring: true,
  enableErrorRecovery: true,
  enableDevTools: true, // Enabled for staging debugging
  healthCheckInterval: 30000, // 30 seconds
  maxRetryAttempts: 4, // More retries than production for debugging
  retryDelay: 2000 // 2 seconds
};

/**
 * Get configuration based on current environment
 */
export function getServiceCoordinationConfig(): ServiceCoordinationConfig {
  const env = import.meta.env.MODE || 'development';
  const customEnv = import.meta.env.VITE_APP_ENV;

  // Check for custom environment variable first
  if (customEnv === 'staging') {
    console.log('🔧 Using staging service coordination config');
    return stagingConfig;
  }

  // Use NODE_ENV for standard environments
  switch (env) {
    case 'production':
      console.log('🚀 Using production service coordination config');
      return productionConfig;
    
    case 'test':
      console.log('🧪 Using testing service coordination config');
      return testingConfig;
    
    case 'development':
    default:
      console.log('🔧 Using development service coordination config');
      return developmentConfig;
  }
}

/**
 * Advanced configuration options for specific deployment scenarios
 */
export const advancedConfigs = {
  /**
   * High-performance configuration for applications with heavy service usage
   */
  highPerformance: {
    ...productionConfig,
    healthCheckInterval: 120000, // 2 minutes
    maxRetryAttempts: 2, // Minimal retries for performance
    retryDelay: 5000 // 5 seconds
  },

  /**
   * High-availability configuration for critical applications
   */
  highAvailability: {
    ...productionConfig,
    healthCheckInterval: 10000, // 10 seconds - frequent checks
    maxRetryAttempts: 5, // More retries for availability
    retryDelay: 1000, // 1 second - fast recovery
    enableErrorRecovery: true
  },

  /**
   * Minimal configuration for lightweight applications
   */
  minimal: {
    enableAutoInitialization: true,
    enableHealthMonitoring: false,
    enableErrorRecovery: false,
    enableDevTools: false,
    maxRetryAttempts: 1,
    retryDelay: 2000
  },

  /**
   * Debug configuration for troubleshooting
   */
  debug: {
    ...developmentConfig,
    healthCheckInterval: 5000, // 5 seconds - very frequent
    maxRetryAttempts: 10, // Many retries for debugging
    retryDelay: 500, // 500ms - fast retries
    enableDevTools: true
  }
};

/**
 * Configuration validation
 */
export function validateServiceCoordinationConfig(config: ServiceCoordinationConfig): {
  valid: boolean;
  issues: string[];
} {
  const issues: string[] = [];

  // Validate health check interval
  if (config.healthCheckInterval && config.healthCheckInterval < 1000) {
    issues.push('Health check interval should be at least 1000ms to avoid performance issues');
  }

  // Validate retry attempts
  if (config.maxRetryAttempts && config.maxRetryAttempts > 10) {
    issues.push('Max retry attempts should not exceed 10 to avoid infinite retry loops');
  }

  // Validate retry delay
  if (config.retryDelay && config.retryDelay < 100) {
    issues.push('Retry delay should be at least 100ms to avoid overwhelming services');
  }

  // Validate environment-specific settings
  if (import.meta.env.MODE === 'production' && config.enableDevTools) {
    issues.push('Dev tools should be disabled in production for security and performance');
  }

  return {
    valid: issues.length === 0,
    issues
  };
}

/**
 * Apply configuration overrides from environment variables
 */
export function applyEnvironmentOverrides(config: ServiceCoordinationConfig): ServiceCoordinationConfig {
  const overrides: Partial<ServiceCoordinationConfig> = {};

  // Check for environment variable overrides
  if (import.meta.env.VITE_SERVICE_HEALTH_CHECK_INTERVAL) {
    overrides.healthCheckInterval = parseInt(import.meta.env.VITE_SERVICE_HEALTH_CHECK_INTERVAL);
  }

  if (import.meta.env.VITE_SERVICE_MAX_RETRY_ATTEMPTS) {
    overrides.maxRetryAttempts = parseInt(import.meta.env.VITE_SERVICE_MAX_RETRY_ATTEMPTS);
  }

  if (import.meta.env.VITE_SERVICE_RETRY_DELAY) {
    overrides.retryDelay = parseInt(import.meta.env.VITE_SERVICE_RETRY_DELAY);
  }

  if (import.meta.env.VITE_SERVICE_ENABLE_DEV_TOOLS) {
    overrides.enableDevTools = import.meta.env.VITE_SERVICE_ENABLE_DEV_TOOLS === 'true';
  }

  if (import.meta.env.VITE_SERVICE_ENABLE_HEALTH_MONITORING) {
    overrides.enableHealthMonitoring = import.meta.env.VITE_SERVICE_ENABLE_HEALTH_MONITORING === 'true';
  }

  if (import.meta.env.VITE_SERVICE_ENABLE_ERROR_RECOVERY) {
    overrides.enableErrorRecovery = import.meta.env.VITE_SERVICE_ENABLE_ERROR_RECOVERY === 'true';
  }

  const finalConfig = { ...config, ...overrides };

  // Validate the final configuration
  const validation = validateServiceCoordinationConfig(finalConfig);
  if (!validation.valid) {
    console.warn('Service coordination configuration issues:', validation.issues);
  }

  return finalConfig;
}

/**
 * Get the final configuration with environment overrides applied
 */
export function getFinalServiceCoordinationConfig(): ServiceCoordinationConfig {
  const baseConfig = getServiceCoordinationConfig();
  return applyEnvironmentOverrides(baseConfig);
}
