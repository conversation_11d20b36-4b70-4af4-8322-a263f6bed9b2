/**
 * Service Coordinator
 * 
 * Centralized service initialization and coordination to prevent
 * duplicate initialization and manage service dependencies.
 */

export interface ServiceInitializer {
  (): Promise<void>;
}

export interface ServiceStats {
  initialized: string[];
  initializing: string[];
  failed: string[];
  totalServices: number;
  successRate: number;
}

export enum ServiceNames {
  MESSAGING = 'messaging',
  ACTIVITY_NOTIFICATIONS = 'activityNotifications',
  USER_NOTIFICATIONS = 'userNotifications',
  CONNECTIONS = 'connections',
  PROFILE = 'profile',
  CACHE = 'cache',
  REALTIME = 'realtime'
}

export class ServiceCoordinator {
  private static instance: ServiceCoordinator;
  private initialized = new Set<string>();
  private initializing = new Set<string>();
  private failed = new Set<string>();
  private initPromises = new Map<string, Promise<void>>();
  private timeouts = new Map<string, NodeJS.Timeout>();
  private readonly DEFAULT_TIMEOUT = 30000; // 30 seconds

  static getInstance(): ServiceCoordinator {
    if (!ServiceCoordinator.instance) {
      ServiceCoordinator.instance = new ServiceCoordinator();
    }
    return ServiceCoordinator.instance;
  }

  /**
   * Initialize a service with coordination and deduplication
   */
  async initializeService(
    serviceName: string, 
    initializer: ServiceInitializer,
    timeout: number = this.DEFAULT_TIMEOUT
  ): Promise<void> {
    // If already initialized, return immediately
    if (this.initialized.has(serviceName)) {
      console.log(`ServiceCoordinator: ${serviceName} already initialized`);
      return;
    }

    // If currently initializing, wait for existing promise
    if (this.initializing.has(serviceName)) {
      console.log(`ServiceCoordinator: ${serviceName} already initializing, waiting...`);
      const existingPromise = this.initPromises.get(serviceName);
      if (existingPromise) {
        return existingPromise;
      }
    }

    // If previously failed, clear failure state and retry
    if (this.failed.has(serviceName)) {
      console.log(`ServiceCoordinator: Retrying failed service ${serviceName}`);
      this.failed.delete(serviceName);
    }

    // Mark as initializing
    this.initializing.add(serviceName);
    
    const initPromise = this.executeServiceInitialization(serviceName, initializer, timeout);
    this.initPromises.set(serviceName, initPromise);

    try {
      await initPromise;
    } finally {
      this.initPromises.delete(serviceName);
    }
  }

  private async executeServiceInitialization(
    serviceName: string,
    initializer: ServiceInitializer,
    timeout: number
  ): Promise<void> {
    console.log(`ServiceCoordinator: Initializing ${serviceName}...`);
    
    // Set up timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Service ${serviceName} initialization timed out after ${timeout}ms`));
      }, timeout);
      this.timeouts.set(serviceName, timer);
    });

    try {
      // Race between initialization and timeout
      await Promise.race([
        initializer(),
        timeoutPromise
      ]);

      // Success
      this.initializing.delete(serviceName);
      this.initialized.add(serviceName);
      this.clearTimeout(serviceName);
      
      console.log(`✅ ServiceCoordinator: ${serviceName} initialized successfully`);
      
    } catch (error) {
      // Failure
      this.initializing.delete(serviceName);
      this.failed.add(serviceName);
      this.clearTimeout(serviceName);
      
      console.error(`❌ ServiceCoordinator: Failed to initialize ${serviceName}:`, error);
      throw error;
    }
  }

  private clearTimeout(serviceName: string): void {
    const timer = this.timeouts.get(serviceName);
    if (timer) {
      clearTimeout(timer);
      this.timeouts.delete(serviceName);
    }
  }

  /**
   * Wait for all services to be initialized
   */
  async waitForAllServices(timeout: number = 30000): Promise<boolean> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (this.initializing.size === 0) {
        return true;
      }
      
      // Wait a bit before checking again
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.warn(`ServiceCoordinator: Timeout waiting for services. Still initializing: ${Array.from(this.initializing)}`);
    return false;
  }

  /**
   * Check if a specific service is initialized
   */
  isServiceInitialized(serviceName: string): boolean {
    return this.initialized.has(serviceName);
  }

  /**
   * Check if a specific service is currently initializing
   */
  isServiceInitializing(serviceName: string): boolean {
    return this.initializing.has(serviceName);
  }

  /**
   * Check if a specific service has failed
   */
  isServiceFailed(serviceName: string): boolean {
    return this.failed.has(serviceName);
  }

  /**
   * Get initialization statistics
   */
  getStats(): ServiceStats {
    const totalServices = this.initialized.size + this.initializing.size + this.failed.size;
    const successRate = totalServices > 0 ? (this.initialized.size / totalServices) * 100 : 0;
    
    return {
      initialized: Array.from(this.initialized),
      initializing: Array.from(this.initializing),
      failed: Array.from(this.failed),
      totalServices,
      successRate: Math.round(successRate * 100) / 100
    };
  }

  /**
   * Get current status summary
   */
  getStatus(): string {
    const stats = this.getStats();
    return `Initialized: ${stats.initialized.length}, Initializing: ${stats.initializing.length}, Failed: ${stats.failed.length}`;
  }

  /**
   * Reset a failed service for retry
   */
  resetService(serviceName: string): void {
    this.failed.delete(serviceName);
    this.initialized.delete(serviceName);
    this.initializing.delete(serviceName);
    this.clearTimeout(serviceName);
    console.log(`ServiceCoordinator: Reset service ${serviceName} for retry`);
  }

  /**
   * Reset all services
   */
  resetAll(): void {
    this.initialized.clear();
    this.initializing.clear();
    this.failed.clear();
    this.initPromises.clear();
    
    // Clear all timeouts
    for (const timer of this.timeouts.values()) {
      clearTimeout(timer);
    }
    this.timeouts.clear();
    
    console.log('ServiceCoordinator: All services reset');
  }

  /**
   * Get list of services that need retry
   */
  getFailedServices(): string[] {
    return Array.from(this.failed);
  }

  /**
   * Retry all failed services
   */
  async retryFailedServices(): Promise<void> {
    const failedServices = this.getFailedServices();
    if (failedServices.length === 0) {
      console.log('ServiceCoordinator: No failed services to retry');
      return;
    }

    console.log(`ServiceCoordinator: Retrying ${failedServices.length} failed services`);
    
    for (const serviceName of failedServices) {
      this.resetService(serviceName);
    }
  }
}

// Export singleton instance
export const serviceCoordinator = ServiceCoordinator.getInstance();

// Export service names for convenience
export { ServiceNames as SERVICE_NAMES };
