{"$schema": "http://json-schema.org/draft-07/schema#", "title": "ZbInnovation Authentication Forms Schema (Vue.js + Quasar + Supabase)", "description": "JSON schemas for all authentication-related forms in the ZbInnovation platform using Vue.js 3, Quasar Framework, and Supabase backend", "definitions": {"signInForm": {"type": "object", "title": "Sign In Form", "description": "User authentication form for existing users with Supabase Auth", "properties": {"email": {"type": "string", "format": "email", "title": "Email Address", "description": "User's email address for Supabase authentication", "maxLength": 255, "errorMessages": {"required": "Email is required", "format": "Please enter a valid email address", "maxLength": "Email must be less than 255 characters"}}, "password": {"type": "string", "title": "Password", "description": "User's password for Supabase authentication", "minLength": 6, "maxLength": 128, "errorMessages": {"required": "Password is required", "minLength": "Password must be at least 6 characters", "maxLength": "Password must be less than 128 characters"}}, "rememberMe": {"type": "boolean", "title": "Remember Me", "description": "Keep user signed in for extended period", "default": false}}, "required": ["email", "password"], "additionalProperties": false, "supabaseEndpoint": {"method": "supabase.auth.signInWithPassword", "params": "{email, password}", "response": "{user, session, error}"}, "quasarValidationRules": {"email": ["val => !!val || 'Email is required'", "val => /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(val) || 'Please enter a valid email'"], "password": ["val => !!val || 'Password is required'", "val => val.length >= 6 || 'Password must be at least 6 characters'"]}, "vueComponentMapping": {"component": "UnifiedAuthDialogs.vue", "store": "authStore (Pinia)", "composable": "useUnifiedAuth", "uiFramework": "Quasar Framework", "formComponent": "q-form", "inputComponent": "q-input", "buttonComponent": "q-btn"}}, "signUpForm": {"type": "object", "title": "Sign Up Form", "description": "User registration form for new accounts with Supabase Auth", "properties": {"email": {"type": "string", "format": "email", "title": "Email Address", "description": "User's email address for Supabase account creation", "maxLength": 255, "errorMessages": {"required": "Email is required", "format": "Please enter a valid email address", "maxLength": "Email must be less than 255 characters", "unique": "This email is already registered"}}, "password": {"type": "string", "title": "Password", "description": "User's password for account security", "minLength": 6, "maxLength": 128, "pattern": "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,}$", "errorMessages": {"required": "Password is required", "minLength": "Password must be at least 6 characters", "maxLength": "Password must be less than 128 characters", "pattern": "Password must contain at least one uppercase letter, one lowercase letter, and one number"}}, "confirmPassword": {"type": "string", "title": "Confirm Password", "description": "Password confirmation for verification", "minLength": 6, "maxLength": 128, "errorMessages": {"required": "Please confirm your password", "match": "Passwords do not match"}}, "acceptTerms": {"type": "boolean", "title": "Accept Terms and Conditions", "description": "User agreement to terms of service", "const": true, "errorMessages": {"required": "You must accept the terms and conditions"}}, "marketingConsent": {"type": "boolean", "title": "Marketing Communications", "description": "Opt-in for marketing emails and updates", "default": false}}, "required": ["email", "password", "confirmPassword", "acceptTerms"], "additionalProperties": false, "supabaseEndpoint": {"method": "supabase.auth.signUp", "params": "{email, password, options: {emailRedirectTo}}", "response": "{user, session, error}"}, "quasarValidationRules": {"email": ["val => !!val || 'Email is required'", "val => /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(val) || 'Please enter a valid email'"], "password": ["val => !!val || 'Password is required'", "val => val.length >= 6 || 'Password must be at least 6 characters'"], "confirmPassword": ["val => !!val || 'Please confirm your password'", "val => val === formData.password || 'Passwords do not match'"], "acceptTerms": ["val => val === true || 'You must accept the terms and conditions'"]}, "vueComponentMapping": {"component": "UnifiedAuthDialogs.vue", "store": "authStore (Pinia)", "composable": "useUnifiedAuth", "uiFramework": "Quasar Framework", "formComponent": "q-form", "inputComponent": "q-input", "checkboxComponent": "q-checkbox"}}, "passwordResetForm": {"type": "object", "title": "Password Reset Form", "description": "Password recovery form using Supabase Auth", "properties": {"email": {"type": "string", "format": "email", "title": "Email Address", "description": "Email address associated with the Supabase account", "maxLength": 255, "errorMessages": {"required": "Email is required", "format": "Please enter a valid email address", "notFound": "No account found with this email address"}}}, "required": ["email"], "additionalProperties": false, "supabaseEndpoint": {"method": "supabase.auth.resetPasswordForEmail", "params": "{email, options: {redirectTo}}", "response": "{data, error}"}, "quasarValidationRules": {"email": ["val => !!val || 'Email is required'", "val => /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(val) || 'Please enter a valid email'"]}, "vueComponentMapping": {"component": "PasswordResetDialog.vue", "store": "authStore (Pinia)", "composable": "usePasswordReset", "uiFramework": "Quasar Framework"}}}, "quasarFormStates": {"loading": {"description": "Form is submitting data to Supabase", "quasarElements": {"qBtn": "loading prop set to true", "qForm": "disabled during submission", "qLinearProgress": "shown at top of form"}}, "success": {"description": "Supabase operation successful", "quasarElements": {"qNotify": "success notification displayed", "qDialog": "closes automatically after delay", "qForm": "reset if applicable"}}, "error": {"description": "Supabase operation failed", "quasarElements": {"qNotify": "error notification displayed", "qBanner": "error banner shown in form", "qInput": "error styling applied to invalid fields"}}}, "accessibilityRequirements": {"quasarAccessibility": {"qInput": "aria-label and aria-describedby attributes", "qBtn": "aria-label for screen readers", "qForm": "role='form' and aria-labelledby", "qDialog": "focus trap and escape key handling"}, "keyboardNavigation": {"tabOrder": "logical tab sequence through Quasar components", "enterSubmit": "Enter key submits q-form", "escapeClose": "Escape key closes q-dialog"}}, "supabaseSecurityFeatures": {"authentication": "Supabase Auth with PKCE flow", "rowLevelSecurity": "RLS policies for data protection", "rateLimiting": "Built-in Supabase rate limiting", "emailVerification": "Automatic email verification flow", "sessionManagement": "Secure JWT token handling"}}