<template>
  <q-page class="flex flex-center bg-grey-2">
    <q-card class="login-card q-pa-lg">
      <q-card-section class="text-center">
        <div class="text-h4 q-mb-md">Welcome Back</div>
        <div class="text-subtitle1 text-grey-7">
          Sign in to continue to ZbInnovation
        </div>
      </q-card-section>

      <q-card-section>
        <!-- Check if user is already authenticated -->
        <div v-if="authStore.isAuthenticated" class="text-center q-pa-md">
          <p class="text-body1 q-mb-md">You are already signed in.</p>
          <q-btn
            color="primary"
            label="Go to Dashboard"
            @click="goToDashboard"
            no-caps
          />
        </div>

        <!-- Redirect message for non-authenticated users -->
        <div v-else class="text-center q-pa-md">
          <q-btn
            color="primary"
            label="Sign In"
            @click="redirectToHomeWithSignIn"
            no-caps
            class="q-mr-sm"
          />
          <q-btn
            outline
            color="primary"
            label="Go to Home"
            @click="goToHome"
            no-caps
          />
        </div>

        <!-- Unified Authentication Dialogs -->
        <UnifiedAuthDialogs />
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import UnifiedAuthDialogs from '../components/auth/UnifiedAuthDialogs.vue'
import { triggerSignIn } from '../services/unifiedAuthService'

const router = useRouter()
const authStore = useAuthStore()

const goToDashboard = () => {
  router.push('/dashboard')
}

const goToHome = () => {
  router.push('/')
}

const redirectToHomeWithSignIn = () => {
  router.push('/')
  // Trigger sign in dialog after a short delay to ensure navigation completes
  setTimeout(() => {
    triggerSignIn()
  }, 100)
}
</script>

<style scoped>
.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .login-card {
    width: 90%;
  }
}
</style>
