<!DOCTYPE html>
<html lang="en">
  <head>
  <script>
    // Initialize global variables to prevent "Identifier has already been declared" errors
    window.B = window.B || {};
    window.g = window.g || {};
    // Add any other global variables that might be causing conflicts
  </script>

  <!-- Google Analytics -->
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    // Actual config will be handled by the analytics plugin
  </script>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Primary Meta Tags -->
    <title>Smile-Factory</title>
    <meta name="title" content="Smile-Factory - Fostering Innovation in Zimbabwe">
    <meta name="description" content="Join Smile-Factory, Zimbabwe's premier ecosystem for innovators, investors, and entrepreneurs. Connect, collaborate, and grow with our state-of-the-art physical and virtual community.">
    <meta name="keywords" content="Smile-Factory, Zimbabwe Innovation, Tech Hub, Startup Incubator, Business Innovation, Entrepreneurship, Zimbabwe Tech, Innovation Hub">
    <meta name="author" content="Smile-Factory">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">
    <link rel="canonical" href="https://Smile-Factory.co.zw/">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://Smile-Factory.co.zw/">
    <meta property="og:title" content="Smile-Factory - Fostering Innovation in Zimbabwe">
    <meta property="og:description" content="Join Smile-Factory, Zimbabwe's premier ecosystem for innovators, investors, and entrepreneurs. Connect, collaborate, and grow with our state-of-the-art physical and virtual community.">
    <meta property="og:image" content="https://ext.same-assets.com/4258758033/1285344749.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://Smile-Factory.co.zw/">
    <meta property="twitter:title" content="Smile-Factory - Fostering Innovation in Zimbabwe">
    <meta property="twitter:description" content="Join Smile-Factory, Zimbabwe's premier ecosystem for innovators, investors, and entrepreneurs. Connect, collaborate, and grow with our state-of-the-art physical and virtual community.">
    <meta property="twitter:image" content="https://ext.same-assets.com/4258758033/1285344749.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Icons&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Icons+Outlined&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Icons+Round&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Icons+Sharp&display=swap" rel="stylesheet">
    <script src="https://code.iconify.design/3/3.1.0/iconify.min.js"></script>

    <!-- Organization Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Smile-Factory",
        "description": "Zimbabwe's premier innovation ecosystem fostering collaboration between innovators, investors, and entrepreneurs.",
        "url": "https://Smile-Factory.co.zw",
        "logo": "https://ext.same-assets.com/4258758033/1285344749.png",
        "sameAs": [
          "https://twitter.com/zb_foryou",
          "https://instagram.com/zb_foryou",
          "https://www.linkedin.com/company/zbforyou",
          "https://www.youtube.com/@zb_foryou"
        ],
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "Zimbabwe"
        },
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "+263-8677-002-005",
          "email": "<EMAIL>",
          "contactType": "customer service"
        }
      }
    </script>

    <!-- Website Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "ZbInnovation",
        "url": "https://ZbInnovation.co.zw",
        "description": "Zimbabwe's premier ecosystem for innovators, investors, and entrepreneurs.",
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://ZbInnovation.co.zw/search?q={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      }
    </script>

    <!-- Material Icons Font CSS -->
    <style>
      .material-icons,
      .material-icons-outlined,
      .material-icons-round,
      .material-icons-sharp {
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
      }

      .material-icons {
        font-family: 'Material Icons', sans-serif;
      }

      .material-icons-outlined {
        font-family: 'Material Icons Outlined', sans-serif;
      }

      .material-icons-round {
        font-family: 'Material Icons Round', sans-serif;
      }

      .material-icons-sharp {
        font-family: 'Material Icons Sharp', sans-serif;
      }
    </style>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>

    <!-- Preload Important Assets -->
    <link rel="preload" as="image" href="https://ext.same-assets.com/4258758033/1285344749.png">
  </body>
</html>


