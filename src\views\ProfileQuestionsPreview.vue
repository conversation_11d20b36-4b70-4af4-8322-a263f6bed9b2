<template>
  <q-page padding>
    <div class="q-pa-md">
      <div class="row items-center q-mb-md">
        <div class="col">
          <h1 class="text-h4 q-mb-none">Profile Questions Review</h1>
        </div>
      </div>

      <!-- Temporary Feature Notice -->
      <q-card class="q-mb-lg bg-amber-1 temporary-notice">
        <q-card-section>
          <div class="row items-center">
            <div class="col-auto q-mr-md">
              <q-icon name="construction" size="48px" color="amber-9" />
            </div>
            <div class="col">
              <div class="text-h6 text-amber-9">🚧 Temporary Feature - For Review Only 🚧</div>
              <p class="q-mb-sm">
                This page is a <strong>temporary tool</strong> designed specifically for team members to review and provide feedback on profile questions across different profile types.
              </p>
              <p class="q-mb-none">
                It allows you to explore all question categories, field types, and options that will be presented to users during profile creation. Please share any suggestions for improvements or missing fields with the development team.
              </p>
            </div>
          </div>
        </q-card-section>
        <q-card-actions align="right" class="bg-amber-2">
          <q-btn flat color="amber-9" label="This feature will be removed after review phase" icon="timer" />
        </q-card-actions>
      </q-card>

      <!-- Profile Type Tabs with Icons -->
      <q-tabs
        v-model="activeTab"
        class="text-primary q-mb-md bg-grey-2"
        active-color="primary"
        indicator-color="primary"
        align="justify"
        narrow-indicator
        dense
        outside-arrows
        mobile-arrows
      >
        <q-tab name="investor" label="" no-caps>
          <template v-slot:default>
            <div class="column items-center">
              <unified-icon name="attach_money" size="24px" />
              <div class="q-mt-xs">Investor</div>
            </div>
          </template>
        </q-tab>
        <q-tab name="innovator" label="" no-caps>
          <template v-slot:default>
            <div class="column items-center">
              <unified-icon name="lightbulb" size="24px" />
              <div class="q-mt-xs">Innovator</div>
            </div>
          </template>
        </q-tab>
        <q-tab name="mentor" label="" no-caps>
          <template v-slot:default>
            <div class="column items-center">
              <unified-icon name="school" size="24px" />
              <div class="q-mt-xs">Mentor</div>
            </div>
          </template>
        </q-tab>
        <q-tab name="professional" label="" no-caps>
          <template v-slot:default>
            <div class="column items-center">
              <unified-icon name="work" size="24px" />
              <div class="q-mt-xs">Professional</div>
            </div>
          </template>
        </q-tab>
        <q-tab name="industry_expert" label="" no-caps>
          <template v-slot:default>
            <div class="column items-center">
              <unified-icon name="engineering" size="24px" />
              <div class="q-mt-xs">Industry Expert</div>
            </div>
          </template>
        </q-tab>
        <q-tab name="academic_student" label="" no-caps>
          <template v-slot:default>
            <div class="column items-center">
              <unified-icon name="menu_book" size="24px" />
              <div class="q-mt-xs">Academic Student</div>
            </div>
          </template>
        </q-tab>
        <q-tab name="academic_institution" label="" no-caps>
          <template v-slot:default>
            <div class="column items-center">
              <unified-icon name="account_balance" size="24px" />
              <div class="q-mt-xs">Academic Institution</div>
            </div>
          </template>
        </q-tab>
        <q-tab name="organisation" label="" no-caps>
          <template v-slot:default>
            <div class="column items-center">
              <unified-icon name="business" size="24px" />
              <div class="q-mt-xs">Organisation</div>
            </div>
          </template>
        </q-tab>
      </q-tabs>

      <!-- Tab Panels with Enhanced Stepper -->
      <q-card class="q-mb-lg">
        <q-card-section class="q-pa-none">
          <q-tab-panels v-model="activeTab" animated class="bg-transparent">
            <q-tab-panel
              v-for="tabName in tabNames"
              :key="tabName"
              :name="tabName"
              class="q-pa-none"
            >
              <!-- Profile Type Header -->
              <div class="profile-type-header q-pa-md text-white" :data-tab="tabName">
                <div class="row items-center">
                  <div class="col">
                    <div class="text-h5">{{ formatProfileType(tabName) }} Profile</div>
                    <div class="text-subtitle1">Review all questions for this profile type</div>
                  </div>
                  <div class="col-auto">
                    <unified-icon :name="getProfileTypeIcon(tabName)" size="48px" />
                  </div>
                </div>
              </div>

              <!-- Enhanced Stepper -->
              <q-stepper
                v-model="currentSteps[tabName]"
                vertical
                color="primary"
                animated
                alternative-labels
                header-nav
                class="stepper-container"
                done-icon="check"
              >
                <q-step
                  v-for="(section, sectionIndex) in getUnifiedProfileSections(tabName)"
                  :key="sectionIndex"
                  :name="sectionIndex"
                  :title="section.title"
                  :done="currentSteps[tabName] > sectionIndex"
                  :prefix="(sectionIndex + 1).toString()"
                >
                  <div class="q-pa-md">
                    <div class="text-subtitle1 q-mb-md">{{ section.title }}</div>

                    <ProfileQuestionForm
                      :questions="section.questions"
                      :values="formValues[tabName]"
                      :options="getUnifiedProfileOptions(tabName)"
                      @update="(id, value) => updateFormValue(tabName, id, value)"
                    />
                  </div>

                  <q-stepper-navigation>
                    <q-btn
                      v-if="sectionIndex > 0"
                      flat
                      color="primary"
                      label="Back"
                      @click="currentSteps[tabName] = sectionIndex - 1"
                      class="q-mr-sm"
                    />
                    <q-btn
                      v-if="sectionIndex < getUnifiedProfileSections(tabName).length - 1"
                      color="primary"
                      label="Continue"
                      @click="currentSteps[tabName] = sectionIndex + 1"
                    />
                    <q-btn
                      v-else
                      color="positive"
                      label="Complete"
                      @click="showCompletionMessage(tabName)"
                    />
                  </q-stepper-navigation>
                </q-step>
              </q-stepper>
            </q-tab-panel>
          </q-tab-panels>
        </q-card-section>
      </q-card>

      <!-- Profile Completion Indicator -->
      <q-card class="q-mb-lg">
        <q-card-section>
          <div class="text-h6 q-mb-md">Profile Completion Simulation</div>
          <ProfileCompletionIndicator
            :profile-data="formValues[activeTab]"
            show-missing-fields
            show-steps
            :total-steps="getUnifiedProfileSections(activeTab).length"
            :color="getProfileTypeColor(activeTab)"
          />
        </q-card-section>
      </q-card>
    </div>

    <!-- Completion Message Dialog -->
    <q-dialog v-model="showCompletionDialog">
      <q-card style="min-width: 350px">
        <q-card-section class="row items-center bg-positive text-white">
          <div class="text-h6">Profile Complete</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-lg">
          <p>This is a simulation of completing the {{ formatProfileType(completedProfileType) }} profile.</p>
          <p>In a real scenario, this data would be saved to the database and the user would be redirected to their dashboard.</p>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="OK" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script>
import { ref, reactive, computed } from 'vue';
import ProfileQuestionForm from '@/components/profile/ProfileQuestionForm.vue';
import ProfileCompletionIndicator from '@/components/profile/ProfileCompletionIndicator.vue';
import { useProfileStore } from '@/stores/profile';
import { useGlobalServicesStore } from '@/stores/globalServices';
import UnifiedIcon from '@/components/ui/UnifiedIcon.vue';
import { useQuasar } from 'quasar';
import {
  getUnifiedProfileSections,
  getUnifiedProfileOptions
} from '@/services/profileQuestions';

export default {
  name: 'ProfileQuestionsPreview',
  components: {
    ProfileQuestionForm,
    ProfileCompletionIndicator,
    UnifiedIcon
  },
  setup() {
    const profileStore = useProfileStore();
    const globalServices = useGlobalServicesStore();
    const { calculateCompletion } = globalServices.profileCompletionService;
    const $q = useQuasar();

    const activeTab = ref('investor');
    const tabNames = [
      'investor',
      'innovator',
      'mentor',
      'professional',
      'industry_expert',
      'academic_student',
      'academic_institution',
      'organisation'
    ];

    const currentSteps = reactive(
      Object.fromEntries(tabNames.map(tab => [tab, 0]))
    );

    const formValues = reactive(
      Object.fromEntries(tabNames.map(tab => [tab, {}]))
    );

    // Dialog state
    const showCompletionDialog = ref(false);
    const completedProfileType = ref('');

    // Format profile type for display
    function formatProfileType(type) {
      if (!type) return 'Unknown';

      // Handle special cases
      if (type === 'industry_expert') return 'Industry Expert';
      if (type === 'academic_student') return 'Academic Student';
      if (type === 'academic_institution') return 'Academic Institution';

      // Default formatting: capitalize first letter
      return type.charAt(0).toUpperCase() + type.slice(1);
    }

    // Get icon for profile type
    function getProfileTypeIcon(type) {
      const icons = {
        'investor': 'attach_money',
        'innovator': 'lightbulb',
        'mentor': 'school',
        'professional': 'work',
        'industry_expert': 'engineering',
        'academic_student': 'menu_book',
        'academic_institution': 'account_balance',
        'organisation': 'business'
      };

      return icons[type] || 'person';
    }

    // Get color for profile type
    function getProfileTypeColor(type) {
      const colors = {
        'investor': 'green',
        'innovator': 'purple',
        'mentor': 'blue',
        'professional': 'teal',
        'industry_expert': 'deep-orange',
        'academic_student': 'indigo',
        'academic_institution': 'light-blue',
        'organisation': 'amber'
      };

      return colors[type] || 'primary';
    }

    // Show completion message
    function showCompletionMessage(profileType) {
      completedProfileType.value = profileType;
      showCompletionDialog.value = true;

      // Show notification
      $q.notify({
        type: 'positive',
        message: `${formatProfileType(profileType)} profile completed successfully!`,
        position: 'top',
        timeout: 2000
      });
    }

    // Options are now provided by the unified profile questions

    const updateFormValue = (tabName, id, value) => {
      formValues[tabName][id] = value;

      // Update profile completion if we have a current profile
      if (profileStore.currentProfile) {
        profileStore.updateProfileCompletion();
      }
    };

    return {
      activeTab,
      tabNames,
      currentSteps,
      formValues,
      getUnifiedProfileSections,
      getUnifiedProfileOptions,
      updateFormValue,
      profileStore,
      formatProfileType,
      getProfileTypeIcon,
      getProfileTypeColor,
      showCompletionMessage,
      showCompletionDialog,
      completedProfileType
    };
  }
}
</script>

<style scoped>
.q-stepper {
  max-width: 1000px;
  margin: 0 auto;
}

.profile-type-header {
  background: linear-gradient(135deg, var(--q-primary) 0%, var(--q-secondary) 100%);
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.stepper-container {
  padding: 20px;
}

/* Profile type specific headers */
.profile-type-header[data-tab="investor"] {
  background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
}

.profile-type-header[data-tab="innovator"] {
  background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
}

.profile-type-header[data-tab="mentor"] {
  background: linear-gradient(135deg, #2196F3 0%, #03A9F4 100%);
}

.profile-type-header[data-tab="professional"] {
  background: linear-gradient(135deg, #009688 0%, #4DB6AC 100%);
}

.profile-type-header[data-tab="industry_expert"] {
  background: linear-gradient(135deg, #FF5722 0%, #FF9800 100%);
}

.profile-type-header[data-tab="academic_student"] {
  background: linear-gradient(135deg, #3F51B5 0%, #5C6BC0 100%);
}

.profile-type-header[data-tab="academic_institution"] {
  background: linear-gradient(135deg, #03A9F4 0%, #4FC3F7 100%);
}

.profile-type-header[data-tab="organisation"] {
  background: linear-gradient(135deg, #FFC107 0%, #FFCA28 100%);
}

/* Tab styling */
.q-tab {
  transition: all 0.3s ease;
}

.q-tab:hover {
  background: rgba(0, 0, 0, 0.05);
}

.q-tab__content {
  min-height: 72px;
}

/* Card styling */
.q-card {
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.q-card:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
}

/* Temporary notice styling */
.temporary-notice {
  border: 2px dashed #f0b400;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(240, 180, 0, 0.2);
}

.temporary-notice::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    45deg,
    rgba(255, 193, 7, 0.05),
    rgba(255, 193, 7, 0.05) 10px,
    rgba(255, 193, 7, 0.1) 10px,
    rgba(255, 193, 7, 0.1) 20px
  );
  pointer-events: none;
}
</style>
