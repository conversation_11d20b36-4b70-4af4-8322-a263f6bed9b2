-- Migration: Create news_items table for news ticker functionality
-- Description: Creates the news_items table required by NewsTickerComponent

-- Create news_items table
CREATE TABLE IF NOT EXISTS public.news_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  category VARCHAR(50) NOT NULL DEFAULT 'general',
  priority INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT true,
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_date TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_news_items_is_active ON public.news_items(is_active);
CREATE INDEX IF NOT EXISTS idx_news_items_priority ON public.news_items(priority);
CREATE INDEX IF NOT EXISTS idx_news_items_category ON public.news_items(category);
CREATE INDEX IF NOT EXISTS idx_news_items_created_at ON public.news_items(created_at);
CREATE INDEX IF NOT EXISTS idx_news_items_start_date ON public.news_items(start_date);
CREATE INDEX IF NOT EXISTS idx_news_items_end_date ON public.news_items(end_date);

-- Enable Row Level Security
ALTER TABLE public.news_items ENABLE ROW LEVEL SECURITY;

-- Create policies for news_items
-- Allow everyone to read active news items
CREATE POLICY IF NOT EXISTS "Anyone can view active news items"
  ON public.news_items
  FOR SELECT
  USING (is_active = true AND (start_date IS NULL OR start_date <= NOW()) AND (end_date IS NULL OR end_date >= NOW()));

-- Allow authenticated users to view all news items (for admin purposes)
CREATE POLICY IF NOT EXISTS "Authenticated users can view all news items"
  ON public.news_items
  FOR SELECT
  USING (auth.role() = 'authenticated');

-- Allow service role to manage news items
CREATE POLICY IF NOT EXISTS "Service role can manage news items"
  ON public.news_items
  FOR ALL
  USING (auth.role() = 'service_role');

-- Allow authenticated users to insert news items (for admin functionality)
CREATE POLICY IF NOT EXISTS "Authenticated users can insert news items"
  ON public.news_items
  FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

-- Allow authenticated users to update news items (for admin functionality)
CREATE POLICY IF NOT EXISTS "Authenticated users can update news items"
  ON public.news_items
  FOR UPDATE
  USING (auth.role() = 'authenticated');

-- Allow authenticated users to delete news items (for admin functionality)
CREATE POLICY IF NOT EXISTS "Authenticated users can delete news items"
  ON public.news_items
  FOR DELETE
  USING (auth.role() = 'authenticated');

-- Create trigger for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_news_items_updated_at
    BEFORE UPDATE ON public.news_items
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- News items table is ready for content
-- Add news items through the admin interface or API
('Platform Updates and New Features', 'We have rolled out several new features including enhanced matchmaking, improved messaging, and better profile management tools.', 'updates', 2, true);

-- Grant necessary permissions
GRANT SELECT ON public.news_items TO anon, authenticated;
GRANT ALL ON public.news_items TO service_role;
