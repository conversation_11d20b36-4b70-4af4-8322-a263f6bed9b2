<template>
  <q-card class="featured-marketplace-skeleton">
    <div class="skeleton-container">
      <!-- Image Skeleton -->
      <div class="skeleton-image">
        <q-skeleton height="100%" />
        
        <!-- Badge Skeletons -->
        <div class="absolute-top-left q-ma-sm">
          <q-skeleton type="QChip" />
        </div>
        
        <div class="absolute-top-right q-ma-sm">
          <q-skeleton type="QChip" />
        </div>
      </div>

      <!-- Content Skeleton -->
      <div class="skeleton-content q-pa-md">
        <!-- Title Skeleton -->
        <q-skeleton 
          type="text" 
          width="80%" 
          height="1.2rem"
          class="q-mb-xs"
        />
        
        <!-- Description Skeleton -->
        <q-skeleton 
          type="text" 
          width="100%" 
          height="0.9rem"
          class="q-mb-xs"
        />
        <q-skeleton 
          type="text" 
          width="70%" 
          height="0.9rem"
          class="q-mb-sm"
        />
        
        <!-- Meta Skeleton -->
        <div class="row items-center justify-between q-mt-md">
          <div class="row items-center">
            <q-skeleton 
              type="QIcon" 
              size="1rem"
              class="q-mr-xs"
            />
            <q-skeleton 
              type="text" 
              width="60px" 
              height="0.75rem"
            />
          </div>
          <div class="row items-center">
            <q-skeleton 
              type="QIcon" 
              size="1rem"
              class="q-mr-xs"
            />
            <q-skeleton 
              type="text" 
              width="40px" 
              height="0.75rem"
            />
          </div>
        </div>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
// No props or logic needed for skeleton
</script>

<style scoped>
.featured-marketplace-skeleton {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  height: 320px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.skeleton-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.skeleton-image {
  position: relative;
  flex: 0 0 180px;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .featured-marketplace-skeleton {
    height: 300px;
  }
  
  .skeleton-image {
    flex: 0 0 160px;
  }
}
</style>
