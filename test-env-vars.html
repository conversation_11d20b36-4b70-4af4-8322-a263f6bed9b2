<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Environment Variables</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-form {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background: #0D8A3E;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0a6b31;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .env-var {
            margin: 5px 0;
            padding: 5px;
            background: #f8f9fa;
            border-left: 3px solid #007bff;
        }
        .env-set {
            border-left-color: #28a745;
        }
        .env-not-set {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>Test Environment Variables</h1>
    
    <div class="test-form">
        <h3>Check Environment Variables</h3>
        <p>This will check if the required environment variables are properly set in Supabase.</p>
        
        <button onclick="checkEnvVars()">Check Environment Variables</button>
    </div>
    
    <div id="result" class="result"></div>

    <script type="module">
        // Import Supabase client
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        // Initialize Supabase client
        const supabaseUrl = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        window.checkEnvVars = async function() {
            const resultDiv = document.getElementById('result');
            
            // Show loading
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = 'Checking environment variables...';
            
            try {
                // Call the test-env-vars function
                const { data, error } = await supabase.functions.invoke('test-env-vars', {
                    body: {}
                });
                
                if (error) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Supabase Error: ${JSON.stringify(error, null, 2)}`;
                    return;
                }
                
                if (data && data.success) {
                    resultDiv.className = 'result success';
                    let html = `✅ Environment Variables Check Complete:<br><br>`;
                    
                    for (const [key, value] of Object.entries(data.envVars)) {
                        const isSet = value !== 'NOT SET';
                        const cssClass = isSet ? 'env-set' : 'env-not-set';
                        const icon = isSet ? '✅' : '❌';
                        html += `<div class="env-var ${cssClass}">${icon} <strong>${key}:</strong> ${value}</div>`;
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Function Error: ${data?.error || 'Unknown error occurred'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Network Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
