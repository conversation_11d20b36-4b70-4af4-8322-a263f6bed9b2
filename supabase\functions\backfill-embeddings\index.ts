import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

// Types for backfill operations
interface BackfillRequest {
  table_names?: string[]; // Specific tables to process, or all if not provided
  batch_size?: number; // Number of records to process per batch
  force_regenerate?: boolean; // Force regeneration even if embeddings exist
  dry_run?: boolean; // Just count records without processing
}

interface BackfillResponse {
  success: boolean;
  tables_processed: {
    table_name: string;
    records_found: number;
    records_processed: number;
    embeddings_generated: number;
    errors: string[];
    processing_time_ms: number;
  }[];
  total_embeddings_generated: number;
  total_processing_time_ms: number;
  errors: string[];
}

// Configuration for all tables that support embeddings
const EMBEDDING_TABLES = [
  'innovator_profiles',
  'investor_profiles', 
  'mentor_profiles',
  'professional_profiles',
  'industry_expert_profiles',
  'academic_student_profiles',
  'academic_institution_profiles',
  'organisation_profiles',
  'posts'
];

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Get records that need embedding generation from a table
 */
async function getRecordsNeedingEmbeddings(
  tableName: string,
  batchSize: number = 10,
  forceRegenerate: boolean = false
): Promise<{ records: any[]; total_count: number }> {
  try {
    let query = supabase.from(tableName).select('id, updated_at, embeddings_generated_at, created_at');

    if (!forceRegenerate) {
      // Only get records where embeddings haven't been generated or are outdated
      query = query.or('embeddings_generated_at.is.null,updated_at.gt.embeddings_generated_at');
    }

    // Get total count first
    const { count, error: countError } = await query.select('*', { count: 'exact', head: true });
    
    if (countError) {
      throw new Error(`Failed to count records in ${tableName}: ${countError.message}`);
    }

    // Get the actual records
    const { data: records, error } = await query.limit(batchSize);

    if (error) {
      throw new Error(`Failed to fetch records from ${tableName}: ${error.message}`);
    }

    return {
      records: records || [],
      total_count: count || 0
    };

  } catch (error) {
    console.error(`Error getting records from ${tableName}:`, error);
    throw error;
  }
}

/**
 * Call the generate-embeddings function for a specific record
 */
async function generateEmbeddingsForRecord(
  tableName: string,
  recordId: string,
  forceRegenerate: boolean = false
): Promise<{ embeddings_generated: number; errors: string[] }> {
  try {
    const { data, error } = await supabase.functions.invoke('generate-embeddings', {
      body: {
        table_name: tableName,
        record_id: recordId,
        force_regenerate: forceRegenerate
      }
    });

    if (error) {
      throw new Error(`Failed to generate embeddings: ${error.message}`);
    }

    return {
      embeddings_generated: data.embeddings_generated || 0,
      errors: data.errors || []
    };

  } catch (error) {
    return {
      embeddings_generated: 0,
      errors: [error.message]
    };
  }
}

/**
 * Process embeddings for a single table
 */
async function processTableEmbeddings(
  tableName: string,
  batchSize: number = 10,
  forceRegenerate: boolean = false,
  dryRun: boolean = false
): Promise<{
  records_found: number;
  records_processed: number;
  embeddings_generated: number;
  errors: string[];
  processing_time_ms: number;
}> {
  const startTime = Date.now();
  const errors: string[] = [];
  let recordsProcessed = 0;
  let totalEmbeddingsGenerated = 0;

  try {
    console.log(`Processing table: ${tableName}`);

    // Get records that need embedding generation
    const { records, total_count } = await getRecordsNeedingEmbeddings(
      tableName, 
      batchSize, 
      forceRegenerate
    );

    console.log(`Found ${records.length} records to process in ${tableName} (total needing processing: ${total_count})`);

    if (dryRun) {
      return {
        records_found: total_count,
        records_processed: 0,
        embeddings_generated: 0,
        errors: [],
        processing_time_ms: Date.now() - startTime
      };
    }

    // Process each record
    for (const record of records) {
      try {
        console.log(`Processing ${tableName} record: ${record.id}`);
        
        const result = await generateEmbeddingsForRecord(
          tableName,
          record.id,
          forceRegenerate
        );

        recordsProcessed++;
        totalEmbeddingsGenerated += result.embeddings_generated;
        
        if (result.errors.length > 0) {
          errors.push(...result.errors.map(err => `${tableName}:${record.id} - ${err}`));
        }

        // Add a small delay to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        const errorMsg = `Failed to process ${tableName}:${record.id}: ${error.message}`;
        errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    const processingTime = Date.now() - startTime;
    
    console.log(`Completed ${tableName}: ${recordsProcessed} records processed, ${totalEmbeddingsGenerated} embeddings generated in ${processingTime}ms`);

    return {
      records_found: total_count,
      records_processed: recordsProcessed,
      embeddings_generated: totalEmbeddingsGenerated,
      errors,
      processing_time_ms: processingTime
    };

  } catch (error) {
    const errorMsg = `Failed to process table ${tableName}: ${error.message}`;
    errors.push(errorMsg);
    console.error(errorMsg);

    return {
      records_found: 0,
      records_processed: 0,
      embeddings_generated: 0,
      errors,
      processing_time_ms: Date.now() - startTime
    };
  }
}

/**
 * Get embedding generation statistics
 */
async function getEmbeddingStats(): Promise<{
  total_records: number;
  records_with_embeddings: number;
  records_needing_embeddings: number;
  by_table: { [table: string]: { total: number; with_embeddings: number; needs_embeddings: number } };
}> {
  const stats = {
    total_records: 0,
    records_with_embeddings: 0,
    records_needing_embeddings: 0,
    by_table: {} as { [table: string]: { total: number; with_embeddings: number; needs_embeddings: number } }
  };

  for (const tableName of EMBEDDING_TABLES) {
    try {
      // Get total count
      const { count: totalCount } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });

      // Get count with embeddings
      const { count: withEmbeddings } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true })
        .not('embeddings_generated_at', 'is', null);

      // Get count needing embeddings
      const { count: needsEmbeddings } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true })
        .or('embeddings_generated_at.is.null,updated_at.gt.embeddings_generated_at');

      const tableStats = {
        total: totalCount || 0,
        with_embeddings: withEmbeddings || 0,
        needs_embeddings: needsEmbeddings || 0
      };

      stats.by_table[tableName] = tableStats;
      stats.total_records += tableStats.total;
      stats.records_with_embeddings += tableStats.with_embeddings;
      stats.records_needing_embeddings += tableStats.needs_embeddings;

    } catch (error) {
      console.error(`Error getting stats for ${tableName}:`, error);
      stats.by_table[tableName] = { total: 0, with_embeddings: 0, needs_embeddings: 0 };
    }
  }

  return stats;
}

/**
 * Main Edge Function handler
 */
Deno.serve(async (req: Request) => {
  const startTime = Date.now();
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });\n  }\n\n  // Handle GET request for statistics\n  if (req.method === 'GET') {\n    try {\n      const stats = await getEmbeddingStats();\n      \n      return new Response(\n        JSON.stringify({\n          success: true,\n          statistics: stats,\n          timestamp: new Date().toISOString()\n        }),\n        {\n          status: 200,\n          headers: {\n            'Content-Type': 'application/json',\n            'Access-Control-Allow-Origin': '*',\n          },\n        }\n      );\n    } catch (error) {\n      return new Response(\n        JSON.stringify({\n          success: false,\n          error: error.message\n        }),\n        {\n          status: 500,\n          headers: {\n            'Content-Type': 'application/json',\n            'Access-Control-Allow-Origin': '*',\n          },\n        }\n      );\n    }\n  }\n\n  if (req.method !== 'POST') {\n    return new Response(\n      JSON.stringify({ error: 'Method not allowed' }),\n      { \n        status: 405,\n        headers: {\n          'Content-Type': 'application/json',\n          'Access-Control-Allow-Origin': '*',\n        }\n      }\n    );\n  }\n\n  try {\n    const body = await req.json() as BackfillRequest;\n    const { \n      table_names = EMBEDDING_TABLES, \n      batch_size = 5, // Smaller batch size for backfill to avoid timeouts\n      force_regenerate = false,\n      dry_run = false\n    } = body;\n\n    console.log(`Starting embedding backfill for tables: ${table_names.join(', ')}`);\n    console.log(`Batch size: ${batch_size}, Force regenerate: ${force_regenerate}, Dry run: ${dry_run}`);\n\n    const tablesProcessed = [];\n    let totalEmbeddingsGenerated = 0;\n    const allErrors: string[] = [];\n\n    // Process each table\n    for (const tableName of table_names) {\n      if (!EMBEDDING_TABLES.includes(tableName)) {\n        const error = `Table ${tableName} is not configured for embeddings`;\n        allErrors.push(error);\n        continue;\n      }\n\n      try {\n        const result = await processTableEmbeddings(\n          tableName,\n          batch_size,\n          force_regenerate,\n          dry_run\n        );\n\n        tablesProcessed.push({\n          table_name: tableName,\n          ...result\n        });\n\n        totalEmbeddingsGenerated += result.embeddings_generated;\n        allErrors.push(...result.errors);\n\n      } catch (error) {\n        const errorMsg = `Failed to process table ${tableName}: ${error.message}`;\n        allErrors.push(errorMsg);\n        \n        tablesProcessed.push({\n          table_name: tableName,\n          records_found: 0,\n          records_processed: 0,\n          embeddings_generated: 0,\n          errors: [errorMsg],\n          processing_time_ms: 0\n        });\n      }\n    }\n\n    const totalProcessingTime = Date.now() - startTime;\n\n    const response: BackfillResponse = {\n      success: allErrors.length === 0,\n      tables_processed: tablesProcessed,\n      total_embeddings_generated: totalEmbeddingsGenerated,\n      total_processing_time_ms: totalProcessingTime,\n      errors: allErrors\n    };\n\n    console.log(`Backfill completed:`, {\n      tables: tablesProcessed.length,\n      total_embeddings: totalEmbeddingsGenerated,\n      total_time: totalProcessingTime,\n      errors: allErrors.length\n    });\n\n    return new Response(\n      JSON.stringify(response),\n      {\n        status: 200,\n        headers: {\n          'Content-Type': 'application/json',\n          'Access-Control-Allow-Origin': '*',\n        },\n      }\n    );\n\n  } catch (error) {\n    console.error('Backfill function error:', error);\n    \n    const totalProcessingTime = Date.now() - startTime;\n    \n    return new Response(\n      JSON.stringify({\n        success: false,\n        tables_processed: [],\n        total_embeddings_generated: 0,\n        total_processing_time_ms: totalProcessingTime,\n        errors: [error.message]\n      }),\n      {\n        status: 500,\n        headers: {\n          'Content-Type': 'application/json',\n          'Access-Control-Allow-Origin': '*',\n        },\n      }\n    );\n  }\n});
