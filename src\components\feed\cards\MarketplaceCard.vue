<template>
  <q-card class="marketplace-card">
    <!-- Marketplace Categories at the top -->
    <div class="q-pa-sm bg-grey-2 row items-center">
      <q-badge
        color="teal"
        class="q-mr-sm"
        size="lg"
      >
        Marketplace
      </q-badge>
      <q-badge
        v-if="listing.type"
        :color="getListingTypeColor(listing.type)"
        class="q-mr-sm"
        size="lg"
      >
        {{ listing.type }}
      </q-badge>
      <q-badge
        v-if="listing.category && listing.category !== listing.type"
        color="green-7"
        class="q-mr-sm"
        size="lg"
        icon="category"
      >
        {{ listing.category }}
      </q-badge>
    </div>

    <q-img :src="listing.image" :ratio="16/9">
    </q-img>

    <q-card-section>
      <div class="text-h6">{{ listing.title }}</div>

      <div v-if="listing.price" class="row items-center q-mb-sm">
        <q-icon name="attach_money" size="xs" class="q-mr-xs" />
        <span class="text-subtitle2 text-weight-bold">{{ listing.price }}</span>
      </div>

      <div v-if="listing.location" class="row items-center q-mb-sm">
        <q-icon name="location_on" size="xs" class="q-mr-xs" />
        <span class="text-caption">{{ listing.location }}</span>
      </div>

      <div v-if="listing.seller" class="row items-center q-mb-sm">
        <q-icon name="person" size="xs" class="q-mr-xs" />
        <span class="text-caption">{{ listing.seller }}</span>
      </div>

      <p class="text-body2">{{ truncatedDescription }}</p>

      <!-- Tags -->
      <div v-if="listingTags.length" class="q-mt-sm">
        <div class="row q-gutter-xs">
          <q-chip
            v-for="(tag, index) in listingTags.slice(0, 3)"
            :key="index"
            dense
            size="sm"
            color="grey-3"
            text-color="grey-8"
          >
            #{{ tag }}
          </q-chip>
          <q-chip
            v-if="listingTags.length > 3"
            dense
            size="sm"
            color="grey-3"
            text-color="grey-8"
          >
            +{{ listingTags.length - 3 }} more
          </q-chip>
        </div>
      </div>
    </q-card-section>

    <q-card-actions>
      <interaction-buttons
        :content-id="listing.id"
        content-type="post"
        :content-data="getContentData()"
        :is-saved="isFavorite"
        :is-liked="listing.isLiked || false"
        :likes-count="listing.likesCount || listing.likes || 0"
        :comments-count="listing.commentsCount || listing.comments_count || listing.comments || 0"
        :show-comments="showComments"
        :show-contact="true"
        :show-view-details="true"
        size="md"
        @comment="toggleComments"
        @share="handleShare"
        @save="handleFavorite"
        @view-details="handleLearnMore"
        @contact="handleContact"
      />
    </q-card-actions>

    <!-- Comments Section -->
    <q-card-section v-if="showComments">
      <q-separator class="q-my-md" />
      <div class="text-h6 q-mb-md">Comments</div>

      <div v-if="loadingComments" class="text-center q-pa-sm">
        <q-spinner color="primary" size="2em" />
        <p class="q-ma-none">Loading comments...</p>
      </div>

      <div v-else-if="!commentsData.length" class="text-center q-pa-sm">
        <p class="q-ma-none">No comments yet. Be the first to comment!</p>
      </div>

      <div v-else>
        <q-list>
          <q-item v-for="comment in commentsData" :key="comment.id" class="q-py-sm">
            <q-item-section avatar top>
              <user-avatar
                :name="comment.author"
                :email="comment.email"
                :avatar-url="comment.avatar"
                :user-id="comment.authorId"
                size="32px"
                @click="comment.authorId && navigateToUserProfile(comment.authorId)"
              />
            </q-item-section>
            <q-item-section>
              <q-item-label class="text-weight-bold">{{ comment.author }}</q-item-label>
              <q-item-label caption>{{ comment.date }}</q-item-label>
              <div class="q-mt-xs">{{ comment.content }}</div>
            </q-item-section>
          </q-item>
        </q-list>
      </div>

      <!-- Comment Form -->
      <div class="q-mt-md">
        <div class="row items-center">
          <user-avatar
            name="Current User"
            size="32px"
            class="q-mr-sm"
            :clickable="false"
          />
          <q-input
            v-model="newComment"
            dense
            outlined
            placeholder="Write a comment..."
            class="col"
            @keyup.enter="!submittingComment && submitComment()"
          >
            <template v-slot:after>
              <q-btn
                round
                dense
                flat
                color="primary"
                icon="send"
                @click="submitComment"
                :disable="!newComment.trim() || submittingComment"
                :loading="submittingComment"
              />
            </template>
          </q-input>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import InteractionButtons from '../../common/InteractionButtons.vue';
import UserAvatar from '../../common/UserAvatar.vue';
import { truncateText, stripHtml } from '../../../utils/textUtils';
import { useUserInteractionsStore } from '../../../stores/userInteractions';
import { useContentInteractions } from '../../../composables/useContentInteractions';

const props = defineProps({
  listing: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['view', 'share', 'favorite', 'contact', 'comment']);
const router = useRouter();
const userInteractionsStore = useUserInteractionsStore();
const contentInteractions = useContentInteractions();

// Comment state
const showComments = ref(false);
const newComment = ref('');
const loadingComments = ref(false);
const submittingComment = ref(false);
const commentsData = ref([]);

// Check if this listing is favorited by the user
const isFavorite = computed(() => {
  return userInteractionsStore.isPostSaved(props.listing.id);
});

// Truncate listing description for feed view
const truncatedDescription = computed(() => {
  let description = props.listing.description || '';

  // Extract content from JSON if needed
  if (typeof description === 'string' && (description.startsWith('{') || description.startsWith('['))) {
    try {
      const parsedContent = JSON.parse(description);

      // Handle different JSON structures
      if (parsedContent.description) {
        description = parsedContent.description;
      } else if (parsedContent.marketplaceDetails && parsedContent.marketplaceDetails.description) {
        description = parsedContent.marketplaceDetails.description;
      } else if (parsedContent.content) {
        description = parsedContent.content;
      } else if (typeof parsedContent === 'object') {
        // If it's an object but doesn't have expected fields, stringify it
        description = JSON.stringify(parsedContent);
      }
    } catch (e) {
      // If parsing fails, it's not valid JSON, keep the original content
      console.log('Failed to parse content as JSON:', e);
    }
  }

  // Strip HTML tags if present
  if (/<[a-z][\s\S]*>/i.test(description)) {
    description = stripHtml(description);
  }

  return truncateText(description, 250);
});

// Process listing tags to ensure they're displayed correctly
const listingTags = computed(() => {
  if (!props.listing.tags) return [];

  // If tags is a string (possibly from HTML), convert it to an array
  if (typeof props.listing.tags === 'string') {
    // Try to parse as JSON if it looks like a JSON array
    if (props.listing.tags.startsWith('[') && props.listing.tags.endsWith(']')) {
      try {
        return JSON.parse(props.listing.tags);
      } catch (e) {
        console.log('Failed to parse tags JSON:', e);
        // If parsing fails, split by comma as fallback
        return props.listing.tags.replace(/[\[\]"']/g, '').split(',').map((tag: string) => tag.trim());
      }
    }
    // If it's not JSON-like, split by comma
    return props.listing.tags.split(',').map((tag: string) => tag.trim());
  }

  // If it's already an array, process each item to handle object tags
  return props.listing.tags.map((tag: any) => {
    // Check if the tag is an object with label/value properties (from select components)
    if (typeof tag === 'object' && tag !== null) {
      // Always prioritize the value property for display
      if (tag.value) return tag.value;
      // If no value, but has a label property, use that
      if (tag.label) return tag.label;
      // Otherwise return a generic tag name instead of stringifying
      return 'Tag';
    }
    // If it's a string or number, return as is
    return tag;
  });
});

// Helper function for InteractionButtons component
function getContentData() {
  return {
    id: props.listing.id,
    title: props.listing.title,
    content: props.listing.content,
    description: props.listing.description,
    userId: props.listing.userId || props.listing.sellerId,
    author: props.listing.seller || props.listing.author,
    isSaved: isFavorite.value,
    isFavorite: isFavorite.value
  };
}

// Methods
function handleLearnMore() {
  emit('view', props.listing.id);

  // Navigate to unified post details page
  router.push({ name: 'post-details', params: { id: props.listing.id } });
}

function handleShare() {
  emit('share', props.listing.id);
}

function handleFavorite() {
  emit('favorite', props.listing.id);
}

function handleContact() {
  emit('contact', props.listing.id);
}

// Comment methods using unified system
async function toggleComments() {
  await contentInteractions.toggleComments(
    props.listing.id,
    'post',
    showComments,
    commentsData,
    loadingComments
  );
}

async function submitComment() {
  const success = await contentInteractions.submitComment(
    props.listing.id,
    'post',
    newComment.value,
    commentsData,
    newComment,
    submittingComment
  );

  if (success) {
    // Note: Comment count is already updated by the posts store, no need to duplicate here
    emit('comment', {
      postId: props.listing.id,
      content: newComment.value
    });
  }
}

// Navigation helper
function navigateToUserProfile(userId: string) {
  router.push({ name: 'user-profile', params: { id: userId } });
}

// Helper function to get listing type color
function getListingTypeColor(type: string): string {
  const typeColors: Record<string, string> = {
    'Product': 'blue',
    'Service': 'green',
    'Project': 'purple',
    'Job': 'orange',
    'Equipment': 'teal',
    'Space': 'deep-orange',
    'Investment': 'indigo'
  };

  return typeColors[type] || 'primary';
}
</script>

<style scoped>
.marketplace-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.marketplace-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .marketplace-card {
    margin: 0 8px 16px 8px;
    width: calc(100% - 16px);
  }

  /* Fix row gutter issues */
  .row.q-gutter-xs {
    margin-left: 0;
  }
}
</style>
