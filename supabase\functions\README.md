# Supabase Edge Functions

This directory contains Supabase Edge Functions for the Smile-Factory application.

## Functions

### send-email-verification

This function handles all email sending for the application. It uses SendGrid as the email service provider.

#### Configuration

To use this function, you need to set the following environment variables in your Supabase project:

- `SENDGRID_API_KEY`: Your SendGrid API key
- `SENDGRID_FROM_EMAIL`: The email address to send from (default: <EMAIL>)
- `SENDGRID_FROM_NAME`: The name to display as the sender (default: Smile-Factory)

#### Deployment

To deploy this function, run the following command:

```bash
supabase functions deploy send-email-verification --project-ref your-project-ref
```

#### Usage

The function accepts POST requests with the following JSON body:

```json
{
  "type": "welcome | password_reset | custom",
  "data": {
    "to": "<EMAIL>",
    "firstName": "Optional First Name",
    "lastName": "Optional Last Name",
    "subject": "Required for custom emails",
    "customHtml": "Required for custom emails and password_reset",
    "customText": "Optional plain text version"
  }
}
```

#### Email Types

- **welcome**: Sends a welcome email to a new user
- **password_reset**: Sends a password reset email with a reset link
- **custom**: Sends a custom email with the provided subject and HTML content

## Shared Modules

### _shared/cors.ts

This module provides CORS headers for all Edge Functions.

## Local Development

To run the functions locally, use the Supabase CLI:

```bash
supabase start
supabase functions serve
```

Then you can test the functions using curl or Postman:

```bash
curl -X POST http://localhost:54321/functions/v1/send-email \
  -H "Content-Type: application/json" \
  -d '{"type":"welcome","data":{"to":"<EMAIL>","firstName":"Test"}}'
```
