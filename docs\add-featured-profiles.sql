-- Add Featured Tags to Existing Profiles
-- This script adds 'featured' tags to selected existing profiles

-- Update 4 existing profiles to be featured
-- Note: Replace the user_id values with actual user IDs from your database

-- Example: Update profiles to add featured tag
-- You should run this after identifying specific user IDs you want to feature

-- Sample update queries (replace with actual user IDs):
/*
UPDATE personal_details 
SET tags = COALESCE(tags, '{}') || ARRAY['featured']
WHERE user_id IN (
  'user-id-1',  -- Replace with actual user ID
  'user-id-2',  -- Replace with actual user ID  
  'user-id-3',  -- Replace with actual user ID
  'user-id-4'   -- Replace with actual user ID
) 
AND NOT ('featured' = ANY(COALESCE(tags, '{}')));
*/

-- Alternative: Select profiles to feature based on criteria
-- This query shows profiles that could be good candidates for featuring:

SELECT 
  user_id,
  profile_name,
  profile_type,
  profile_completion,
  first_name,
  last_name,
  bio,
  tags
FROM personal_details 
WHERE 
  profile_completion >= 70 
  AND profile_state = 'ACTIVE'
  AND profile_visibility = 'public'
  AND NOT ('featured' = ANY(COALESCE(tags, '{}')))
ORDER BY profile_completion DESC, created_at DESC
LIMIT 10;

-- Instructions for manual execution:
-- 1. Run the SELECT query above to identify good candidates
-- 2. Choose 4 profiles with diverse profile_types and high completion
-- 3. Update the UPDATE query with the chosen user_ids
-- 4. Execute the UPDATE query to add featured tags

-- Example of what the final UPDATE should look like:
-- UPDATE personal_details 
-- SET tags = COALESCE(tags, '{}') || ARRAY['featured']
-- WHERE user_id IN (
--   'actual-user-id-1',
--   'actual-user-id-2', 
--   'actual-user-id-3',
--   'actual-user-id-4'
-- ) 
-- AND NOT ('featured' = ANY(COALESCE(tags, '{}')));
