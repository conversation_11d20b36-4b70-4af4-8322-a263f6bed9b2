<template>
  <q-page class="marketplace-listing-page q-pa-md">
    <div class="container">
      <div v-if="loading" class="text-center q-pa-lg">
        <q-spinner color="primary" size="3em" />
        <p>Loading listing...</p>
      </div>

      <div v-else-if="error" class="text-center q-pa-lg text-negative">
        <p>{{ error }}</p>
        <q-btn color="primary" label="Go Back" @click="$router.go(-1)" />
      </div>

      <template v-else-if="listing">
        <div class="row q-col-gutter-md">
          <!-- Left Column - Images -->
          <div class="col-12 col-md-6">
            <q-card flat bordered>
              <q-img
                :src="listing.image"
                :ratio="4/3"
              />

              <!-- Thumbnail Gallery (placeholder) -->
              <q-card-section class="row q-gutter-sm justify-center">
                <q-img
                  v-for="n in 3"
                  :key="n"
                  :src="listing.image"
                  width="80px"
                  height="60px"
                  class="cursor-pointer rounded-borders"
                />
              </q-card-section>
            </q-card>
          </div>

          <!-- Right Column - Details -->
          <div class="col-12 col-md-6">
            <q-card flat bordered>
              <q-card-section>
                <div class="row items-center justify-between">
                  <div>
                    <q-badge :color="getListingTypeColor(listing.type)" class="q-mr-sm">
                      {{ listing.type }}
                    </q-badge>
                    <q-badge color="grey">{{ listing.category }}</q-badge>
                  </div>

                  <div>
                    <q-btn
                      flat
                      round
                      color="grey"
                      :icon="isFavorite ? 'favorite' : 'favorite_border'"
                      @click="handleFavorite"
                    >
                      <q-tooltip>{{ isFavorite ? 'Remove from Favorites' : 'Add to Favorites' }}</q-tooltip>
                    </q-btn>
                    <q-btn flat round color="grey" icon="share" @click="handleShare">
                      <q-tooltip>Share</q-tooltip>
                    </q-btn>
                  </div>
                </div>

                <div class="text-h4 q-mt-md">{{ listing.title }}</div>

                <div v-if="listing.price" class="text-h5 text-primary q-mt-sm">
                  {{ listing.price }}
                </div>

                <q-separator class="q-my-md" />

                <div class="text-subtitle1 q-mb-xs">Description</div>
                <p>{{ listing.description }}</p>

                <div v-if="listing.tags && listing.tags.length" class="q-mt-md">
                  <div class="text-subtitle1 q-mb-xs">Tags</div>
                  <div class="q-gutter-xs">
                    <q-chip
                      v-for="(tag, index) in listing.tags"
                      :key="index"
                      color="grey-3"
                      text-color="grey-8"
                    >
                      {{ tag }}
                    </q-chip>
                  </div>
                </div>

                <div class="row items-center q-mt-md">
                  <q-icon name="location_on" size="sm" class="q-mr-xs" />
                  <span>{{ listing.location }}</span>
                </div>

                <div class="row items-center q-mt-sm">
                  <q-icon name="person" size="sm" class="q-mr-xs" />
                  <span>{{ listing.seller }}</span>
                </div>

                <div class="row q-gutter-md q-mt-xl">
                  <contact-button
                    v-if="listing"
                    :content-id="listing.id"
                    content-type="listing"
                    :content-data="getContentData()"
                    label="Contact Seller"
                    icon="email"
                    class="full-width"
                  />
                  <q-btn outline color="primary" label="View More Listings" icon="list" class="full-width" @click="viewMoreListings" />
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- Related Listings -->
        <div class="q-mt-xl">
          <div class="text-h5 q-mb-md">Similar Listings</div>
          <div class="row q-col-gutter-md">
            <div v-for="(relatedListing, index) in relatedListings" :key="index" class="col-12 col-md-4">
              <q-card class="marketplace-card" @click="viewListing(relatedListing.id)">
                <q-img :src="relatedListing.image" :ratio="16/9">
                  <div class="absolute-bottom text-subtitle2 text-center bg-transparent">
                    <q-badge :color="getListingTypeColor(relatedListing.type)">{{ relatedListing.type }}</q-badge>
                  </div>
                </q-img>
                <q-card-section>
                  <div class="text-h6">{{ relatedListing.title }}</div>
                  <div class="text-subtitle2 text-primary q-mb-sm">{{ relatedListing.price }}</div>
                  <p class="text-body2">{{ relatedListing.description }}</p>
                </q-card-section>
                <q-card-actions align="right">
                  <q-btn flat color="primary" label="Learn More" />
                </q-card-actions>
              </q-card>
            </div>
          </div>
        </div>
      </template>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { supabase } from '../../lib/supabase';
import { usePostsStore } from '../../stores/posts';
import { useContentInteractions } from '../../composables/useContentInteractions';
import { useUserInteractionsStore } from '../../stores/userInteractions';
import ContactButton from '../../components/common/ContactButton.vue';
import MessageDialog from '../../components/messaging/MessageDialog.vue';

const route = useRoute();
const router = useRouter();
const $q = useQuasar();
const listingId = route.params.id as string;
const contentInteractions = useContentInteractions();
const userInteractionsStore = useUserInteractionsStore();

const listing = ref(null);
const loading = ref(true);
const error = ref(null);
const relatedListings = ref([]);

// Check if this listing is favorited by the user
const isFavorite = computed(() => {
  return listing.value ? userInteractionsStore.isPostSaved(listing.value.id) : false;
});

onMounted(async () => {
  try {
    loading.value = true;
    error.value = null;

    // Fetch marketplace listing from database (marketplace items are stored as posts)
    const postsStore = usePostsStore();
    const listingPost = await postsStore.getPostById(Number(listingId));

    if (listingPost && (
      listingPost.postType === 'marketplace' ||
      listingPost.subType === 'marketplace' ||
      listingPost.sub_type === 'marketplace'
    )) {
      // Map post data to marketplace listing format
      listing.value = {
        id: listingPost.id,
        title: listingPost.title || 'Untitled Listing',
        description: listingPost.content || '',
        price: listingPost.price || 0,
        category: listingPost.category || 'General',
        condition: listingPost.condition || 'New',
        location: listingPost.location || 'Not specified',
        images: listingPost.images || [listingPost.featuredImage || listingPost.imageUrl || 'https://placehold.co/600x400/e0e0e0/ffffff?text=Product'],
        seller: {
          name: listingPost.author || 'Unknown Seller',
          avatar: listingPost.authorAvatar || 'https://placehold.co/40x40/e0e0e0/ffffff?text=U',
          rating: 5.0 // TODO: Get actual seller rating
        },
        userId: listingPost.userId,
        createdAt: listingPost.createdAt,
        views: listingPost.views || 0,
        isFavorite: false // TODO: Check if user has favorited this listing
      };

      // Get related listings (same category)
      await postsStore.fetchPosts({
        postType: 'marketplace',
        category: listingPost.category,
        limit: 4
      });
      relatedListings.value = postsStore.posts
        .filter(p => p.id !== listingPost.id)
        .slice(0, 3);
    } else {
      error.value = 'Listing not found';
    }
  } catch (err) {
    console.error('Error fetching listing:', err);
    error.value = 'Failed to load listing';
  } finally {
    loading.value = false;
  }
});

function viewListing(id: number | string) {
  router.push({ name: 'post-details', params: { id } });
}

// Helper function for ContactButton component
function getContentData() {
  if (!listing.value) return {};

  return {
    id: listing.value.id,
    title: listing.value.title,
    content: listing.value.content,
    description: listing.value.description,
    userId: listing.value.userId,
    author: listing.value.seller?.name || listing.value.seller,
    isSaved: isFavorite.value,
    isFavorite: isFavorite.value
  };
}

// Helper function to get listing type color
function getListingTypeColor(type: string): string {
  const typeColors: Record<string, string> = {
    'Product': 'blue',
    'Service': 'green',
    'Project': 'purple',
    'Job': 'orange',
    'Equipment': 'teal',
    'Space': 'deep-orange',
    'Investment': 'indigo'
  };

  return typeColors[type] || 'primary';
}

function viewMoreListings() {
  // Navigate to marketplace with filter for this seller or category
  if (listing.value.category) {
    router.push({
      path: '/virtual-community',
      query: {
        tab: 'marketplace',
        category: listing.value.category
      }
    });
  } else {
    router.push({ path: '/virtual-community', query: { tab: 'marketplace' } });
  }
}

// Button handlers using shared composable
function handleFavorite() {
  if (listing.value) {
    contentInteractions.toggleSaveContent(listing.value.id, 'listing', listing.value);
  }
}

function handleShare() {
  if (listing.value) {
    contentInteractions.shareContent(listing.value.id, 'listing', listing.value);
  }
}
</script>

<style scoped>
.marketplace-listing-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.marketplace-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.marketplace-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
