<template>
  <div class="interaction-buttons">
    <!-- Standard Actions Row -->
    <div class="row q-gutter-sm justify-between">
      <div class="row q-gutter-sm">
        <!-- Like Button -->
        <q-btn
          flat
          dense
          :size="size"
          :color="optimisticLiked ? 'primary' : 'grey'"
          :icon="optimisticLiked ? 'thumb_up' : 'thumb_up_off_alt'"
          :label="optimisticLikesCount"
          @click="handleLike"
          :loading="likeLoading"
          :disable="likeLoading"
          class="like-button"
          :class="{ 'liked': optimisticLiked }"
        >
          <q-tooltip v-if="!likeLoading">
            {{ optimisticLiked ? 'Unlike this post' : 'Like this post' }}
          </q-tooltip>
        </q-btn>
        
        <!-- Comment Button -->
        <q-btn
          flat
          dense
          :size="size"
          :color="(commentsCount && commentsCount > 0) ? 'primary' : 'grey'"
          icon="chat_bubble_outline"
          :label="commentsCount || 0"
          @click="handleComment"
          :loading="commentLoading"
        />
        
        <!-- Share Button -->
        <q-btn
          flat
          dense
          :size="size"
          color="grey"
          icon="share"
          @click="handleShare"
          :loading="shareLoading"
        />
        
        <!-- Save/Bookmark Button -->
        <q-btn
          flat
          dense
          :size="size"
          :color="isSaved ? 'primary' : 'grey'"
          :icon="isSaved ? 'bookmark' : 'bookmark_border'"
          @click="handleSave"
          :loading="saveLoading"
        />
      </div>

      <div class="row q-gutter-sm">
        <!-- View Details Button (only show if not in single view) -->
        <q-btn
          v-if="showViewDetails"
          flat
          dense
          :size="size"
          color="primary"
          icon="visibility"
          label="View Details"
          @click="handleViewDetails"
        />

        <!-- Contact Button (unified for all screen sizes) -->
        <contact-button
          v-if="showContact && shouldShowContactButton"
          :content-id="contentId"
          :content-type="contentType"
          :content-data="contentData"
          :size="size"
          :flat="true"
          :dense="true"
          :label="$q.screen.xs ? '' : undefined"
        />

        <!-- Dynamic CTA Button -->
        <q-btn
          v-if="dynamicCTA && dynamicCTA.label"
          :icon="dynamicCTA.icon"
          :label="dynamicCTA.label"
          @click="handleDynamicCTA"
          :loading="ctaLoading"
          class="zb-btn-small"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useContentInteractions } from '../../composables/useContentInteractions';
import ContactButton from './ContactButton.vue';

export interface DynamicCTA {
  label: string;
  icon?: string;
  color?: string;
  action: string;
}

const props = defineProps({
  // Content identification
  contentId: {
    type: [Number, String],
    required: true
  },
  contentType: {
    type: String,
    required: true,
    validator: (value: string) => ['post', 'listing', 'event', 'group', 'profile'].includes(value)
  },
  contentData: {
    type: Object,
    required: true
  },
  
  // Button states
  isLiked: {
    type: Boolean,
    default: false
  },
  isSaved: {
    type: Boolean,
    default: false
  },
  likesCount: {
    type: Number,
    default: 0
  },
  commentsCount: {
    type: Number,
    default: 0
  },
  
  // Display options
  size: {
    type: String,
    default: 'sm'
  },
  showViewDetails: {
    type: Boolean,
    default: true
  },
  showContact: {
    type: Boolean,
    default: true
  },
  showComments: {
    type: Boolean,
    default: false
  },
  
  // Dynamic CTA
  dynamicCTA: {
    type: Object as () => DynamicCTA | null,
    default: null
  }
});

const emit = defineEmits([
  'comment',
  'share',
  'save',
  'viewDetails',
  'connect',
  'dynamicCTA'
]);

// Loading states
const likeLoading = ref(false);
const shareLoading = ref(false);
const saveLoading = ref(false);
const ctaLoading = ref(false);
const commentLoading = ref(false);

// Optimistic UI state for likes
const optimisticLiked = ref(props.isLiked);
const optimisticLikesCount = ref(props.likesCount || 0);

// Use content interactions composable
const contentInteractions = useContentInteractions();

// Use Quasar for screen size detection
const $q = useQuasar();

// Watch for prop changes to sync optimistic state
watch(() => props.isLiked, (newValue) => {
  optimisticLiked.value = newValue;
}, { immediate: true });

watch(() => props.likesCount, (newValue) => {
  optimisticLikesCount.value = newValue || 0;
}, { immediate: true });

// Computed property for contact button display
const shouldShowContactButton = computed(() => {
  // Only show contact button for specific content types that need it
  const contactableTypes = ['listing', 'marketplace'];

  // Check if this is a marketplace post or listing
  if (contactableTypes.includes(props.contentType)) {
    return true;
  }

  // For posts, only show contact button if it's a marketplace post
  if (props.contentType === 'post' && props.contentData) {
    const postType = props.contentData.postType?.toLowerCase() || '';
    const subType = props.contentData.subType?.toLowerCase() || '';
    return postType === 'marketplace' || subType === 'marketplace';
  }

  return false;
});

// Handlers
async function handleLike() {
  if (likeLoading.value) return; // Prevent double clicks

  likeLoading.value = true;

  // Store original state for rollback
  const originalLiked = optimisticLiked.value;
  const originalCount = optimisticLikesCount.value;

  // Optimistic update - immediately update UI
  optimisticLiked.value = !originalLiked;
  optimisticLikesCount.value = originalLiked ? originalCount - 1 : originalCount + 1;

  try {
    if (props.contentType === 'post' || props.contentType === 'listing') {
      const success = await contentInteractions.likeContent(props.contentId as number);

      if (!success) {
        // Rollback optimistic update on failure
        optimisticLiked.value = originalLiked;
        optimisticLikesCount.value = originalCount;
      }
      // Note: The store will update the actual post data, and our watchers will sync the optimistic state
    }
  } catch (error) {
    // Rollback optimistic update on error
    optimisticLiked.value = originalLiked;
    optimisticLikesCount.value = originalCount;
    console.error('Error in handleLike:', error);
  } finally {
    likeLoading.value = false;
  }
}

function handleComment() {
  commentLoading.value = true;
  try {
    emit('comment', props.contentId);
  } finally {
    // Reset loading state after a short delay to show feedback
    setTimeout(() => {
      commentLoading.value = false;
    }, 300);
  }
}

async function handleShare() {
  shareLoading.value = true;
  try {
    await contentInteractions.shareContent(
      props.contentId,
      props.contentType as any,
      props.contentData
    );
    emit('share', props.contentId);
  } finally {
    shareLoading.value = false;
  }
}

async function handleSave() {
  saveLoading.value = true;
  try {
    await contentInteractions.toggleSaveContent(
      props.contentId,
      props.contentType as any,
      props.contentData
    );
    emit('save', props.contentId);
  } finally {
    saveLoading.value = false;
  }
}

function handleViewDetails() {
  console.log('InteractionButtons: handleViewDetails called');
  console.log('InteractionButtons: Props:', {
    contentId: props.contentId,
    contentType: props.contentType,
    showViewDetails: props.showViewDetails,
    contentData: props.contentData
  });

  try {
    contentInteractions.viewContent(
      props.contentId,
      props.contentType as any,
      props.contentData
    );
    emit('viewDetails', props.contentId);
  } catch (error) {
    console.error('InteractionButtons: Error in handleViewDetails:', error);
  }
}

async function handleDynamicCTA() {
  if (!props.dynamicCTA) return;

  ctaLoading.value = true;
  try {
    // Handle different CTA actions based on the action type
    switch (props.dynamicCTA.action) {
      case 'join':
        await contentInteractions.joinGroup(props.contentId);
        break;
      case 'collaborate':
        await contentInteractions.requestCollaboration(props.contentId);
        break;
      case 'mentorship':
        await contentInteractions.requestMentorship(props.contentId);
        break;
      case 'view':
        // Handle view action by emitting viewDetails
        emit('viewDetails', props.contentId);
        return; // Don't emit dynamicCTA for view actions
      case 'connect':
        // Handle connect action by emitting connect
        emit('connect', props.contentId);
        return; // Don't emit dynamicCTA for connect actions
      default:
        console.warn('Unknown CTA action:', props.dynamicCTA.action);
    }
    emit('dynamicCTA', { contentId: props.contentId, action: props.dynamicCTA.action });
  } finally {
    ctaLoading.value = false;
  }
}
</script>

<style scoped>
.like-button {
  transition: all 0.2s ease;
}

.like-button.liked {
  transform: scale(1.05);
}

.like-button:hover {
  transform: scale(1.1);
}

.like-button.liked:hover {
  transform: scale(1.15);
}
</style>

<style scoped>
.interaction-buttons {
  width: 100%;
}
</style>
