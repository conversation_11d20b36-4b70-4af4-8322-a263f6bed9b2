<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Resend Function</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-form {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background: #0D8A3E;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0a6b31;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .debug-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Debug Resend Function</h1>
    
    <div class="test-form">
        <h3>Debug Test</h3>
        <p>This will test the basic setup and check if RESEND_KEY is configured properly.</p>
        
        <button id="debugBtn">Run Debug Test</button>
    </div>
    
    <div id="result" class="result"></div>

    <script type="module">
        // Import Supabase client
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        // Initialize Supabase client
        const supabaseUrl = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        document.getElementById('debugBtn').addEventListener('click', async function() {\n            const resultDiv = document.getElementById('result');\n            \n            // Show loading\n            resultDiv.style.display = 'block';\n            resultDiv.className = 'result';\n            resultDiv.innerHTML = 'Running debug test...';\n            \n            try {\n                // Call the debug function\n                const { data, error } = await supabase.functions.invoke('debug-resend', {\n                    body: {\n                        test: 'debug',\n                        timestamp: new Date().toISOString()\n                    }\n                });\n                \n                if (error) {\n                    resultDiv.className = 'result error';\n                    resultDiv.innerHTML = `❌ Supabase Error: ${JSON.stringify(error, null, 2)}`;\n                    return;\n                }\n                \n                if (data) {\n                    if (data.success) {\n                        resultDiv.className = 'result success';\n                        resultDiv.innerHTML = '✅ Debug test completed successfully!';\n                    } else {\n                        resultDiv.className = 'result error';\n                        resultDiv.innerHTML = '❌ Debug test failed';\n                    }\n                    \n                    // Show debug output\n                    const debugDiv = document.createElement('div');\n                    debugDiv.className = 'debug-output';\n                    debugDiv.innerHTML = JSON.stringify(data, null, 2);\n                    resultDiv.appendChild(debugDiv);\n                } else {\n                    resultDiv.className = 'result error';\n                    resultDiv.innerHTML = '❌ No data returned from function';\n                }\n            } catch (error) {\n                resultDiv.className = 'result error';\n                resultDiv.innerHTML = `❌ Network Error: ${error.message}`;\n            }\n        });\n    </script>\n</body>\n</html>
