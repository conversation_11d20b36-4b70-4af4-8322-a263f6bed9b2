/**
 * Email Service
 *
 * This service provides a centralized way to send emails using Supabase Edge Functions.
 * It handles all email types and provides a consistent interface for the application.
 */

import { supabase } from '../lib/supabase'

// Types for email requests
export interface EmailRequest {
  type: 'welcome' | 'password_reset' | 'custom' | 'verification' | 'notification'
  data: {
    to: string
    firstName?: string
    lastName?: string
    subject?: string
    customHtml?: string
    customText?: string
    verificationLink?: string
    // Notification-specific data
    notificationType?: 'connection_request' | 'connection_accepted' | 'post_like' | 'post_comment' | 'new_message' | 'community_digest'
    requesterName?: string
    requesterProfileUrl?: string
    postTitle?: string
    postUrl?: string
    likerName?: string
    commenterName?: string
    commentText?: string
    senderName?: string
    messagePreview?: string
    conversationUrl?: string
    digestData?: {
      newPosts: number
      newMembers: number
      topPosts: Array<{ title: string; url: string; likes: number }>
      featuredContent: Array<{ title: string; url: string; type: string }>
    }
  }
}

/**
 * Sends an email using the Supabase Edge Function
 * @param request The email request data
 * @returns A promise that resolves to the API response
 */
export async function sendEmail(request: EmailRequest): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    // Validate email format
    if (!request.data.to || !validateEmail(request.data.to)) {
      console.error('Invalid email address:', request.data.to)
      return { success: false, error: 'Invalid email address' }
    }

    // Validate request based on type
    if (request.type === 'custom' && (!request.data.subject || !request.data.customHtml)) {
      console.error('Invalid custom email request - missing subject or HTML content')
      return { success: false, error: 'Custom emails require a subject and HTML content' }
    }

    console.log('Sending email request to Edge Function:', {
      type: request.type,
      to: request.data.to,
      subject: request.data.subject || 'No subject provided'
    })

    // Choose the appropriate Edge Function based on email type
    let functionName = 'send-email-fixed'

    if (request.type === 'notification') {
      functionName = 'send-notification-email'
    }

    // Try to call the appropriate email function
    let data, error;
    try {
      const result = await supabase.functions.invoke(functionName, {
        body: request.type === 'notification' ? {
          type: request.data.notificationType,
          data: request.data
        } : request,
        method: 'POST'
      });
      data = result.data;
      error = result.error;
    } catch (primaryError) {
      console.error(`${functionName} function failed:`, primaryError);
      error = primaryError;
    }

    // We're not using a fallback for now since we've updated the SendGrid API key
    if (error) {
      console.warn('Email function failed:', error);
      // Log detailed error information
      console.error('Error details:', JSON.stringify(error, null, 2));
      console.error('Error properties:', {
        status: error.status,
        message: error.message,
        name: error.name,
        context: error.context
      });
      return { success: false, error: error.message || 'Failed to send email' }
    }

    console.log('Email sent successfully:', {
      type: request.type,
      to: request.data.to,
      response: data
    })

    return { success: true, message: data?.message || 'Email sent successfully' }
  } catch (error: any) {
    console.error('Unexpected error in email service:', error)
    console.error('Error stack:', error.stack)
    return { success: false, error: error.message || 'An unexpected error occurred' }
  }
}

/**
 * Sends a welcome email to a new user
 * @param email The user's email address
 * @param firstName Optional first name of the user
 * @param lastName Optional last name of the user
 * @returns A promise that resolves to the API response
 */
export async function sendWelcomeEmail(email: string, firstName?: string, lastName?: string): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    // Validate email format
    if (!email || !validateEmail(email)) {
      console.error('Invalid email address:', email)
      return { success: false, error: 'Invalid email address' }
    }

    // Welcome content is now embedded in the verification email template
    // No separate welcome email is sent to avoid duplicates
    console.log('Welcome email will be delivered via verification template for:', email)
    return { success: true, message: 'Welcome content will be delivered in verification email' }
  } catch (error: any) {
    console.error('Unexpected error sending welcome email:', error)
    return { success: false, error: error.message || 'An unexpected error occurred' }
  }
}

/**
 * Sends a password reset email to a user
 * @param email The user's email address
 * @param resetLink The password reset link
 * @param firstName Optional first name of the user
 * @returns A promise that resolves to the API response
 */
export async function sendPasswordResetEmail(email: string, resetLink: string, firstName?: string): Promise<{ success: boolean; message?: string; error?: string }> {
  console.log('Sending password reset email:', { email, resetLink, firstName })
  return sendEmail({
    type: 'password_reset',
    data: {
      to: email,
      firstName,
      customHtml: resetLink,
      subject: 'Reset Your ZbInnovation Password'
    }
  })
}

/**
 * Sends a custom email
 * @param email The recipient's email address
 * @param subject The email subject
 * @param html The HTML content of the email
 * @param text Optional plain text version of the email
 * @returns A promise that resolves to the API response
 */
export async function sendCustomEmail(email: string, subject: string, html: string, text?: string): Promise<{ success: boolean; message?: string; error?: string }> {
  return sendEmail({
    type: 'custom',
    data: {
      to: email,
      subject,
      customHtml: html,
      customText: text
    }
  })
}

/**
 * Sends a verification email to a user
 * @param email The user's email address
 * @param verificationLink The verification link
 * @param firstName Optional first name of the user
 * @returns A promise that resolves to the API response
 */
export async function sendVerificationEmail(email: string, verificationLink: string, firstName?: string): Promise<{ success: boolean; message?: string; error?: string }> {
  return sendEmail({
    type: 'verification',
    data: {
      to: email,
      firstName,
      verificationLink
    }
  })
}

/**
 * Sends a connection request notification email
 * @param email The recipient's email address
 * @param requesterName The name of the person who sent the connection request
 * @param requesterProfileUrl The URL to the requester's profile
 * @param firstName Optional first name of the recipient
 * @returns A promise that resolves to the API response
 */
export async function sendConnectionRequestEmail(
  email: string,
  requesterName: string,
  requesterProfileUrl: string,
  firstName?: string
): Promise<{ success: boolean; message?: string; error?: string }> {
  return sendEmail({
    type: 'notification',
    data: {
      to: email,
      firstName,
      notificationType: 'connection_request',
      requesterName,
      requesterProfileUrl
    }
  })
}

/**
 * Sends a connection accepted notification email
 * @param email The recipient's email address
 * @param accepterName The name of the person who accepted the connection
 * @param accepterProfileUrl The URL to the accepter's profile
 * @param firstName Optional first name of the recipient
 * @returns A promise that resolves to the API response
 */
export async function sendConnectionAcceptedEmail(
  email: string,
  accepterName: string,
  accepterProfileUrl: string,
  firstName?: string
): Promise<{ success: boolean; message?: string; error?: string }> {
  return sendEmail({
    type: 'notification',
    data: {
      to: email,
      firstName,
      notificationType: 'connection_accepted',
      requesterName: accepterName,
      requesterProfileUrl: accepterProfileUrl
    }
  })
}

/**
 * Sends a post like notification email
 * @param email The recipient's email address
 * @param likerName The name of the person who liked the post
 * @param postTitle The title of the post
 * @param postUrl The URL to the post
 * @param firstName Optional first name of the recipient
 * @returns A promise that resolves to the API response
 */
export async function sendPostLikeEmail(
  email: string,
  likerName: string,
  postTitle: string,
  postUrl: string,
  firstName?: string
): Promise<{ success: boolean; message?: string; error?: string }> {
  return sendEmail({
    type: 'notification',
    data: {
      to: email,
      firstName,
      notificationType: 'post_like',
      likerName,
      postTitle,
      postUrl
    }
  })
}

/**
 * Sends a post comment notification email
 * @param email The recipient's email address
 * @param commenterName The name of the person who commented
 * @param postTitle The title of the post
 * @param commentText The comment text
 * @param postUrl The URL to the post
 * @param firstName Optional first name of the recipient
 * @returns A promise that resolves to the API response
 */
export async function sendPostCommentEmail(
  email: string,
  commenterName: string,
  postTitle: string,
  commentText: string,
  postUrl: string,
  firstName?: string
): Promise<{ success: boolean; message?: string; error?: string }> {
  return sendEmail({
    type: 'notification',
    data: {
      to: email,
      firstName,
      notificationType: 'post_comment',
      commenterName,
      postTitle,
      commentText,
      postUrl
    }
  })
}

/**
 * Sends a new message notification email
 * @param email The recipient's email address
 * @param senderName The name of the message sender
 * @param messagePreview A preview of the message
 * @param conversationUrl The URL to the conversation
 * @param firstName Optional first name of the recipient
 * @returns A promise that resolves to the API response
 */
export async function sendNewMessageEmail(
  email: string,
  senderName: string,
  messagePreview: string,
  conversationUrl: string,
  firstName?: string
): Promise<{ success: boolean; message?: string; error?: string }> {
  return sendEmail({
    type: 'notification',
    data: {
      to: email,
      firstName,
      notificationType: 'new_message',
      senderName,
      messagePreview,
      conversationUrl
    }
  })
}

/**
 * Validates an email address format
 * @param email The email address to validate
 * @returns True if the email is valid, false otherwise
 */
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
