// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'
import { SendGridClient } from '../_shared/sendgrid.ts'

const SENDGRID_API_KEY = Deno.env.get('SENDGRID_API_KEY') || ''
const SUPABASE_URL = Deno.env.get('ZB_SUPABASE_URL') || 'https://dpicnvisvxpmgjtbeicf.supabase.co'
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('ZB_SUPABASE_SERVICE_ROLE_KEY') || ''
const SENDER_EMAIL = '<EMAIL>'
const SITE_URL = 'https://Smile-Factory.co.zw'

// Minimum profile completion percentage threshold
const MIN_COMPLETION_THRESHOLD = 70
// Days since last login threshold
const DAYS_SINCE_LOGIN_THRESHOLD = 2

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the service role key
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    // Calculate the date threshold (2 days ago)
    const thresholdDate = new Date()
    thresholdDate.setDate(thresholdDate.getDate() - DAYS_SINCE_LOGIN_THRESHOLD)
    const thresholdDateString = thresholdDate.toISOString()

    // Get users with incomplete profiles who haven't logged in for 2+ days
    const { data: usersToRemind, error: usersError } = await supabase
      .from('personal_details')
      .select('user_id, email, first_name, last_name, profile_completion, last_login')
      .lt('profile_completion', MIN_COMPLETION_THRESHOLD)
      .lt('last_login', thresholdDateString)
      .order('last_login', { ascending: true })

    if (usersError) {
      throw new Error(`Error fetching users: ${usersError.message}`)
    }

    if (!usersToRemind || usersToRemind.length === 0) {
      return new Response(
        JSON.stringify({ success: true, message: 'No users need reminders at this time' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
      )
    }

    // Create SendGrid client
    const sendgrid = new SendGridClient(SENDGRID_API_KEY)

    // Send reminder emails to each user
    const results = await Promise.all(
      usersToRemind.map(async (user) => {
        try {
          // Prepare email content
          const emailContent = `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <title>Complete Your ZB Innovation Hub Profile</title>
              <style>
                body {
                  font-family: Arial, sans-serif;
                  line-height: 1.6;
                  color: #333;
                  margin: 0;
                  padding: 0;
                }
                .container {
                  max-width: 600px;
                  margin: 0 auto;
                  padding: 20px;
                }
                .header {
                  background-color: #0D8A3E;
                  padding: 20px;
                  text-align: center;
                }
                .header h1 {
                  color: white;
                  margin: 0;
                }
                .content {
                  padding: 20px;
                  background-color: #f9f9f9;
                }
                .footer {
                  text-align: center;
                  padding: 20px;
                  font-size: 12px;
                  color: #666;
                }
                .button {
                  display: inline-block;
                  background-color: #0D8A3E;
                  color: white;
                  text-decoration: none;
                  padding: 10px 20px;
                  border-radius: 4px;
                  margin-top: 20px;
                }
                .progress-container {
                  width: 100%;
                  background-color: #e0e0e0;
                  border-radius: 4px;
                  margin: 20px 0;
                }
                .progress-bar {
                  height: 20px;
                  background-color: #a4ca39;
                  border-radius: 4px;
                  width: ${user.profile_completion || 0}%;
                  text-align: center;
                  line-height: 20px;
                  color: white;
                }
              </style>
            </head>
            <body>
              <div class="container">
                <div class="header">
                  <h1>Complete Your Profile</h1>
                </div>
                <div class="content">
                  <p>Hi ${user.email},</p>
                  <p>We noticed that you haven't completed your profile on the ZB Innovation Hub platform.</p>
                  <p>A complete profile helps you connect with other members of the innovation ecosystem and unlock all the features of our platform.</p>

                  <div class="progress-container">
                    <div class="progress-bar">${user.profile_completion || 0}%</div>
                  </div>

                  <p>Your profile is only <strong>${user.profile_completion || 0}%</strong> complete. Take a few minutes to finish setting up your profile to get the most out of our platform.</p>

                  <div style="text-align: center;">
                    <a href="${SITE_URL}/dashboard/profile" class="button">Complete Your Profile Now</a>
                  </div>

                  <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
                  <p>Best regards,<br>The ZB Innovation Hub Team</p>
                </div>
                <div class="footer">
                  <p>© ${new Date().getFullYear()} ZB Innovation Hub. All rights reserved.</p>
                  <p>This email was sent to ${user.email}</p>
                </div>
              </div>
            </body>
            </html>
          `

          // Send the reminder email
          const result = await sendgrid.send({
            to: user.email,
            from: SENDER_EMAIL,
            subject: 'Complete Your ZB Innovation Hub Profile',
            html: emailContent,
          })

          // Update the last_reminder_sent field in the database
          if (result.success) {
            await supabase
              .from('personal_details')
              .update({ last_reminder_sent: new Date().toISOString() })
              .eq('user_id', user.user_id)
          }

          return {
            user_id: user.user_id,
            email: user.email,
            success: result.success,
            error: result.error
          }
        } catch (error) {
          console.error(`Error sending reminder to ${user.email}:`, error)
          return {
            user_id: user.user_id,
            email: user.email,
            success: false,
            error: error.message
          }
        }
      })
    )

    // Count successful and failed emails
    const successful = results.filter(r => r.success).length
    const failed = results.filter(r => !r.success).length

    // Return results
    return new Response(
      JSON.stringify({
        success: true,
        message: `Sent ${successful} reminder emails, ${failed} failed`,
        results
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )

  } catch (error) {
    console.error('Error sending profile reminders:', error)

    return new Response(
      JSON.stringify({ error: error.message || 'Internal server error' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
