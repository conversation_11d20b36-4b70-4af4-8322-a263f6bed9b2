import { supabase } from '@/lib/supabase';
import { ref } from 'vue';

/**
 * Activity Tracking Service
 *
 * This service provides functions for tracking user activities and retrieving activity history.
 * It interacts with the user_activity table in the database.
 */
export function useActivityTrackingService() {
  const isTracking = ref(false);
  const isInitialized = ref(false);

  /**
   * Initialize the activity tracking service
   *
   * @returns Success status
   */
  async function initialize(): Promise<boolean> {
    if (isInitialized.value) {
      console.log('ActivityTrackingService: Already initialized');
      return true;
    }

    try {
      console.log('ActivityTrackingService: Initializing...');

      // Check if user_activity table exists
      const { error: tableCheckError } = await supabase
        .from('user_activity')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      if (tableCheckError) {
        console.warn('ActivityTrackingService: user_activity table has issues or does not exist:', tableCheckError.message);
        // Still mark as initialized to avoid blocking other services
      }

      isInitialized.value = true;
      console.log('ActivityTrackingService: Initialized successfully');
      return true;
    } catch (error) {
      console.error('ActivityTrackingService: Failed to initialize:', error);
      // Still mark as initialized to avoid blocking other services
      isInitialized.value = true;
      return true;
    }
  }

  /**
   * Track a user activity
   *
   * @param activityType The type of activity (e.g., post_create, post_like, post_comment)
   * @param details Additional details about the activity (stored as JSONB)
   * @returns Success status
   */
  async function trackActivity(activityType: string, details: any = {}): Promise<boolean> {
    if (isTracking.value) return false;

    try {
      isTracking.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot track activity: User not authenticated');
        return false;
      }

      console.log(`Tracking activity: ${activityType}`, details);

      // For success story submissions, add to user's posts
      if (activityType === 'success_story_submit' && details.post_id) {
        // Add to user's posts if not already there
        const { data: userPosts, error: postsError } = await supabase
          .from('user_posts')
          .select('*')
          .eq('user_id', user.id)
          .eq('post_id', details.post_id);

        if (postsError) {
          console.error('Error checking user posts:', postsError);
        } else if (!userPosts || userPosts.length === 0) {
          // Add to user's posts
          const { error: insertError } = await supabase
            .from('user_posts')
            .insert({
              user_id: user.id,
              post_id: details.post_id,
              created_at: new Date().toISOString()
            });

          if (insertError) {
            console.error('Error adding to user posts:', insertError);
          }
        }
      }

      // First check if the user_activity table exists
      try {
        const { error: tableCheckError } = await supabase
          .from('user_activity')
          .select('id', { count: 'exact', head: true })
          .limit(1);

        // If the table doesn't exist or has issues, log and return success anyway
        if (tableCheckError) {
          console.warn('The user_activity table has issues or does not exist:', tableCheckError.message);
          return true; // Return success to avoid breaking the main functionality
        }

        // If table exists, try to insert the activity
        const { error } = await supabase.from('user_activity').insert({
          user_id: user.id,
          activity_type: activityType,
          timestamp: new Date().toISOString(),
          details
        });

        if (error) {
          console.error('Error tracking activity:', error);
          // Don't throw, just log and continue
          return true; // Return success to avoid breaking the main functionality
        }
      } catch (tableError) {
        console.error('Error checking user_activity table:', tableError);
        return true; // Return success to avoid breaking the main functionality
      }

      return true;
    } catch (error) {
      console.error('Error in trackActivity:', error);
      return false;
    } finally {
      isTracking.value = false;
    }
  }

  /**
   * Get user activities
   *
   * @param userId The user ID (defaults to current user)
   * @param limit Maximum number of activities to return
   * @param page Page number for pagination
   * @returns Array of user activities
   */
  async function getUserActivities(
    userId?: string,
    limit: number = 10,
    page: number = 1
  ): Promise<any[]> {
    try {
      if (!userId) {
        const { data: { user } } = await supabase.auth.getUser();
        userId = user?.id;
      }

      if (!userId) {
        console.error('Cannot get activities: No user ID provided');
        return [];
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      console.log(`Fetching activities for user ${userId} (page ${page}, limit ${limit})`);

      const { data, error } = await supabase
        .from('user_activity')
        .select('*')
        .eq('user_id', userId)
        .order('timestamp', { ascending: false })
        .range(from, to);

      if (error) {
        console.error('Error fetching user activities:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in getUserActivities:', error);
      return [];
    }
  }

  /**
   * Get activity count by type
   *
   * @param userId The user ID (defaults to current user)
   * @param activityType The activity type to count
   * @returns Count of activities
   */
  async function getActivityCount(
    userId?: string,
    activityType?: string
  ): Promise<number> {
    try {
      if (!userId) {
        const { data: { user } } = await supabase.auth.getUser();
        userId = user?.id;
      }

      if (!userId) {
        console.error('Cannot get activity count: No user ID provided');
        return 0;
      }

      let query = supabase
        .from('user_activity')
        .select('id', { count: 'exact' })
        .eq('user_id', userId);

      if (activityType) {
        query = query.eq('activity_type', activityType);
      }

      const { count, error } = await query;

      if (error) {
        console.error('Error getting activity count:', error);
        throw error;
      }

      return count || 0;
    } catch (error) {
      console.error('Error in getActivityCount:', error);
      return 0;
    }
  }

  return {
    initialize,
    trackActivity,
    getUserActivities,
    getActivityCount,
    isTracking,
    isInitialized
  };
}
