<template>
  <div class="featured-profiles-section">
    <!-- Section Header -->
    <div class="featured-header q-mb-md">
      <div class="text-h6 text-weight-bold">Featured Profiles</div>
      <p class="text-caption text-grey-7 q-mb-none">Connect with leading innovators and experts</p>
    </div>

    <!-- Horizontal Scrollable Container -->
    <div class="featured-scroll-container">
      <div class="featured-scroll-content" ref="scrollContainer">
        <!-- Featured Profiles -->
        <div v-for="profile in featuredProfiles" :key="profile.user_id" class="featured-item">
          <featured-profile-card
            :profile="profile"
            @click="handleProfileClick"
          />
        </div>

        <!-- Loading placeholders -->
        <div v-if="loading" class="featured-item" v-for="n in 4" :key="`loading-${n}`">
          <featured-profile-skeleton />
        </div>

        <!-- Empty state when no featured profiles -->
        <div v-if="!loading && featuredProfiles.length === 0" class="featured-empty">
          <div class="text-center q-pa-lg">
            <q-icon name="people" size="3em" color="grey-5" />
            <p class="text-grey-6 q-mt-md">No featured profiles available</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import FeaturedProfileCard from './FeaturedProfileCard.vue';
import FeaturedProfileSkeleton from './FeaturedProfileSkeleton.vue';
import { useProfileStore } from '../../stores/profileStore';

// Props
interface Props {
  maxItems?: number;
}

const props = withDefaults(defineProps<Props>(), {
  maxItems: 6
});

// Emits
const emit = defineEmits<{
  profileClick: [profile: any];
}>();

// Composables
const router = useRouter();
const profileStore = useProfileStore();

// State
const loading = ref(true);
const scrollContainer = ref<HTMLElement>();

// Computed
const featuredProfiles = computed(() => {
  return profileStore.publicProfiles
    .filter(profile => 
      profile.tags?.includes('featured')
    )
    .slice(0, props.maxItems);
});

// Methods
async function loadFeaturedProfiles() {
  try {
    loading.value = true;
    console.log('Loading featured profiles...');
    
    // Load profiles with featured tags
    await profileStore.loadPublicProfiles(1, 12, {
      tags: ['featured']
    });
    
    console.log('Featured profiles loaded:', featuredProfiles.value.length);
  } catch (error) {
    console.error('Error loading featured profiles:', error);
  } finally {
    loading.value = false;
  }
}

function handleProfileClick(profile: any) {
  emit('profileClick', profile);

  // Navigate to user profile page
  router.push({ name: 'user-profile', params: { id: profile.user_id } });
}

// Lifecycle
onMounted(() => {
  console.log('FeaturedProfilesSection mounted');
  loadFeaturedProfiles();
});
</script>

<style scoped>
.featured-profiles-section {
  margin-bottom: 24px;
}

.featured-header {
  padding: 0 16px;
}

.featured-scroll-container {
  position: relative;
  overflow: hidden;
}

.featured-scroll-content {
  display: flex;
  gap: 16px;
  padding: 0 16px 16px 16px;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.featured-scroll-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.featured-item {
  flex: 0 0 260px;
  min-width: 260px;
}

.featured-empty {
  flex: 1;
  min-width: 100%;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .featured-item {
    flex: 0 0 220px;
    min-width: 220px;
  }
  
  .featured-scroll-content {
    gap: 12px;
    padding: 0 12px 12px 12px;
  }
}

@media (max-width: 480px) {
  .featured-item {
    flex: 0 0 180px;
    min-width: 180px;
  }
}
</style>
