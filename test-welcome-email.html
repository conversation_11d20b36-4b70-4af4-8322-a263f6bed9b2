<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Welcome Email</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-form {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #0D8A3E;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0a6b31;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Test Welcome Email Function</h1>
    
    <div class="test-form">
        <h3>Test Welcome Email Function</h3>
        <p>This will test the new welcome-email function that uses Resend API.</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="firstName">First Name (optional):</label>
                <input type="text" id="firstName" name="firstName" placeholder="John">
            </div>
            
            <button type="submit">Send Test Welcome Email</button>
        </form>
    </div>
    
    <div id="result" class="result"></div>

    <script type="module">
        // Import Supabase client
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        // Initialize Supabase client
        const supabaseUrl = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';
        const supabase = createClient(supabaseUrl, supabaseKey);

        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const firstName = document.getElementById('firstName').value;
            const resultDiv = document.getElementById('result');

            // Show loading
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = 'Sending test email...';

            try {
                // Call the notification function with welcome type
                const { data, error } = await supabase.functions.invoke('send-notification-email', {
                    body: {
                        type: 'welcome',
                        data: {
                            to: email,
                            firstName: firstName || undefined
                        }
                    }
                });

                if (error) {
                    throw error;
                }

                if (data && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Success! Welcome email sent to ${email}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Error: ${data?.error || 'Unknown error occurred'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Network Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
