/**
 * AI Profile Analyzer Service
 *
 * Provides intelligent analysis of user profiles for AI context building,
 * completion guidance, and milestone celebration.
 *
 * IMPORTANT: This service works with the existing profile system and does NOT
 * duplicate profile completion calculation. It enhances the existing data
 * with AI-specific insights and recommendations.
 */

import { supabase } from '@/lib/supabase'

export interface ProfileAnalysis {
  completion_percentage: number
  completion_stage: 'empty' | 'basic' | 'developing' | 'advanced' | 'complete'
  missing_critical_fields: string[]
  missing_optional_fields: string[]
  strengths: string[]
  recommendations: string[]
  next_steps: string[]
  milestone_achieved?: string
  celebration_message?: string
}

export interface ProfileCompletionMilestone {
  percentage: number
  title: string
  description: string
  celebration_message: string
  next_steps: string[]
}

// Define completion milestones
const COMPLETION_MILESTONES: ProfileCompletionMilestone[] = [
  {
    percentage: 25,
    title: "Profile Started! 🎉",
    description: "You've taken the first step in building your innovation profile",
    celebration_message: "Great start! You've begun your journey on ZbInnovation.",
    next_steps: ["Add your bio", "Select your profile type", "Upload a profile picture"]
  },
  {
    percentage: 50,
    title: "Halfway There! 🚀",
    description: "Your profile is taking shape and becoming more discoverable",
    celebration_message: "Excellent progress! You're halfway to a complete profile.",
    next_steps: ["Add your expertise areas", "Include contact information", "Share your goals"]
  },
  {
    percentage: 75,
    title: "Almost Complete! ⭐",
    description: "Your profile is looking professional and comprehensive",
    celebration_message: "Amazing! Your profile is almost complete and looking great.",
    next_steps: ["Add social media links", "Include portfolio items", "Review and publish"]
  },
  {
    percentage: 100,
    title: "Profile Complete! 🎊",
    description: "Your profile is now fully optimized for maximum visibility",
    celebration_message: "Congratulations! Your profile is now complete and ready to attract connections.",
    next_steps: ["Start networking", "Create your first post", "Join relevant groups"]
  }
]

// Profile type specific field requirements based on actual database structure
const PROFILE_FIELD_REQUIREMENTS = {
  innovator: {
    critical: ['first_name', 'last_name', 'email', 'profile_type', 'bio'],
    important: ['phone_number', 'profile_name'],
    optional: ['gender', 'hear_about_us']
  },
  investor: {
    critical: ['first_name', 'last_name', 'email', 'profile_type', 'bio'],
    important: ['phone_number', 'profile_name'],
    optional: ['gender', 'hear_about_us']
  },
  mentor: {
    critical: ['first_name', 'last_name', 'email', 'profile_type', 'bio'],
    important: ['phone_number', 'profile_name'],
    optional: ['gender', 'hear_about_us']
  },
  academic_student: {
    critical: ['first_name', 'last_name', 'email', 'profile_type', 'bio'],
    important: ['phone_number', 'profile_name'],
    optional: ['gender', 'hear_about_us']
  },
  professional: {
    critical: ['first_name', 'last_name', 'email', 'profile_type', 'bio'],
    important: ['phone_number', 'profile_name'],
    optional: ['gender', 'hear_about_us']
  },
  academic_institution: {
    critical: ['first_name', 'last_name', 'email', 'profile_type', 'bio'],
    important: ['phone_number', 'profile_name'],
    optional: ['gender', 'hear_about_us']
  },
  industry_expert: {
    critical: ['first_name', 'last_name', 'email', 'profile_type', 'bio'],
    important: ['phone_number', 'profile_name'],
    optional: ['gender', 'hear_about_us']
  }
}

export class AIProfileAnalyzer {
  /**
   * Analyze a user's profile for AI context and provide recommendations
   * Uses existing profile completion from the database, does not recalculate
   */
  async analyzeProfile(userId: string, profileData?: any): Promise<ProfileAnalysis> {
    try {
      // Get profile data if not provided - use existing personal_details table
      let profile = profileData
      if (!profile) {
        const { data, error } = await supabase
          .from('personal_details')
          .select('*')
          .eq('user_id', userId)
          .single()

        if (error) throw error
        profile = data
      }

      if (!profile) {
        return this.getEmptyProfileAnalysis()
      }

      // Use existing profile completion from database (don't recalculate)
      const completionPercentage = profile.profile_completion || 0
      const profileType = profile.profile_type || 'professional'

      // Determine completion stage based on existing completion
      const completionStage = this.determineCompletionStage(completionPercentage)

      // Generate AI-specific insights and recommendations
      const analysis: ProfileAnalysis = {
        completion_percentage: completionPercentage,
        completion_stage: completionStage,
        missing_critical_fields: this.identifyMissingFields(profile, profileType),
        missing_optional_fields: this.identifyOptionalFields(profile, profileType),
        strengths: this.identifyStrengths(profile),
        recommendations: this.generateRecommendations(profile, completionPercentage),
        next_steps: this.generateNextSteps(profile, completionPercentage, profileType)
      }

      // Check for milestones based on existing completion
      const milestone = this.checkMilestone(completionPercentage)
      if (milestone) {
        analysis.milestone_achieved = milestone.title
        analysis.celebration_message = milestone.celebration_message
      }

      return analysis
    } catch (error) {
      console.error('Error analyzing profile:', error)
      return this.getEmptyProfileAnalysis()
    }
  }

  /**
   * Determine completion stage based on existing completion percentage
   */
  private determineCompletionStage(completionPercentage: number): ProfileAnalysis['completion_stage'] {
    if (completionPercentage === 0) return 'empty'
    if (completionPercentage < 30) return 'basic'
    if (completionPercentage < 60) return 'developing'
    if (completionPercentage < 90) return 'advanced'
    return 'complete'
  }

  /**
   * Identify missing critical fields based on profile type
   */
  private identifyMissingFields(profile: any, profileType: string): string[] {
    const requirements = PROFILE_FIELD_REQUIREMENTS[profileType] || PROFILE_FIELD_REQUIREMENTS.professional
    const criticalFields = requirements.critical || []

    return criticalFields.filter(field => !this.hasValue(profile[field]))
  }

  /**
   * Identify missing optional fields based on profile type
   */
  private identifyOptionalFields(profile: any, profileType: string): string[] {
    const requirements = PROFILE_FIELD_REQUIREMENTS[profileType] || PROFILE_FIELD_REQUIREMENTS.professional
    const optionalFields = [...(requirements.important || []), ...(requirements.optional || [])]

    return optionalFields.filter(field => !this.hasValue(profile[field]))
  }

  /**
   * Check if a field has a meaningful value
   */
  private hasValue(value: any): boolean {
    if (value === null || value === undefined) return false
    if (typeof value === 'string' && value.trim() === '') return false
    if (Array.isArray(value) && value.length === 0) return false
    return true
  }

  /**
   * Identify profile strengths
   */
  private identifyStrengths(profile: any): string[] {
    const strengths: string[] = []

    if (this.hasValue(profile.bio) && profile.bio.length > 100) {
      strengths.push('Comprehensive bio')
    }
    if (this.hasValue(profile.avatar_url)) {
      strengths.push('Profile picture added')
    }
    if (this.hasValue(profile.linkedin)) {
      strengths.push('LinkedIn connected')
    }
    if (this.hasValue(profile.website)) {
      strengths.push('Website included')
    }
    if (profile.is_public) {
      strengths.push('Public profile visibility')
    }

    return strengths
  }

  /**
   * Generate personalized recommendations based on existing profile data
   */
  private generateRecommendations(profile: any, completionPercentage: number): string[] {
    const recommendations: string[] = []

    // Basic profile recommendations
    if (!this.hasValue(profile.first_name) || !this.hasValue(profile.last_name)) {
      recommendations.push('Complete your name to help others identify you')
    }

    if (!this.hasValue(profile.bio) || profile.bio.length < 50) {
      recommendations.push('Add a detailed bio to help others understand your background')
    }

    if (!this.hasValue(profile.phone_number)) {
      recommendations.push('Add contact information to enable direct communication')
    }

    // Completion-based recommendations
    if (completionPercentage < 30) {
      recommendations.push('Complete basic profile information to get started')
    } else if (completionPercentage < 70) {
      recommendations.push('Aim for 70%+ completion to maximize networking opportunities')
    } else if (completionPercentage < 100) {
      recommendations.push('Complete remaining fields to optimize your profile visibility')
    }

    // Profile state recommendations
    if (profile.profile_state === 'DRAFT') {
      recommendations.push('Publish your profile when ready to start networking')
    }

    // Profile type specific recommendations
    if (profile.profile_type && completionPercentage >= 50) {
      recommendations.push(`Explore ${profile.profile_type}-specific features and connections`)
    }

    return recommendations.slice(0, 4) // Limit to 4 recommendations
  }

  /**
   * Generate next steps based on profile state and completion
   */
  private generateNextSteps(profile: any, completionPercentage: number, profileType: string): string[] {
    const nextSteps: string[] = []

    // Completion-based next steps
    if (completionPercentage < 30) {
      nextSteps.push('Add your basic information (name, bio, contact details)')
      if (!profile.profile_type) {
        nextSteps.push('Select your profile type to get personalized guidance')
      }
    } else if (completionPercentage < 70) {
      nextSteps.push('Complete profile-specific information for your role')
      nextSteps.push('Add professional details and goals')
    } else if (completionPercentage < 100) {
      nextSteps.push('Complete remaining optional fields')
      nextSteps.push('Review and optimize your profile content')
    } else {
      nextSteps.push('Start networking and connecting with relevant profiles')
      nextSteps.push('Create your first post to engage with the community')
    }

    // Profile type specific next steps
    if (profileType && completionPercentage >= 30) {
      switch (profileType) {
        case 'innovator':
          nextSteps.push('Showcase your innovation and seek investor connections')
          break
        case 'investor':
          nextSteps.push('Define investment criteria and discover startups')
          break
        case 'mentor':
          nextSteps.push('Highlight expertise areas and connect with mentees')
          break
        case 'professional':
          nextSteps.push('Network with industry peers and explore collaborations')
          break
        case 'academic_student':
          nextSteps.push('Connect with mentors and explore learning opportunities')
          break
      }
    }

    // Profile state specific next steps
    if (profile.profile_state === 'DRAFT' && completionPercentage >= 50) {
      nextSteps.push('Publish your profile to start receiving connections')
    }

    return nextSteps.slice(0, 4) // Limit to 4 next steps
  }

  /**
   * Check if user has achieved a new milestone
   */
  private checkMilestone(completionPercentage: number): ProfileCompletionMilestone | null {
    // Find the highest milestone achieved
    const achievedMilestones = COMPLETION_MILESTONES.filter(
      milestone => completionPercentage >= milestone.percentage
    )

    if (achievedMilestones.length === 0) return null

    // Return the highest milestone
    return achievedMilestones[achievedMilestones.length - 1]
  }

  /**
   * Get default analysis for empty profile
   */
  private getEmptyProfileAnalysis(): ProfileAnalysis {
    return {
      completion_percentage: 0,
      completion_stage: 'empty',
      missing_critical_fields: ['first_name', 'last_name', 'email', 'profile_type'],
      missing_optional_fields: ['bio', 'avatar_url', 'linkedin'],
      strengths: [],
      recommendations: [
        'Start by adding your basic information',
        'Choose your profile type to get personalized guidance',
        'Add a bio to introduce yourself to the community'
      ],
      next_steps: [
        'Complete your name and email',
        'Select your profile type',
        'Write a brief bio about yourself'
      ]
    }
  }

  /**
   * Get milestone information for a specific completion percentage
   */
  getMilestoneInfo(completionPercentage: number): ProfileCompletionMilestone | null {
    return this.checkMilestone(completionPercentage)
  }

  /**
   * Get all available milestones
   */
  getAllMilestones(): ProfileCompletionMilestone[] {
    return COMPLETION_MILESTONES
  }
}

// Export singleton instance
export const aiProfileAnalyzer = new AIProfileAnalyzer()
