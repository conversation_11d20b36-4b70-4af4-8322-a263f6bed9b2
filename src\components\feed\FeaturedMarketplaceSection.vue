<template>
  <div class="featured-marketplace-section">
    <!-- Section Header -->
    <div class="featured-header q-mb-md">
      <div class="text-h6 text-weight-bold">Featured Solutions</div>
      <p class="text-caption text-grey-7 q-mb-none">Discover innovative products and services</p>
    </div>

    <!-- Horizontal Scrollable Container -->
    <div class="featured-scroll-container">
      <div class="featured-scroll-content" ref="scrollContainer">
        <!-- Featured Marketplace Items -->
        <div v-for="item in featuredItems" :key="item.id" class="featured-item">
          <featured-marketplace-card
            :item="item"
            @click="handleItemClick"
          />
        </div>

        <!-- Loading placeholders -->
        <div v-if="loading" class="featured-item" v-for="n in 4" :key="`loading-${n}`">
          <featured-marketplace-skeleton />
        </div>

        <!-- Empty state when no featured items -->
        <div v-if="!loading && featuredItems.length === 0" class="featured-empty">
          <div class="text-center q-pa-lg">
            <q-icon name="storefront" size="3em" color="grey-5" />
            <p class="text-grey-6 q-mt-md">No featured marketplace items available</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import FeaturedMarketplaceCard from './FeaturedMarketplaceCard.vue';
import FeaturedMarketplaceSkeleton from './FeaturedMarketplaceSkeleton.vue';
import { usePostsStore } from '../../stores/posts';

// Props
interface Props {
  maxItems?: number;
}

const props = withDefaults(defineProps<Props>(), {
  maxItems: 5
});

// Emits
const emit = defineEmits<{
  itemClick: [item: any];
}>();

// Composables
const router = useRouter();
const postsStore = usePostsStore();

// State
const loading = ref(true);
const scrollContainer = ref<HTMLElement>();

// Computed
const featuredItems = computed(() => {
  return postsStore.posts
    .filter(post => {
      // Use same logic as posts store marketplace getter
      const hasMarketplaceSubType = post.subType?.toLowerCase() === 'marketplace';
      const hasMarketplaceFields = post.price !== undefined || (post.location && post.price);
      const hasMarketplaceTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && (
          tag.toLowerCase() === 'marketplace' ||
          tag.toLowerCase() === 'market'
        )
      );

      const isMarketplace = hasMarketplaceSubType || hasMarketplaceFields || hasMarketplaceTag;
      const isFeatured = post.tags?.includes('featured');
      const isPublished = post.status === 'published';

      return isMarketplace && isFeatured && isPublished;
    })
    .slice(0, props.maxItems);
});

// Methods
async function loadFeaturedItems() {
  try {
    loading.value = true;
    console.log('Loading featured marketplace items...');
    
    // Load marketplace items with featured tags
    await postsStore.fetchPosts({
      postType: 'MARKETPLACE',
      subType: 'marketplace',
      tags: ['featured'],
      status: 'published'
    });
    
    console.log('Featured marketplace items loaded:', featuredItems.value.length);
  } catch (error) {
    console.error('Error loading featured marketplace items:', error);
  } finally {
    loading.value = false;
  }
}

function handleItemClick(item: any) {
  emit('itemClick', item);

  // Navigate to unified post details page
  router.push({ name: 'post-details', params: { id: item.id } });
}

// Lifecycle
onMounted(() => {
  console.log('FeaturedMarketplaceSection mounted');
  loadFeaturedItems();
});
</script>

<style scoped>
.featured-marketplace-section {
  margin-bottom: 24px;
}

.featured-header {
  padding: 0 16px;
}

.featured-scroll-container {
  position: relative;
  overflow: hidden;
}

.featured-scroll-content {
  display: flex;
  gap: 16px;
  padding: 0 16px 16px 16px;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.featured-scroll-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.featured-item {
  flex: 0 0 340px;
  min-width: 340px;
}

.featured-empty {
  flex: 1;
  min-width: 100%;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .featured-item {
    flex: 0 0 300px;
    min-width: 300px;
  }

  .featured-scroll-content {
    gap: 12px;
    padding: 0 12px 12px 12px;
  }
}

@media (max-width: 480px) {
  .featured-item {
    flex: 0 0 260px;
    min-width: 260px;
  }
}
</style>
