<template>
  <div class="blog-layout">

    <!-- Loading State -->
    <div v-if="loading" class="text-center q-pa-xl">
      <q-spinner color="primary" size="3em" />
      <div class="text-subtitle1 q-mt-sm">Loading articles...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center q-pa-xl text-negative">
      <q-icon name="error" size="3em" class="q-mb-md" />
      <div class="text-h6 q-mb-md">{{ error }}</div>
      <q-btn color="primary" label="Retry" @click="fetchArticles" />
    </div>

    <!-- Blog Content -->
    <div v-else-if="articles.length > 0">
      <!-- Featured Articles Section -->
      <div v-if="featuredArticles.length > 0" class="featured-section">
        <div class="section-header q-mb-lg">
          <h3 class="text-h5 text-weight-medium q-mb-xs">Featured Stories</h3>
          <p class="text-body2 text-grey-7">Highlighted content from our community</p>
        </div>

        <!-- Featured Articles Grid -->
        <div class="featured-grid">
          <!-- Main Featured Article (Large) -->
          <div v-if="featuredArticles[0]" class="featured-main">
            <blog-featured-card
              :article="featuredArticles[0]"
              size="large"
              @read="readArticle"
            />
          </div>

          <!-- Secondary Featured Articles - 3 smaller stories on the right -->
          <div v-if="featuredArticles.length > 1" class="featured-secondary">
            <blog-featured-card
              v-for="article in featuredArticles.slice(1, 4)"
              :key="article.id"
              :article="article"
              size="small"
              @read="readArticle"
            />
          </div>
        </div>
      </div>

      <!-- Regular Articles Section -->
      <div class="articles-section">
        <div class="section-header q-mb-lg">
          <h3 class="text-h5 text-weight-medium q-mb-xs">Latest Articles</h3>
          <p class="text-body2 text-grey-7">Recent posts and updates</p>
        </div>

        <!-- Articles Grid -->
        <div class="articles-grid row q-col-gutter-lg">
          <div
            v-for="article in regularArticles"
            :key="article.id"
            class="col-12 col-sm-6 col-md-4"
          >
            <blog-card
              :article="article"
              @read="readArticle"
            />
          </div>
        </div>

        <!-- Load More Button -->
        <div class="text-center q-mt-xl">
          <q-btn
            v-if="hasMoreArticles"
            color="primary"
            outline
            label="Load More Articles"
            @click="loadMoreArticles"
            :loading="loadingMore"
            size="md"
            class="q-px-xl"
          />
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state text-center q-pa-xl">
      <q-icon name="article" size="4em" color="grey-5" class="q-mb-md" />
      <div class="text-h6 q-mb-sm">No articles found</div>
      <p class="text-body2 text-grey-7 q-mb-lg">
        {{ searchQuery || selectedCategory ? 'Try adjusting your search or filters' : 'Be the first to share your story!' }}
      </p>
      <q-btn
        v-if="!searchQuery && !selectedCategory"
        color="primary"
        label="Create Article"
        @click="createArticle"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { usePostsStore } from '@/stores/posts';
import { useFilterStore } from '@/stores/filterStore';
import { feedDataService } from '@/services/feedDataService';
import BlogCard from '@/components/feed/cards/BlogCard.vue';
import BlogFeaturedCard from '@/components/blog/BlogFeaturedCard.vue';

// Props
interface Props {
  initialFilters?: {
    searchQuery?: string;
    category?: string;
    tags?: string[];
  };
}

const props = withDefaults(defineProps<Props>(), {
  initialFilters: () => ({})
});

// Composables
const router = useRouter();
const postsStore = usePostsStore();
const filterStore = useFilterStore();

// State
const loading = ref(false);
const loadingMore = ref(false);
const error = ref<string | null>(null);
const searchQuery = ref(props.initialFilters.searchQuery || '');
const selectedCategory = ref(props.initialFilters.category || '');
const sortBy = ref('newest');

// Computed
const articles = computed(() => postsStore.articles);

const featuredArticles = computed(() => {
  return articles.value.filter(article =>
    article.tags?.includes('featured') ||
    article.category?.toLowerCase() === 'featured'
  ).slice(0, 4); // 1 main + 3 secondary featured stories
});

const regularArticles = computed(() => {
  return articles.value.filter(article => 
    !article.tags?.includes('featured') && 
    article.category?.toLowerCase() !== 'featured'
  );
});

const hasMoreArticles = computed(() => {
  // This would be determined by your pagination logic
  return false; // Placeholder
});

// Methods
async function fetchArticles() {
  loading.value = true;
  error.value = null;

  try {
    const filters = {
      category: filterStore.blogFilters.blogCategories?.length > 0 ? filterStore.blogFilters.blogCategories[0] : 'blog',
      searchQuery: filterStore.searchQuery || searchQuery.value,
      readTime: filterStore.blogFilters.readTime,
      tags: props.initialFilters?.tags || []
    };

    const pagination = {
      page: 1,
      limit: 20,
      cursor: null,
      scrollPosition: 0
    };

    await feedDataService.fetchArticles(pagination, filters);
  } catch (err: any) {
    console.error('Error fetching articles:', err);
    error.value = err.message || 'Failed to load articles';
  } finally {
    loading.value = false;
  }
}

async function loadMoreArticles() {
  loadingMore.value = true;
  try {
    // Implement pagination logic here
    await fetchArticles();
  } catch (err: any) {
    console.error('Error loading more articles:', err);
  } finally {
    loadingMore.value = false;
  }
}

function handleSort() {
  // Implement sorting logic
  fetchArticles();
}

function readArticle(articleId: number) {
  const article = articles.value.find(a => a.id === articleId);
  if (article?.slug) {
    router.push({ name: 'virtual-community-article', params: { slug: article.slug } });
  }
}



function createArticle() {
  // Navigate to article creation
  router.push({ name: 'virtual-community', query: { tab: 'feed', action: 'create', type: 'blog' } });
}

// Lifecycle
onMounted(() => {
  fetchArticles();
});

// Watch for filter changes from sidebar
watch(() => props.initialFilters, (newFilters) => {
  if (newFilters) {
    searchQuery.value = newFilters.searchQuery || '';
    selectedCategory.value = newFilters.category || '';
    fetchArticles();
  }
}, { deep: true });

// Watch for filter store changes
watch(
  () => [
    filterStore.blogFilters.blogCategories,
    filterStore.searchQuery,
    filterStore.blogFilters.readTime
  ],
  () => {
    fetchArticles();
  },
  { deep: true }
);

// Watch for local filter changes
watch([searchQuery, selectedCategory], () => {
  fetchArticles();
}, { debounce: 300 });
</script>

<style scoped>
.blog-layout {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}



.featured-section {
  margin-bottom: 0.15rem; /* Reduced by 90% from 1.5rem */
}

.section-header {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 1rem;
}

.featured-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-top: 1.5rem;
  align-items: start; /* Align items to the top to prevent stretching */
}

.featured-main {
  /* Remove fixed row span to allow natural height */
}

.featured-secondary {
  display: flex;
  flex-direction: column;
  gap: 0.75rem; /* Smaller gap for compact layout */
  /* Remove max-height to allow natural sizing */
}

.articles-grid {
  margin-top: 1.5rem;
}

.empty-state {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 1024px) {
  .featured-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .featured-secondary {
    flex-direction: row;
    overflow-x: auto;
    gap: 1rem;
    padding-bottom: 0.5rem;
  }

  .featured-secondary .featured-card {
    min-width: 300px;
    flex-shrink: 0;
  }
}

@media (max-width: 768px) {
  .featured-secondary .featured-card {
    min-width: 280px;
  }

  .articles-grid .col-md-4 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .featured-secondary .featured-card {
    min-width: 250px;
  }
}
</style>
