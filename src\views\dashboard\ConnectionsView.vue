<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <q-card class="content-header-card bg-primary text-white">
          <q-card-section>
            <div class="text-h4">My Connections</div>
            <p class="text-body1 q-mt-md">
              Manage your network connections and connection requests.
            </p>
          </q-card-section>
        </q-card>
      </div>

      <!-- Connection Requests Section -->
      <div class="col-12">
        <q-card class="connection-section-card">
          <q-tabs
            v-model="requestsTab"
            dense
            class="text-grey"
            active-color="primary"
            indicator-color="primary"
            align="justify"
            narrow-indicator
          >
            <q-tab name="received" label="Received Requests" />
            <q-tab name="sent" label="Sent Requests" />
          </q-tabs>

          <q-separator />

          <q-tab-panels v-model="requestsTab" animated>
            <q-tab-panel name="received">
              <q-card-section class="bg-grey-2">
                <div class="row items-center justify-between">
                  <div class="col">
                    <div class="text-subtitle1 text-primary">
                      Connection Requests
                      <notification-badge
                        v-if="connectionRequestsCount > 0"
                        :count="connectionRequestsCount"
                        color="red"
                        rounded
                        class="q-ml-sm"
                      />
                    </div>
                    <div class="text-caption text-grey">People who want to connect with you</div>
                  </div>
                </div>
              </q-card-section>
              <q-card-section>
                <div class="q-mb-md">
                  <q-btn
                    color="primary"
                    label="Refresh Connection Requests"
                    icon="refresh"
                    @click="refreshConnectionRequests"
                    :loading="refreshing"
                    size="sm"
                  />
                </div>
                <connection-requests
                  ref="connectionRequestsComponent"
                  @connectionAccepted="refreshConnectionsList"
                />
              </q-card-section>
            </q-tab-panel>

            <q-tab-panel name="sent">
              <q-card-section class="bg-grey-2">
                <div class="row items-center justify-between">
                  <div class="col">
                    <div class="text-subtitle1 text-primary">
                      Sent Connection Requests
                    </div>
                    <div class="text-caption text-grey">Connection requests you've sent to others</div>
                  </div>
                </div>
              </q-card-section>
              <q-card-section>
                <div class="q-mb-md">
                  <q-btn
                    color="primary"
                    label="Refresh Sent Requests"
                    icon="refresh"
                    @click="refreshSentRequests"
                    :loading="refreshingSent"
                    size="sm"
                  />
                </div>
                <sent-connection-requests ref="sentConnectionRequestsComponent" />
              </q-card-section>
            </q-tab-panel>
          </q-tab-panels>
        </q-card>
      </div>

      <!-- Established Connections Section -->
      <div class="col-12">
        <q-card class="connection-section-card">
          <q-card-section class="bg-grey-2">
            <div class="row items-center justify-between">
              <div class="col">
                <div class="text-subtitle1 text-primary">
                  My Network
                  <q-chip v-if="connectionsStore.connectionsCount > 0"
                          color="primary"
                          text-color="white"
                          size="sm"
                          class="q-ml-sm">
                    {{ connectionsStore.connectionsCount }}
                  </q-chip>
                </div>
                <div class="text-caption text-grey">People you're connected with</div>
              </div>
            </div>
          </q-card-section>
          <q-card-section>
            <connections-list ref="connectionsListComponent" @connectionAccepted="refreshConnectionsList" />
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore } from '../../stores/auth';
import { useActivityNotificationsStore } from '../../stores/activityNotifications';
import { useConnectionsStore } from '../../stores/connections';
import { useUserNotificationsStore } from '../../stores/userNotifications';
import ConnectionsList from '../../components/connections/ConnectionsList.vue';
import ConnectionRequests from '../../components/connections/ConnectionRequests.vue';
import SentConnectionRequests from '../../components/connections/SentConnectionRequests.vue';
import NotificationBadge from '../../components/common/NotificationBadge.vue';
import { supabase } from '../../lib/supabase';

const router = useRouter();
const $q = useQuasar();
const authStore = useAuthStore();
const activityNotificationsStore = useActivityNotificationsStore();
const connectionsStore = useConnectionsStore();
const userNotificationsStore = useUserNotificationsStore();

// UI state
const requestsTab = ref('received');

// Component refs
const connectionRequestsComponent = ref(null);
const sentConnectionRequestsComponent = ref(null);
const connectionsListComponent = ref(null);

// Get connection requests count
const connectionRequestsCount = computed(() => activityNotificationsStore.connectionRequests);
const refreshing = ref(false);
const refreshingSent = ref(false);

// Function to refresh connection requests
async function refreshConnectionRequests() {
  try {
    refreshing.value = true;
    console.log('Manually refreshing connection requests');

    // Debug check for connection requests
    await connectionsStore.debugCheckConnectionRequests();

    // Refresh the count
    await activityNotificationsStore.fetchConnectionRequestsCount();

    // Call the loadRequests method on the component if available
    if (connectionRequestsComponent.value &&
        typeof connectionRequestsComponent.value.loadRequests === 'function') {
      console.log('Calling loadRequests on the component');
      await connectionRequestsComponent.value.loadRequests();
    } else {
      console.log('Component ref or loadRequests method not available, using direct approach');

      // Direct approach to refresh the component
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data, error } = await supabase
          .from('user_connections')
          .select(`
            *,
            user:personal_details!user_id(
              user_id,
              email,
              first_name,
              last_name,
              profile_name,
              avatar_url
            )
          `)
          .eq('connected_user_id', user.id)
          .eq('connection_status', 'pending');

        if (error) {
          console.error('Error directly fetching connection requests:', error);
        } else if (data) {
          console.log('Directly fetched connection requests:', data.length);
          connectionsStore.connectionRequests = data;
        }
      }
    }

    // Show success notification
    $q.notify({
      color: 'positive',
      message: 'Connection requests refreshed',
      icon: 'refresh',
      position: 'top',
      timeout: 2000
    });
  } catch (error) {
    console.error('Error refreshing connection requests:', error);

    // Show error notification
    $q.notify({
      color: 'negative',
      message: 'Failed to refresh connection requests',
      icon: 'error',
      position: 'top',
      timeout: 2000
    });
  } finally {
    refreshing.value = false;
  }
}

// Function to refresh sent connection requests
async function refreshSentRequests() {
  try {
    refreshingSent.value = true;
    console.log('Manually refreshing sent connection requests');

    // Call the loadSentRequests method on the component if available
    if (sentConnectionRequestsComponent.value &&
        typeof sentConnectionRequestsComponent.value.loadSentRequests === 'function') {
      console.log('Calling loadSentRequests on the component');
      await sentConnectionRequestsComponent.value.loadSentRequests();
    } else {
      console.log('Component ref or loadSentRequests method not available, using direct approach');

      // Direct approach to refresh the component
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data, error } = await supabase
          .from('user_connections')
          .select(`
            *,
            connected_user:personal_details!connected_user_id(
              user_id,
              email,
              first_name,
              last_name,
              profile_name,
              avatar_url
            )
          `)
          .eq('user_id', user.id)
          .eq('connection_status', 'pending');

        if (error) {
          console.error('Error directly fetching sent connection requests:', error);
        } else {
          console.log('Directly fetched sent connection requests:', data?.length || 0);

          // Try to find the component and update it
          const sentRequestsComponent = document.querySelector('.sent-connection-requests');
          if (sentRequestsComponent && sentRequestsComponent.__vue__) {
            sentRequestsComponent.__vue__.sentRequests = data || [];
          }
        }
      }
    }

    // Show success notification
    $q.notify({
      color: 'positive',
      message: 'Sent connection requests refreshed',
      icon: 'refresh',
      position: 'top',
      timeout: 2000
    });
  } catch (error) {
    console.error('Error refreshing sent connection requests:', error);

    // Show error notification
    $q.notify({
      color: 'negative',
      message: 'Failed to refresh sent connection requests',
      icon: 'error',
      position: 'top',
      timeout: 2000
    });
  } finally {
    refreshingSent.value = false;
  }
}

// Function to refresh connections list
async function refreshConnectionsList() {
  try {
    console.log('Refreshing connections list and store');

    // Refresh the connections store to update the count
    console.log('Refreshing connections store...');
    await connectionsStore.fetchUserConnections();
    console.log('Connections store refreshed, new count:', connectionsStore.connectionsCount);

    // Refresh the connections list component
    if (connectionsListComponent.value &&
        typeof connectionsListComponent.value.loadConnections === 'function') {
      console.log('Refreshing connections list component');
      await connectionsListComponent.value.loadConnections();
    }
  } catch (error) {
    console.error('Error refreshing connections list:', error);
  }
}

// Check if user is authenticated and initialize data
onMounted(async () => {
  console.log('ConnectionsView mounted, auth state:', authStore.isAuthenticated);

  // Get the current user directly from Supabase
  const { data: { user } } = await supabase.auth.getUser();
  console.log('Current user from Supabase:', user);

  if (!authStore.isAuthenticated) {
    console.error('User not authenticated according to authStore');
    router.push('/login');
    $q.notify({
      color: 'negative',
      message: 'You must be logged in to view this page',
      icon: 'error'
    });
    return;
  }

  // Synchronize all connection data first
  console.log('ConnectionsView: Synchronizing connection data');
  await connectionsStore.synchronizeConnectionData();

  // Debug: Check for connection requests directly
  console.log('ConnectionsView: Debugging connection requests');
  await connectionsStore.debugCheckConnectionRequests();

  // Wait for components to be mounted
  setTimeout(async () => {
    if (connectionRequestsComponent.value &&
        typeof connectionRequestsComponent.value.loadRequests === 'function') {
      console.log('Loading connection requests via component method');
      await connectionRequestsComponent.value.loadRequests();
    }

    if (sentConnectionRequestsComponent.value &&
        typeof sentConnectionRequestsComponent.value.loadSentRequests === 'function') {
      console.log('Loading sent connection requests via component method');
      await sentConnectionRequestsComponent.value.loadSentRequests();
    }
  }, 500);

  // Fetch connection requests count
  await activityNotificationsStore.fetchConnectionRequestsCount();

  // Load user connections for count display
  console.log('ConnectionsView: Loading user connections for count display');
  await connectionsStore.fetchUserConnections();
  console.log('ConnectionsView: Connections loaded, count:', connectionsStore.connectionsCount);

  // Initialize user notifications store
  await userNotificationsStore.initialize();

  // Mark connection requests as viewed since the user is now viewing them
  activityNotificationsStore.markConnectionRequestsAsViewed();

  // Mark connection request notifications as read
  const connectionNotifications = userNotificationsStore.notifications
    .filter(n => n.type === 'connection_request' && !n.is_read)
    .map(n => n.id);

  if (connectionNotifications.length > 0) {
    await userNotificationsStore.markAsRead(connectionNotifications);
  }

  // Add a click event listener to the document to mark notifications as viewed when clicked
  document.addEventListener('click', (event) => {
    // Check if the clicked element is a notification or contains a notification
    const target = event.target as HTMLElement;
    const notificationElement = target.closest('.notification-badge, .q-badge');

    if (notificationElement) {
      console.log('Notification clicked, marking as viewed');
      // Mark both types of notifications as viewed
      activityNotificationsStore.markConnectionRequestsAsViewed();
      activityNotificationsStore.markActivitiesAsRead();
    }
  });
});
</script>

<style scoped>
.content-header-card {
  border-radius: 8px;
  overflow: hidden;
}

.connection-section-card {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.connection-section-card:hover {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

@media (max-width: 599px) {
  .q-pa-md {
    padding: 8px;
  }

  .connection-section-card {
    margin-bottom: 12px;
  }
}
</style>
