/**
 * Service Initialization Composable
 * 
 * Provides a composable for managing service initialization lifecycle
 * in Vue components and the main application.
 */

import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useGlobalServicesStore } from '../stores/globalServices';

export interface ServiceInitializationOptions {
  autoInitialize?: boolean;
  retryOnFailure?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  enableHealthMonitoring?: boolean;
  healthCheckInterval?: number;
}

export function useServiceInitialization(options: ServiceInitializationOptions = {}) {
  const {
    autoInitialize = true,
    retryOnFailure = true,
    maxRetries = 3,
    retryDelay = 2000,
    enableHealthMonitoring = true,
    healthCheckInterval = 30000
  } = options;

  const globalServices = useGlobalServicesStore();
  
  // State
  const initializationAttempts = ref(0);
  const lastInitializationError = ref<string | null>(null);
  const healthCheckTimer = ref<NodeJS.Timeout | null>(null);

  // Computed properties
  const isReady = computed(() => globalServices.allInitialized);
  const isInitializing = computed(() => globalServices.isInitializing);
  const hasErrors = computed(() => globalServices.hasErrors);
  const serviceHealth = computed(() => globalServices.serviceHealth);

  // Initialization with retry logic
  async function initializeServices(): Promise<boolean> {
    try {
      initializationAttempts.value++;
      lastInitializationError.value = null;
      
      console.log(`ServiceInitialization: Attempt ${initializationAttempts.value} to initialize services`);
      
      await globalServices.initializeAllServices();
      
      // Validate dependencies after initialization
      const validation = globalServices.validateServiceDependencies();
      if (!validation.valid) {
        throw new Error(`Service dependency validation failed: ${validation.issues.join(', ')}`);
      }
      
      console.log('ServiceInitialization: All services initialized and validated successfully');
      
      // Start health monitoring if enabled
      if (enableHealthMonitoring) {
        startHealthMonitoring();
      }
      
      return true;
    } catch (error: any) {
      lastInitializationError.value = error.message;
      console.error('ServiceInitialization: Failed to initialize services:', error);
      
      // Retry logic
      if (retryOnFailure && initializationAttempts.value < maxRetries) {
        console.log(`ServiceInitialization: Retrying in ${retryDelay}ms (attempt ${initializationAttempts.value + 1}/${maxRetries})`);
        
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return await initializeServices();
      }
      
      return false;
    }
  }

  // Health monitoring
  function startHealthMonitoring() {
    if (healthCheckTimer.value) {
      clearInterval(healthCheckTimer.value);
    }
    
    healthCheckTimer.value = setInterval(() => {
      const health = globalServices.serviceHealth;
      
      if (health.status === 'UNHEALTHY') {
        console.warn('ServiceInitialization: Services are unhealthy, attempting recovery');
        globalServices.recoverFailedServices().catch(error => {
          console.error('ServiceInitialization: Failed to recover services:', error);
        });
      } else if (health.status === 'DEGRADED') {
        console.warn('ServiceInitialization: Services are degraded:', health.failedServices);
      }
    }, healthCheckInterval);
  }

  function stopHealthMonitoring() {
    if (healthCheckTimer.value) {
      clearInterval(healthCheckTimer.value);
      healthCheckTimer.value = null;
    }
  }

  // Manual recovery
  async function recoverServices(): Promise<boolean> {
    try {
      await globalServices.recoverFailedServices();
      return true;
    } catch (error: any) {
      lastInitializationError.value = error.message;
      console.error('ServiceInitialization: Failed to recover services:', error);
      return false;
    }
  }

  // Graceful shutdown
  async function shutdownServices(): Promise<void> {
    stopHealthMonitoring();
    await globalServices.shutdownAllServices();
  }

  // Lifecycle hooks
  onMounted(async () => {
    if (autoInitialize) {
      await initializeServices();
    }
  });

  onUnmounted(() => {
    stopHealthMonitoring();
  });

  return {
    // State
    isReady,
    isInitializing,
    hasErrors,
    serviceHealth,
    initializationAttempts: computed(() => initializationAttempts.value),
    lastInitializationError: computed(() => lastInitializationError.value),

    // Actions
    initializeServices,
    recoverServices,
    shutdownServices,
    startHealthMonitoring,
    stopHealthMonitoring,

    // Service access
    services: globalServices
  };
}
