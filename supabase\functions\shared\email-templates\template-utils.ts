// Email template utilities for ZB Innovation Hub notification system

export interface EmailTemplateData {
  [key: string]: any;
}

export interface EmailTemplate {
  subject: string;
  content: string;
}

/**
 * Simple template engine for replacing placeholders in email templates
 * Supports {{variable}} syntax and {{#if condition}} blocks
 */
export class TemplateEngine {
  private static replacePlaceholders(template: string, data: EmailTemplateData): string {
    return template.replace(/\{\{([^}]+)\}\}/g, (match, key) => {
      const trimmedKey = key.trim();
      
      // Handle conditional blocks {{#if condition}}
      if (trimmedKey.startsWith('#if ')) {
        const condition = trimmedKey.substring(4);
        return data[condition] ? '' : match; // Return empty for true conditions, keep placeholder for false
      }
      
      // Handle else blocks {{else}}
      if (trimmedKey === 'else') {
        return match; // Keep placeholder for processing
      }
      
      // Handle end blocks {{/if}}
      if (trimmedKey.startsWith('/')) {
        return ''; // Remove end blocks
      }
      
      // Regular variable replacement
      return data[trimmedKey] || '';
    });
  }

  private static processConditionals(template: string, data: EmailTemplateData): string {
    // Process {{#if condition}}...{{else}}...{{/if}} blocks
    const ifRegex = /\{\{#if\s+([^}]+)\}\}(.*?)(?:\{\{else\}\}(.*?))?\{\{\/if\}\}/gs;
    
    return template.replace(ifRegex, (match, condition, ifContent, elseContent = '') => {
      const conditionValue = data[condition.trim()];
      return conditionValue ? ifContent : elseContent;
    });
  }

  public static render(template: string, data: EmailTemplateData): string {
    // First process conditionals
    let processed = this.processConditionals(template, data);
    
    // Then replace placeholders
    processed = this.replacePlaceholders(processed, data);
    
    return processed;
  }
}

/**
 * Email template manager for loading and rendering templates
 */
export class EmailTemplateManager {
  private static baseTemplate: string | null = null;
  private static templates: Map<string, string> = new Map();

  private static async loadTemplate(templateName: string): Promise<string> {
    if (this.templates.has(templateName)) {
      return this.templates.get(templateName)!;
    }

    try {
      const templatePath = `./email-templates/${templateName}.html`;
      const template = await Deno.readTextFile(templatePath);
      this.templates.set(templateName, template);
      return template;
    } catch (error) {
      console.error(`Failed to load template ${templateName}:`, error);
      throw new Error(`Template ${templateName} not found`);
    }
  }

  private static async loadBaseTemplate(): Promise<string> {
    if (this.baseTemplate) {
      return this.baseTemplate;
    }

    this.baseTemplate = await this.loadTemplate('base-template');
    return this.baseTemplate;
  }

  public static async renderEmail(
    templateName: string,
    data: EmailTemplateData,
    subject: string
  ): Promise<EmailTemplate> {
    try {
      // Load the specific template content
      const contentTemplate = await this.loadTemplate(templateName);
      
      // Render the content with data
      const renderedContent = TemplateEngine.render(contentTemplate, data);
      
      // Load base template and inject content
      const baseTemplate = await this.loadBaseTemplate();
      const fullEmailData = {
        ...data,
        subject,
        content: renderedContent,
        unsubscribe_url: data.unsubscribe_url || `${data.platform_url}/unsubscribe?token=${data.unsubscribe_token}`,
        preferences_url: data.preferences_url || `${data.platform_url}/notification-preferences`
      };
      
      const renderedEmail = TemplateEngine.render(baseTemplate, fullEmailData);
      
      return {
        subject: TemplateEngine.render(subject, data),
        content: renderedEmail
      };
    } catch (error) {
      console.error(`Failed to render email template ${templateName}:`, error);
      throw error;
    }
  }

  /**
   * Generate welcome email
   */
  public static async generateWelcomeEmail(data: {
    user_name: string;
    user_email: string;
    platform_url: string;
    unsubscribe_token?: string;
  }): Promise<EmailTemplate> {
    return this.renderEmail(
      'welcome-email',
      data,
      'Welcome to Smile-Factory - Your Innovation Journey Starts Here!'
    );
  }

  /**
   * Generate connection request email
   */
  public static async generateConnectionRequestEmail(data: {
    recipient_name: string;
    requester_name: string;
    requester_initials: string;
    requester_title: string;
    requester_id: string;
    connection_id: string;
    connection_message?: string;
    platform_url: string;
    unsubscribe_token?: string;
  }): Promise<EmailTemplate> {
    return this.renderEmail(
      'connection-request',
      data,
      `${data.requester_name} wants to connect with you on ZB Innovation Hub`
    );
  }

  /**
   * Generate connection accepted email
   */
  public static async generateConnectionAcceptedEmail(data: {
    requester_name: string;
    recipient_name: string;
    recipient_initials: string;
    recipient_title: string;
    recipient_id: string;
    platform_url: string;
    unsubscribe_token?: string;
  }): Promise<EmailTemplate> {
    return this.renderEmail(
      'connection-accepted',
      data,
      `${data.recipient_name} accepted your connection request`
    );
  }

  /**
   * Generate new message email
   */
  public static async generateNewMessageEmail(data: {
    recipient_name: string;
    sender_name: string;
    sender_initials: string;
    sender_title: string;
    sender_id: string;
    message_preview: string;
    message_truncated: boolean;
    conversation_id: string;
    conversation_title: string;
    sent_at: string;
    platform_url: string;
    unsubscribe_token?: string;
  }): Promise<EmailTemplate> {
    return this.renderEmail(
      'new-message',
      data,
      `New message from ${data.sender_name} on ZB Innovation Hub`
    );
  }

  /**
   * Generate post interaction email
   */
  public static async generatePostInteractionEmail(data: {
    post_author_name: string;
    interactor_name: string;
    interactor_initials: string;
    interactor_title: string;
    interactor_id: string;
    post_id: string;
    post_title: string;
    post_excerpt?: string;
    is_like: boolean;
    comment_text?: string;
    interaction_time: string;
    total_interactions: number;
    platform_url: string;
    unsubscribe_token?: string;
  }): Promise<EmailTemplate> {
    const subject = data.is_like 
      ? `${data.interactor_name} liked your post on ZB Innovation Hub`
      : `${data.interactor_name} commented on your post on ZB Innovation Hub`;
      
    return this.renderEmail('post-interaction', data, subject);
  }
}
