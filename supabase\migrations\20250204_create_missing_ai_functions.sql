-- Migration: Create Missing AI Database Functions
-- File: supabase/migrations/20250204_create_missing_ai_functions.sql
-- Description: Creates the missing database functions that AI services are trying to call

-- =============================================================================
-- RAG CONTEXT RETRIEVAL FUNCTION
-- =============================================================================

-- Create embeddings table if it doesn't exist (for RAG content storage)
CREATE TABLE IF NOT EXISTS embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding vector(384),
    source_table VARCHAR(100),
    source_id UUID,
    content_type VARCHAR(50), -- 'profile', 'post', 'event', etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient vector search
CREATE INDEX IF NOT EXISTS embeddings_embedding_idx 
ON embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX IF NOT EXISTS embeddings_content_type_idx ON embeddings(content_type);
CREATE INDEX IF NOT EXISTS embeddings_source_idx ON embeddings(source_table, source_id);

-- Main RAG context retrieval function
CREATE OR REPLACE FUNCTION get_rag_context(
    query_embedding TEXT, -- JSON array string format: "[0.1, 0.2, ...]"
    user_id_param UUID DEFAULT NULL,
    context_types TEXT[] DEFAULT NULL,
    max_context_items INTEGER DEFAULT 10,
    similarity_threshold FLOAT DEFAULT 0.6
)
RETURNS TABLE (
    content_snippet TEXT,
    content_type VARCHAR(50),
    relevance_score FLOAT,
    metadata JSONB,
    source_table VARCHAR(100),
    source_id UUID
)
LANGUAGE plpgsql
AS $$
DECLARE
    embedding_vector vector(384);
BEGIN
    -- Parse the embedding string to vector
    BEGIN
        embedding_vector := query_embedding::vector(384);
    EXCEPTION WHEN OTHERS THEN
        -- If parsing fails, return empty result
        RETURN;
    END;

    -- Search embeddings table for similar content
    RETURN QUERY
    SELECT 
        e.content as content_snippet,
        e.content_type,
        (1 - (e.embedding <=> embedding_vector))::FLOAT as relevance_score,
        e.metadata,
        e.source_table,
        e.source_id
    FROM embeddings e
    WHERE e.embedding IS NOT NULL
        AND (context_types IS NULL OR e.content_type = ANY(context_types))
        AND (1 - (e.embedding <=> embedding_vector)) > similarity_threshold
    ORDER BY e.embedding <=> embedding_vector
    LIMIT max_context_items;
END;
$$;

-- =============================================================================
-- AI CONVERSATION CONTEXT FUNCTIONS
-- =============================================================================

-- Enhanced AI conversations table (update existing if needed)
DO $$
BEGIN
    -- Add missing columns to ai_conversations if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'ai_conversations' AND column_name = 'title') THEN
        ALTER TABLE ai_conversations ADD COLUMN title TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'ai_conversations' AND column_name = 'summary') THEN
        ALTER TABLE ai_conversations ADD COLUMN summary TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'ai_conversations' AND column_name = 'summary_embedding') THEN
        ALTER TABLE ai_conversations ADD COLUMN summary_embedding vector(384);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'ai_conversations' AND column_name = 'message_count') THEN
        ALTER TABLE ai_conversations ADD COLUMN message_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'ai_conversations' AND column_name = 'last_message_at') THEN
        ALTER TABLE ai_conversations ADD COLUMN last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'ai_conversations' AND column_name = 'context_data') THEN
        ALTER TABLE ai_conversations ADD COLUMN context_data JSONB DEFAULT '{}';
    END IF;
END $$;

-- Create ai_messages table if it doesn't exist
CREATE TABLE IF NOT EXISTS ai_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    content_embedding vector(384),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'ai_messages_conversation_id_fkey'
    ) THEN
        ALTER TABLE ai_messages 
        ADD CONSTRAINT ai_messages_conversation_id_fkey 
        FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Create indexes for ai_messages
CREATE INDEX IF NOT EXISTS ai_messages_conversation_id_idx ON ai_messages(conversation_id);
CREATE INDEX IF NOT EXISTS ai_messages_created_at_idx ON ai_messages(created_at DESC);

-- Function to get AI conversation context
CREATE OR REPLACE FUNCTION get_ai_conversation_context(
    conversation_id_param UUID,
    message_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    conversation_id UUID,
    conversation_title TEXT,
    conversation_summary TEXT,
    messages JSONB
)
LANGUAGE plpgsql
AS $$
DECLARE
    conv_record RECORD;
    messages_json JSONB;
BEGIN
    -- Get conversation details
    SELECT c.id, c.title, c.summary
    INTO conv_record
    FROM ai_conversations c
    WHERE c.id = conversation_id_param;
    
    IF NOT FOUND THEN
        RETURN;
    END IF;
    
    -- Get recent messages for this conversation
    SELECT jsonb_agg(
        jsonb_build_object(
            'role', m.role,
            'content', m.content,
            'created_at', m.created_at
        ) ORDER BY m.created_at ASC
    )
    INTO messages_json
    FROM (
        SELECT role, content, created_at
        FROM ai_messages
        WHERE conversation_id = conversation_id_param
        ORDER BY created_at DESC
        LIMIT message_limit
    ) m;
    
    -- Return the conversation context
    RETURN QUERY
    SELECT 
        conv_record.id,
        conv_record.title,
        conv_record.summary,
        COALESCE(messages_json, '[]'::jsonb);
END;
$$;

-- =============================================================================
-- SEARCH SIMILAR MESSAGES FUNCTION
-- =============================================================================

CREATE OR REPLACE FUNCTION search_similar_messages(
    query_embedding vector(384),
    match_threshold FLOAT DEFAULT 0.7,
    match_count INTEGER DEFAULT 5,
    user_id_param UUID DEFAULT NULL
)
RETURNS TABLE (
    message_id UUID,
    conversation_id UUID,
    content TEXT,
    role VARCHAR(20),
    similarity FLOAT,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id as message_id,
        m.conversation_id,
        m.content,
        m.role,
        (1 - (m.content_embedding <=> query_embedding))::FLOAT as similarity,
        m.created_at
    FROM ai_messages m
    JOIN ai_conversations c ON m.conversation_id = c.id
    WHERE m.content_embedding IS NOT NULL
        AND (user_id_param IS NULL OR c.user_id = user_id_param)
        AND (1 - (m.content_embedding <=> query_embedding)) > match_threshold
    ORDER BY m.content_embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- =============================================================================
-- RAG SYSTEM ANALYSIS FUNCTION
-- =============================================================================

CREATE OR REPLACE FUNCTION analyze_rag_system()
RETURNS TABLE (
    total_embeddings BIGINT,
    embeddings_by_type JSONB,
    avg_embedding_age_days FLOAT,
    missing_embeddings_count BIGINT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_embeddings,
        jsonb_object_agg(
            content_type, 
            count
        ) as embeddings_by_type,
        AVG(EXTRACT(EPOCH FROM (NOW() - created_at)) / 86400)::FLOAT as avg_embedding_age_days,
        0::BIGINT as missing_embeddings_count -- Placeholder for now
    FROM (
        SELECT 
            content_type,
            COUNT(*) as count,
            created_at
        FROM embeddings
        WHERE embedding IS NOT NULL
        GROUP BY content_type, created_at
    ) stats;
END;
$$;

-- =============================================================================
-- SQL EXECUTION FUNCTION FOR TEXT2SQL
-- =============================================================================

-- Safe SQL execution function for text2sql queries
CREATE OR REPLACE FUNCTION exec_sql_direct(sql_query TEXT)
RETURNS TABLE (result JSONB)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    query_lower TEXT;
    rec RECORD;
    results JSONB[];
    result_row JSONB;
BEGIN
    -- Security check: only allow SELECT queries
    query_lower := LOWER(TRIM(sql_query));

    IF NOT query_lower LIKE 'select%' THEN
        RAISE EXCEPTION 'Only SELECT queries are allowed';
    END IF;

    -- Additional security: prevent dangerous operations
    IF query_lower LIKE '%drop%' OR
       query_lower LIKE '%delete%' OR
       query_lower LIKE '%update%' OR
       query_lower LIKE '%insert%' OR
       query_lower LIKE '%alter%' OR
       query_lower LIKE '%create%' THEN
        RAISE EXCEPTION 'Potentially dangerous SQL operations are not allowed';
    END IF;

    -- Execute the query and collect results
    FOR rec IN EXECUTE sql_query LOOP
        result_row := to_jsonb(rec);
        results := array_append(results, result_row);
    END LOOP;

    -- Return results as JSONB array
    FOR i IN 1..COALESCE(array_length(results, 1), 0) LOOP
        RETURN QUERY SELECT results[i];
    END LOOP;

    RETURN;
END;
$$;

-- =============================================================================
-- PERMISSIONS AND COMMENTS
-- =============================================================================

-- Grant permissions
GRANT ALL ON embeddings TO authenticated;
GRANT ALL ON embeddings TO service_role;
GRANT ALL ON ai_messages TO authenticated;
GRANT ALL ON ai_messages TO service_role;

-- Grant execute permissions for functions
GRANT EXECUTE ON FUNCTION exec_sql_direct TO service_role;

-- Add comments for documentation
COMMENT ON FUNCTION get_rag_context IS 'Retrieves relevant context for RAG queries using vector similarity search';
COMMENT ON FUNCTION get_ai_conversation_context IS 'Gets conversation context including recent messages for AI continuity';
COMMENT ON FUNCTION search_similar_messages IS 'Finds similar messages across conversations using vector similarity';
COMMENT ON FUNCTION analyze_rag_system IS 'Provides analytics on the RAG system status and performance';
COMMENT ON FUNCTION exec_sql_direct IS 'Safely executes SELECT queries for text2sql functionality';
COMMENT ON TABLE embeddings IS 'Stores vector embeddings for RAG content retrieval';
COMMENT ON TABLE ai_messages IS 'Stores individual messages within AI conversations';
