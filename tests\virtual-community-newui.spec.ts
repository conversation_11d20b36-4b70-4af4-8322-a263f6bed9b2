import { test, expect } from '@playwright/test'

// Helper to visit a tab with the new UI flag
async function gotoTab(page: any, tab: string) {
  await page.goto(`/virtual-community?tab=${tab}&newUI=1`)
}

test.describe('Virtual Community - New UI (flagged)', () => {
  test('Feed tab renders glass shell and search', async ({ page }) => {
    await gotoTab(page, 'feed')

    // Topbar exists
    await expect(page.locator('header.topbar')).toBeVisible()

    // AI search input present
    await expect(page.getByPlaceholder('Ask anything… (AI search)')).toBeVisible()

    // Left nav: Feed is active (aria-current="page")
    const feedLink = page.locator('aside.left nav.nav a:has-text("Feed")')
    await expect(feedLink).toBeVisible()
    await expect(feedLink).toHaveAttribute('aria-current', 'page')

    // Right rail: Announcements panel visible
    await expect(page.locator('aside.right .panel:has-text("Announcements")')).toBeVisible()
  })

  test('Events tab shows Upcoming Events and Refresh Featured', async ({ page }) => {
    await gotoTab(page, 'events')

    await expect(page.locator('aside.right .panel:has-text("Upcoming Events")')).toBeVisible()
    await expect(page.getByRole('button', { name: 'Refresh Featured' })).toBeVisible()
  })

  test('Blog tab renders within new shell without crashing', async ({ page }) => {
    await gotoTab(page, 'blog')

    // Shell topbar should still be visible
    await expect(page.locator('header.topbar')).toBeVisible()

    // Blog content might vary based on data; assert page rendered some layout elements
    // Either a featured header or an empty state should be present eventually.
    // We just ensure no navigation error and center area present.
    await expect(page.locator('.main .center')).toBeVisible()
  })
})

