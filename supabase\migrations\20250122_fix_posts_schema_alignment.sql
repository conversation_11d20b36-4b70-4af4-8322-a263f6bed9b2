-- Migration: Fix Posts Schema Alignment
-- Description: Aligns database schema with frontend expectations and fixes table/column mismatches
-- Date: 2025-01-22

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. <PERSON><PERSON> posts table with proper structure if it doesn't exist
CREATE TABLE IF NOT EXISTS public.posts (
  id SERIAL PRIMARY KEY,  -- Using SERIAL for integer IDs as expected by frontend
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  post_type VARCHAR(50) NOT NULL DEFAULT 'platform',
  sub_type VARCHAR(50) NOT NULL DEFAULT 'general',
  title TEXT,
  content TEXT NOT NULL,
  excerpt TEXT,
  image_url TEXT,
  featured_image TEXT,
  category VARCHAR(50),
  sub_category VARCHAR(50),
  tags TEXT[] DEFAULT '{}',
  visibility VARCHAR(20) DEFAULT 'public',
  is_featured BOOLEAN DEFAULT false,
  is_automated BOOLEAN DEFAULT false,
  likes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  slug TEXT,
  status VARCHAR(20) DEFAULT 'published',
  
  -- Event fields
  event_date TIMESTAMP WITH TIME ZONE,
  event_location TEXT,
  event_type VARCHAR(50),
  event_theme VARCHAR(50),
  
  -- Blog fields
  blog_title TEXT,
  blog_excerpt TEXT,
  blog_category VARCHAR(50),
  read_time INTEGER,
  
  -- Opportunity fields
  opportunity_deadline TIMESTAMP WITH TIME ZONE,
  opportunity_type VARCHAR(50),
  application_url TEXT,
  
  -- Resource fields
  resource_type VARCHAR(50),
  file_type VARCHAR(20),
  file_size INTEGER,
  download_url TEXT,
  
  -- Success Story fields
  achievement_type VARCHAR(50),
  achievement_details TEXT,
  
  -- Question/Help fields
  topic_area VARCHAR(50),
  is_resolved BOOLEAN DEFAULT false,
  
  -- Job/Talent fields
  company TEXT,
  location TEXT,
  job_type VARCHAR(50),
  compensation TEXT,
  
  -- Innovation Challenge fields
  prize TEXT,
  deadline TIMESTAMP WITH TIME ZONE,
  submission_url TEXT,
  
  -- Admin Announcement fields
  is_pinned BOOLEAN DEFAULT false,
  
  -- Automated Post fields
  activity_type VARCHAR(50),
  related_entity_id INTEGER,  -- Changed from UUID to INTEGER to match frontend expectations
  
  -- Media and metadata
  media_urls JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create comments table (using 'comments' as expected by frontend)
CREATE TABLE IF NOT EXISTS public.comments (
  id SERIAL PRIMARY KEY,  -- Using SERIAL for integer IDs
  post_id INTEGER REFERENCES public.posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  likes_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create likes table (using 'likes' as expected by frontend)
CREATE TABLE IF NOT EXISTS public.likes (
  id SERIAL PRIMARY KEY,  -- Using SERIAL for integer IDs
  post_id INTEGER REFERENCES public.posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, user_id)
);

-- 4. Create comment_likes table
CREATE TABLE IF NOT EXISTS public.comment_likes (
  id SERIAL PRIMARY KEY,
  comment_id INTEGER REFERENCES public.comments(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(comment_id, user_id)
);

-- 5. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_posts_post_type ON public.posts(post_type);
CREATE INDEX IF NOT EXISTS idx_posts_sub_type ON public.posts(sub_type);
CREATE INDEX IF NOT EXISTS idx_posts_category ON public.posts(category);
CREATE INDEX IF NOT EXISTS idx_posts_created_at ON public.posts(created_at);
CREATE INDEX IF NOT EXISTS idx_posts_user_id ON public.posts(user_id);
CREATE INDEX IF NOT EXISTS idx_posts_status ON public.posts(status);

CREATE INDEX IF NOT EXISTS idx_comments_post_id ON public.comments(post_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON public.comments(created_at);

CREATE INDEX IF NOT EXISTS idx_likes_post_id ON public.likes(post_id);
CREATE INDEX IF NOT EXISTS idx_likes_user_id ON public.likes(user_id);

CREATE INDEX IF NOT EXISTS idx_comment_likes_comment_id ON public.comment_likes(comment_id);
CREATE INDEX IF NOT EXISTS idx_comment_likes_user_id ON public.comment_likes(user_id);

-- 6. Enable Row Level Security
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comment_likes ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS policies for posts
CREATE POLICY IF NOT EXISTS "Posts are viewable by everyone"
  ON public.posts FOR SELECT
  USING (true);

CREATE POLICY IF NOT EXISTS "Users can insert their own posts"
  ON public.posts FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own posts"
  ON public.posts FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own posts"
  ON public.posts FOR DELETE
  USING (auth.uid() = user_id);

-- 8. Create RLS policies for comments
CREATE POLICY IF NOT EXISTS "Comments are viewable by everyone"
  ON public.comments FOR SELECT
  USING (true);

CREATE POLICY IF NOT EXISTS "Users can insert their own comments"
  ON public.comments FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own comments"
  ON public.comments FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own comments"
  ON public.comments FOR DELETE
  USING (auth.uid() = user_id);

-- 9. Create RLS policies for likes
CREATE POLICY IF NOT EXISTS "Likes are viewable by everyone"
  ON public.likes FOR SELECT
  USING (true);

CREATE POLICY IF NOT EXISTS "Users can insert their own likes"
  ON public.likes FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own likes"
  ON public.likes FOR DELETE
  USING (auth.uid() = user_id);

-- 10. Create RLS policies for comment_likes
CREATE POLICY IF NOT EXISTS "Comment likes are viewable by everyone"
  ON public.comment_likes FOR SELECT
  USING (true);

CREATE POLICY IF NOT EXISTS "Users can insert their own comment likes"
  ON public.comment_likes FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own comment likes"
  ON public.comment_likes FOR DELETE
  USING (auth.uid() = user_id);

-- 11. Create or replace the posts_with_authors view
CREATE OR REPLACE VIEW posts_with_authors AS
SELECT 
    p.*,
    prof.first_name,
    prof.last_name,
    prof.email,
    prof.avatar_url
FROM 
    posts p
LEFT JOIN 
    profiles prof ON p.user_id = prof.user_id;

-- 12. Create comments_with_authors view
CREATE OR REPLACE VIEW comments_with_authors AS
SELECT 
    c.*,
    prof.first_name,
    prof.last_name,
    prof.email,
    prof.avatar_url
FROM 
    comments c
LEFT JOIN 
    profiles prof ON c.user_id = prof.user_id;

-- 13. Grant permissions
GRANT ALL ON public.posts TO authenticated, service_role;
GRANT ALL ON public.comments TO authenticated, service_role;
GRANT ALL ON public.likes TO authenticated, service_role;
GRANT ALL ON public.comment_likes TO authenticated, service_role;
GRANT SELECT ON posts_with_authors TO authenticated, service_role;
GRANT SELECT ON comments_with_authors TO authenticated, service_role;

-- 14. Create trigger function for updating updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 15. Create triggers for automatic timestamp updates
DROP TRIGGER IF EXISTS update_posts_updated_at ON public.posts;
CREATE TRIGGER update_posts_updated_at
    BEFORE UPDATE ON public.posts
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_comments_updated_at ON public.comments;
CREATE TRIGGER update_comments_updated_at
    BEFORE UPDATE ON public.comments
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- 16. Add check constraints for data integrity
ALTER TABLE public.posts 
ADD CONSTRAINT check_post_type 
CHECK (post_type IN ('automated', 'admin', 'platform'));

ALTER TABLE public.posts 
ADD CONSTRAINT check_status 
CHECK (status IN ('draft', 'published', 'archived', 'pending_approval'));

ALTER TABLE public.posts 
ADD CONSTRAINT check_visibility 
CHECK (visibility IN ('public', 'private', 'connections'));

-- Migration completed successfully
-- This migration aligns the database schema with frontend expectations:
-- 1. Uses integer IDs instead of UUIDs for posts, comments, and likes
-- 2. Uses correct table names (comments, likes) as expected by frontend
-- 3. Includes all necessary columns and proper data types
-- 4. Sets up proper RLS policies and indexes
-- 5. Creates views for joined data with author information
