/**
 * AI Enhanced Embedding Service
 * 
 * Builds on the existing embedding system to provide enhanced user interest
 * embeddings, composite embeddings, and dynamic interest tracking for better
 * AI recommendations and content matching.
 * 
 * IMPORTANT: This service enhances the existing embedding system without
 * replacing it. It works with the current generate-embeddings Edge function.
 */

import { supabase } from '@/lib/supabase'

export interface UserInterestProfile {
  user_id: string
  interests: string[]
  preferences: Record<string, any>
  behavior_patterns: string[]
  interaction_history: string[]
  last_updated: string
}

export interface CompositeEmbedding {
  profile_embedding: number[]
  interest_embedding: number[]
  behavior_embedding: number[]
  composite_embedding: number[]
  similarity_weights: {
    profile: number
    interest: number
    behavior: number
  }
}

export interface EnhancedMatchResult {
  profile_id: string
  user_id: string
  profile_type: string
  similarity_score: number
  match_reasons: string[]
  interest_overlap: string[]
  behavior_compatibility: number
  profile_data: any
}

export class AIEnhancedEmbeddingService {
  
  /**
   * Generate enhanced user interest embedding based on profile data and behavior
   */
  async generateUserInterestEmbedding(userId: string): Promise<number[] | null> {
    try {
      // Get user's profile data
      const { data: profile, error: profileError } = await supabase
        .from('personal_details')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (profileError || !profile) {
        console.warn('Could not get profile for interest embedding:', profileError)
        return null
      }

      // Get profile-specific data based on profile type
      const profileSpecificData = await this.getProfileSpecificData(userId, profile.profile_type)
      
      // Extract interests from various sources
      const interests = this.extractUserInterests(profile, profileSpecificData)
      
      // Get user behavior data
      const behaviorData = await this.getUserBehaviorData(userId)
      
      // Combine all interest sources
      const interestText = this.combineInterestSources(interests, behaviorData)
      
      if (!interestText || interestText.length < 10) {
        console.log('Insufficient interest data for embedding generation')
        return null
      }

      // Generate embedding using existing embed function
      const { data, error } = await supabase.functions.invoke('embed', {
        body: {
          input: interestText,
          model: 'bge-m3', // Use the best model for interests
          chunk_strategy: 'none'
        }
      })

      if (error) {
        console.error('Error generating interest embedding:', error)
        return null
      }

      return data.embedding
    } catch (error) {
      console.error('Error in generateUserInterestEmbedding:', error)
      return null
    }
  }

  /**
   * Get profile-specific data for interest extraction
   */
  private async getProfileSpecificData(userId: string, profileType: string): Promise<any> {
    const tableMap: Record<string, string> = {
      'innovator': 'innovator_profiles',
      'investor': 'investor_profiles',
      'mentor': 'mentor_profiles',
      'professional': 'professional_profiles',
      'industry_expert': 'industry_expert_profiles',
      'academic_student': 'academic_student_profiles',
      'academic_institution': 'academic_institution_profiles'
    }

    const tableName = tableMap[profileType]
    if (!tableName) return null

    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .eq('user_id', userId)
        .single()

      return error ? null : data
    } catch (error) {
      console.warn(`Could not get ${profileType} specific data:`, error)
      return null
    }
  }

  /**
   * Extract user interests from profile data
   */
  private extractUserInterests(profile: any, profileSpecificData: any): string[] {
    const interests: string[] = []

    // Extract from basic profile
    if (profile.bio) {
      interests.push(profile.bio)
    }

    // Extract from profile-specific data based on type
    if (profileSpecificData) {
      switch (profile.profile_type) {
        case 'innovator':
          if (profileSpecificData.innovation_area) interests.push(profileSpecificData.innovation_area)
          if (profileSpecificData.innovation_description) interests.push(profileSpecificData.innovation_description)
          if (profileSpecificData.current_challenges) interests.push(profileSpecificData.current_challenges)
          if (profileSpecificData.short_term_goals) interests.push(profileSpecificData.short_term_goals)
          break
        case 'investor':
          if (profileSpecificData.investment_focus) interests.push(profileSpecificData.investment_focus)
          if (profileSpecificData.investment_criteria) interests.push(profileSpecificData.investment_criteria)
          if (profileSpecificData.preferred_sectors) interests.push(profileSpecificData.preferred_sectors)
          break
        case 'mentor':
          if (profileSpecificData.expertise_areas) interests.push(profileSpecificData.expertise_areas)
          if (profileSpecificData.industry_experience) interests.push(profileSpecificData.industry_experience)
          if (profileSpecificData.collaboration_interests) interests.push(profileSpecificData.collaboration_interests)
          break
        case 'professional':
          if (profileSpecificData.skills) interests.push(profileSpecificData.skills)
          if (profileSpecificData.industry) interests.push(profileSpecificData.industry)
          if (profileSpecificData.career_goals) interests.push(profileSpecificData.career_goals)
          break
        case 'academic_student':
          if (profileSpecificData.field_of_study) interests.push(profileSpecificData.field_of_study)
          if (profileSpecificData.research_interests) interests.push(profileSpecificData.research_interests)
          if (profileSpecificData.career_interests) interests.push(profileSpecificData.career_interests)
          break
      }
    }

    return interests.filter(Boolean)
  }

  /**
   * Get user behavior data for interest analysis
   */
  private async getUserBehaviorData(userId: string): Promise<string[]> {
    const behaviorData: string[] = []

    try {
      // Get recent AI conversations for interest patterns
      const { data: conversations } = await supabase
        .from('ai_conversations')
        .select('summary, context_data')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10)

      if (conversations) {
        conversations.forEach(conv => {
          if (conv.summary) behaviorData.push(conv.summary)
          if (conv.context_data?.interests) {
            behaviorData.push(...conv.context_data.interests)
          }
        })
      }

      // Get recent posts for content interests
      const { data: posts } = await supabase
        .from('posts')
        .select('title, content, tags')
        .eq('author_id', userId)
        .order('created_at', { ascending: false })
        .limit(5)

      if (posts) {
        posts.forEach(post => {
          if (post.title) behaviorData.push(post.title)
          if (post.content) behaviorData.push(post.content.substring(0, 200))
          if (post.tags) behaviorData.push(post.tags.join(' '))
        })
      }

    } catch (error) {
      console.warn('Could not get user behavior data:', error)
    }

    return behaviorData.filter(Boolean)
  }

  /**
   * Combine interest sources into text for embedding
   */
  private combineInterestSources(interests: string[], behaviorData: string[]): string {
    const allSources = [...interests, ...behaviorData]
    
    // Clean and deduplicate
    const cleanedSources = allSources
      .map(text => text.trim())
      .filter(text => text.length > 5)
      .slice(0, 20) // Limit to prevent token overflow

    return cleanedSources.join(' | ')
  }

  /**
   * Create composite embedding from multiple embedding types
   */
  async createCompositeEmbedding(userId: string, profileType: string): Promise<CompositeEmbedding | null> {
    try {
      // Get existing profile embeddings
      const profileEmbedding = await this.getExistingProfileEmbedding(userId, profileType)
      
      // Generate interest embedding
      const interestEmbedding = await this.generateUserInterestEmbedding(userId)
      
      // Generate behavior embedding (simplified for now)
      const behaviorEmbedding = await this.generateBehaviorEmbedding(userId)

      if (!profileEmbedding || !interestEmbedding) {
        console.log('Missing required embeddings for composite creation')
        return null
      }

      // Create weighted composite embedding
      const weights = this.getCompositeWeights(profileType)
      const compositeEmbedding = this.combineEmbeddings([
        { embedding: profileEmbedding, weight: weights.profile },
        { embedding: interestEmbedding, weight: weights.interest },
        { embedding: behaviorEmbedding || new Array(1024).fill(0), weight: weights.behavior }
      ])

      return {
        profile_embedding: profileEmbedding,
        interest_embedding: interestEmbedding,
        behavior_embedding: behaviorEmbedding || new Array(1024).fill(0),
        composite_embedding: compositeEmbedding,
        similarity_weights: weights
      }
    } catch (error) {
      console.error('Error creating composite embedding:', error)
      return null
    }
  }

  /**
   * Get existing profile embedding from database
   */
  private async getExistingProfileEmbedding(userId: string, profileType: string): Promise<number[] | null> {
    const tableMap: Record<string, string> = {
      'innovator': 'innovator_profiles',
      'investor': 'investor_profiles',
      'mentor': 'mentor_profiles',
      'professional': 'professional_profiles',
      'industry_expert': 'industry_expert_profiles',
      'academic_student': 'academic_student_profiles',
      'academic_institution': 'academic_institution_profiles'
    }

    const tableName = tableMap[profileType]
    if (!tableName) return null

    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('profile_embedding')
        .eq('user_id', userId)
        .single()

      if (error || !data?.profile_embedding) return null

      // Parse embedding from string format
      const embeddingStr = data.profile_embedding.replace(/[\[\]]/g, '')
      return embeddingStr.split(',').map(Number)
    } catch (error) {
      console.warn('Could not get existing profile embedding:', error)
      return null
    }
  }

  /**
   * Generate behavior embedding based on user interactions
   */
  private async generateBehaviorEmbedding(userId: string): Promise<number[] | null> {
    // For now, return null - this can be enhanced later with interaction tracking
    return null
  }

  /**
   * Get composite weights based on profile type
   */
  private getCompositeWeights(profileType: string): { profile: number; interest: number; behavior: number } {
    const weightMap: Record<string, { profile: number; interest: number; behavior: number }> = {
      'innovator': { profile: 0.4, interest: 0.5, behavior: 0.1 },
      'investor': { profile: 0.5, interest: 0.4, behavior: 0.1 },
      'mentor': { profile: 0.6, interest: 0.3, behavior: 0.1 },
      'professional': { profile: 0.5, interest: 0.4, behavior: 0.1 },
      'academic_student': { profile: 0.3, interest: 0.6, behavior: 0.1 },
      'industry_expert': { profile: 0.6, interest: 0.3, behavior: 0.1 },
      'academic_institution': { profile: 0.7, interest: 0.2, behavior: 0.1 }
    }

    return weightMap[profileType] || { profile: 0.5, interest: 0.4, behavior: 0.1 }
  }

  /**
   * Combine multiple embeddings with weights
   */
  private combineEmbeddings(embeddingData: { embedding: number[]; weight: number }[]): number[] {
    if (embeddingData.length === 0) return []

    const dimensions = embeddingData[0].embedding.length
    const result = new Array(dimensions).fill(0)

    // Weighted average of embeddings
    for (let i = 0; i < dimensions; i++) {
      let weightedSum = 0
      let totalWeight = 0

      for (const { embedding, weight } of embeddingData) {
        if (embedding.length === dimensions) {
          weightedSum += embedding[i] * weight
          totalWeight += weight
        }
      }

      result[i] = totalWeight > 0 ? weightedSum / totalWeight : 0
    }

    return result
  }

  /**
   * Store enhanced embedding data for future use
   */
  async storeEnhancedEmbedding(userId: string, compositeEmbedding: CompositeEmbedding): Promise<boolean> {
    try {
      // Store in a new table for enhanced embeddings (to be created)
      // For now, we'll store in the embeddings table with special metadata
      const { error } = await supabase
        .from('embeddings')
        .upsert({
          content: `Enhanced user embedding for ${userId}`,
          metadata: {
            type: 'user_composite',
            user_id: userId,
            weights: compositeEmbedding.similarity_weights,
            generated_at: new Date().toISOString()
          },
          embedding: `[${compositeEmbedding.composite_embedding.join(',')}]`,
          source_table: 'personal_details',
          source_id: userId
        }, {
          onConflict: 'source_table,source_id'
        })

      return !error
    } catch (error) {
      console.error('Error storing enhanced embedding:', error)
      return false
    }
  }
}

// Export singleton instance
export const aiEnhancedEmbeddingService = new AIEnhancedEmbeddingService()
