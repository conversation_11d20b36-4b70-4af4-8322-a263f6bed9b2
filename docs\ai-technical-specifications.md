# AI System Technical Specifications

## Overview

This document provides detailed technical specifications for the overhauled AI system in the ZbInnovation platform, based on research findings and platform requirements.

## Core Requirements

### 1. Authentication-Aware AI System

#### User Context Detection
```typescript
interface UserAuthContext {
  // Authentication State
  is_authenticated: boolean;
  user_id?: string;
  session_id: string;
  
  // Profile Status
  profile_exists: boolean;
  profile_completion_percentage: number;
  profile_type?: 'innovator' | 'mentor' | 'investor' | 'expert' | 'student' | 'institution' | 'organization' | 'government';
  
  // Platform Engagement
  onboarding_completed: boolean;
  first_time_user: boolean;
  last_active: Date;
  platform_familiarity_score: number; // 1-10
}
```

#### Context-Aware Response System
```typescript
interface AIResponseContext {
  // For Unauthenticated Users
  signup_cta: boolean;
  login_cta: boolean;
  platform_exploration_mode: boolean;
  
  // For Authenticated Users
  profile_completion_suggestions: boolean;
  personalized_recommendations: boolean;
  advanced_features_access: boolean;
  
  // Universal
  navigation_assistance: boolean;
  feature_explanations: boolean;
  community_guidance: boolean;
}
```

### 2. Bidirectional Integration System

#### AI-to-UI Actions
```typescript
interface AIToUIAction {
  id: string;
  type: 'navigation' | 'dialog' | 'filter' | 'prefill' | 'highlight';
  
  // Navigation Actions
  route?: string;
  route_params?: Record<string, any>;
  
  // Dialog Actions
  dialog_type?: 'auth' | 'profile' | 'connection' | 'post' | 'message';
  dialog_data?: any;
  
  // Filter Actions
  filter_section?: 'feed' | 'profiles' | 'events' | 'groups' | 'marketplace';
  filter_criteria?: Record<string, any>;
  
  // Prefill Actions
  form_type?: 'post' | 'message' | 'profile' | 'event';
  prefill_data?: Record<string, any>;
  
  // UI Highlighting
  highlight_elements?: string[];
  
  // Execution Context
  requires_auth: boolean;
  confirmation_required: boolean;
  success_message?: string;
  error_fallback?: string;
}
```

#### UI-to-AI Triggers
```typescript
interface UIToAITrigger {
  trigger_id: string;
  context_type: 'profile_completion' | 'content_discovery' | 'matchmaking' | 'help' | 'creation_assistance';
  
  // Context Data
  current_page: string;
  user_context: UserAuthContext;
  page_state?: any;
  
  // Prefilled Content
  initial_message?: string;
  suggested_questions: string[];
  
  // Expected Outcomes
  expected_actions: AIToUIAction[];
  conversation_goal: string;
}
```

### 3. Platform-Specific AI Features

#### Content Discovery & Matchmaking
```typescript
interface ContentDiscoveryRequest {
  user_profile: UserProfile;
  discovery_type: 'profiles' | 'posts' | 'events' | 'groups' | 'opportunities';
  current_filters?: any;
  user_preferences?: any;
  interaction_history?: any[];
}

interface MatchmakingCriteria {
  profile_type_compatibility: string[];
  interest_alignment: string[];
  goal_matching: string[];
  location_preferences?: any;
  collaboration_type?: string;
  experience_level?: string;
}
```

#### SQL Query Generation
```typescript
interface NaturalLanguageQuery {
  user_question: string;
  context: {
    user_permissions: string[];
    accessible_tables: string[];
    user_profile: UserProfile;
  };
  expected_result_type: 'data' | 'insights' | 'recommendations';
}

interface GeneratedSQLQuery {
  sql: string;
  explanation: string;
  safety_checks: string[];
  estimated_complexity: 'low' | 'medium' | 'high';
  requires_approval: boolean;
}
```

### 4. Conversation Memory System

#### Vector-Based Memory Storage
```sql
-- Enhanced conversation memory with context
CREATE TABLE ai_conversation_memory (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  session_id UUID NOT NULL,
  
  -- Conversation Data
  user_message TEXT NOT NULL,
  ai_response TEXT NOT NULL,
  conversation_turn INTEGER NOT NULL,
  
  -- Vector Embeddings
  message_embedding vector(1536),
  response_embedding vector(1536),
  context_embedding vector(1536),
  
  -- Context Information
  user_context JSONB NOT NULL,
  platform_state JSONB DEFAULT '{}',
  actions_taken JSONB DEFAULT '[]',
  
  -- Metadata
  conversation_type TEXT NOT NULL, -- 'general', 'support', 'discovery', 'creation'
  satisfaction_score INTEGER, -- 1-5, user feedback
  resolved BOOLEAN DEFAULT false,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for efficient vector similarity search
CREATE INDEX ai_conversation_memory_embedding_idx 
ON ai_conversation_memory 
USING ivfflat (message_embedding vector_cosine_ops);
```

#### Memory Retrieval Strategy
```typescript
interface ConversationMemoryQuery {
  user_id: string;
  current_message: string;
  context_similarity_threshold: number; // 0.7-0.9
  max_retrieved_conversations: number; // 5-10
  time_decay_factor: number; // Recent conversations weighted higher
  conversation_type_filter?: string[];
}

interface RetrievedMemory {
  conversation_id: string;
  similarity_score: number;
  context_relevance: number;
  time_relevance: number;
  combined_score: number;
  summary: string;
  key_insights: string[];
}
```

### 5. Real-Time Streaming Implementation

#### Server-Sent Events Structure
```typescript
interface AIStreamChunk {
  type: 'content' | 'action' | 'suggestion' | 'complete' | 'error';
  
  // Content Chunks
  content?: string;
  content_complete?: boolean;
  
  // Action Chunks
  action?: AIToUIAction;
  action_ready?: boolean;
  
  // Suggestion Chunks
  suggestions?: string[];
  
  // Completion
  conversation_id?: string;
  total_tokens?: number;
  
  // Error Handling
  error?: {
    code: string;
    message: string;
    recoverable: boolean;
  };
}
```

#### Streaming Response Handler
```typescript
class AIStreamHandler {
  private eventSource: EventSource;
  private messageBuffer: string = '';
  private currentActions: AIToUIAction[] = [];
  
  async handleStream(
    request: AIRequest,
    onChunk: (chunk: AIStreamChunk) => void,
    onComplete: (result: AIResponse) => void,
    onError: (error: Error) => void
  ): Promise<void> {
    // Implementation for handling streaming responses
  }
  
  private parseStreamChunk(data: string): AIStreamChunk {
    // Parse line-delimited JSON chunks
  }
  
  private executeAction(action: AIToUIAction): Promise<void> {
    // Execute UI actions from AI responses
  }
}
```

### 6. Security & Privacy Specifications

#### Row Level Security Implementation
```sql
-- Conversation privacy
CREATE POLICY "Users can only access their own conversations"
ON ai_conversation_memory
FOR ALL TO authenticated
USING (user_id = auth.uid());

-- Schema access control
CREATE POLICY "Users can only query accessible schema elements"
ON platform_schema_index
FOR SELECT TO authenticated
USING (
  table_name IN (
    SELECT table_name FROM user_accessible_tables 
    WHERE user_id = auth.uid()
  )
);
```

#### Data Encryption
```typescript
interface EncryptionConfig {
  // Conversation content encryption
  conversation_encryption: {
    algorithm: 'AES-256-GCM';
    key_rotation_days: 30;
    encrypt_embeddings: false; // Embeddings need to be searchable
  };
  
  // User context encryption
  context_encryption: {
    sensitive_fields: ['email', 'phone', 'personal_details'];
    encryption_level: 'field_level';
  };
}
```

### 7. Performance Specifications

#### Response Time Requirements
```typescript
interface PerformanceTargets {
  // Streaming Response Times
  first_chunk_latency: '< 500ms';
  chunk_interval: '< 100ms';
  total_response_time: '< 10s';
  
  // Vector Search Performance
  similarity_search_time: '< 200ms';
  embedding_generation_time: '< 300ms';
  
  // Database Query Performance
  context_retrieval_time: '< 150ms';
  conversation_storage_time: '< 100ms';
  
  // UI Action Execution
  action_execution_time: '< 50ms';
  navigation_response_time: '< 100ms';
}
```

#### Caching Strategy
```typescript
interface CachingConfig {
  // User Context Caching
  user_context_ttl: 300; // 5 minutes
  profile_data_ttl: 1800; // 30 minutes
  
  // Conversation Memory Caching
  recent_conversations_ttl: 600; // 10 minutes
  embedding_cache_ttl: 3600; // 1 hour
  
  // Schema Metadata Caching
  schema_index_ttl: 86400; // 24 hours
  query_templates_ttl: 3600; // 1 hour
  
  // Response Caching
  common_responses_ttl: 1800; // 30 minutes
  user_specific_cache_ttl: 900; // 15 minutes
}
```

### 8. Error Handling & Fallback Strategies

#### Error Classification
```typescript
interface AIErrorTypes {
  // API Errors
  'deepseek_api_error': {
    fallback: 'cached_response' | 'generic_help';
    user_message: 'AI service temporarily unavailable. Please try again.';
    retry_strategy: 'exponential_backoff';
  };
  
  // Context Errors
  'context_retrieval_error': {
    fallback: 'basic_context';
    user_message: 'Using basic assistance mode.';
    retry_strategy: 'immediate';
  };
  
  // Permission Errors
  'insufficient_permissions': {
    fallback: 'auth_prompt';
    user_message: 'Please sign in to access this feature.';
    retry_strategy: 'none';
  };
  
  // Vector Database Errors
  'vector_search_error': {
    fallback: 'keyword_search';
    user_message: 'Using alternative search method.';
    retry_strategy: 'circuit_breaker';
  };
}
```

## Implementation Priorities

### Phase 1: Core Infrastructure
1. Database schema setup with pg_vector
2. Basic Edge Functions with DeepSeek integration
3. Authentication-aware context building
4. Simple conversation memory

### Phase 2: Bidirectional Integration
1. AI-to-UI action system
2. UI-to-AI trigger system
3. Real-time streaming implementation
4. Basic error handling

### Phase 3: Advanced Features
1. SQL query generation
2. Content discovery & matchmaking
3. Advanced conversation memory
4. Performance optimization

### Phase 4: Production Readiness
1. Comprehensive security implementation
2. Advanced error handling & fallbacks
3. Performance monitoring
4. User acceptance testing

## Cleanup & Removal Plan

### Files to Remove
```
docs/ai-features-implementation.md
docs/ai-assistant-implementation-tracker.md
docs/ai-implementation-analysis.md
docs/ai-cleanup-summary.md
docs/ai-assistant-technical-implementation.md
docs/ai-enhancement-project/ (entire directory)
src/services/aiService.ts (deprecated)
src/components/ai/AIFeaturesCard.vue (mock implementation)
```

### Files to Overhaul
```
src/services/aiEnhancedService.ts → src/services/aiChatService.ts
src/stores/aiChatStore.ts (enhance with new specifications)
src/components/ai/AIChatAssistant.vue (rebuild with new architecture)
supabase/functions/ai-enhanced-chat/index.ts (complete rewrite)
```

### Database Cleanup
```sql
-- Remove old AI tables if they exist
DROP TABLE IF EXISTS ai_post_suggestions;
DROP TABLE IF EXISTS ai_learning_data;
DROP TABLE IF EXISTS chat_sessions;
DROP TABLE IF EXISTS chat_messages;

-- Create new enhanced schema as specified above
```

This specification provides the technical foundation for implementing a production-ready AI system that meets all the platform requirements while maintaining security, performance, and user experience standards.
