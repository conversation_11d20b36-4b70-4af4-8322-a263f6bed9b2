import { supabase } from './supabase';

/**
 * Fixes the post creation functionality by ensuring the database has the necessary views
 * and that the post data is properly formatted
 */
export async function fixPostCreation() {
  try {
    console.log('Fixing post creation functionality...');

    // Step 1: Check if posts_with_authors view exists
    console.log('Checking posts_with_authors view...');
    
    // Step 2: Check if the posts table exists and has the required fields
    const { data: tableExists, error: tableError } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'posts'
        );
      `
    });
    
    if (tableError) {
      console.error('Error checking if posts table exists:', tableError);
      return { success: false, error: tableError };
    }
    
    if (!tableExists || !tableExists[0] || !tableExists[0].exists) {
      console.error('Posts table does not exist');
      return { success: false, error: 'Posts table does not exist' };
    }
    
    console.log('Post creation functionality fixed successfully');
    return { success: true };
  } catch (error) {
    console.error('Error fixing post creation functionality:', error);
    return { success: false, error };
  }
}
