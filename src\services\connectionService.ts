import { supabase } from '@/lib/supabase';
import { ref } from 'vue';
import { integratedNotificationService } from './integratedNotificationService';
import { getUniversalUsername } from '../utils/userUtils';

/**
 * Connection Management Service
 *
 * This service provides functions for managing user connections and retrieving connection data.
 * It interacts with the user_connections table in the database.
 */
export function useConnectionService(activityService?: any) {
  const isLoading = ref(false);
  // Activity service will be injected by the global services store

  /**
   * Send a connection request to another user
   *
   * @param userId The user ID to connect with
   * @param connectionType The type of connection (e.g., 'follow', 'connect', 'mentor')
   * @returns Success status
   */
  async function connectWithUser(
    userId: string,
    connectionType: string = 'follow'
  ): Promise<boolean> {
    if (isLoading.value) return false;

    try {
      isLoading.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user || user.id === userId) {
        console.error('Cannot connect: Invalid user ID');
        return false;
      }

      console.log(`Connecting with user ${userId} (type: ${connectionType})`);

      // Check if connection already exists - don't use single() to avoid 406 errors
      const { data: existingConnections, error: checkError } = await supabase
        .from('user_connections')
        .select('*')
        .eq('user_id', user.id)
        .eq('connected_user_id', userId)
        .limit(1);

      if (checkError) {
        console.error('Error checking existing connection:', checkError);
        throw checkError;
      }

      const existingConnection = existingConnections && existingConnections.length > 0
        ? existingConnections[0]
        : null;

      if (existingConnection) {
        // Connection already exists, check its current status
        console.log('Connection already exists:', existingConnection);

        if (existingConnection.connection_status === 'pending') {
          console.log('Connection request already pending');
          return true; // Don't create duplicate pending requests
        }

        if (existingConnection.connection_status === 'accepted') {
          console.log('Users are already connected');
          return true; // Already connected
        }

        // Update existing connection to pending (e.g., if it was rejected before)
        const { error: updateError } = await supabase
          .from('user_connections')
          .update({
            connection_type: connectionType,
            connection_status: 'pending',
            updated_at: new Date().toISOString()
          })
          .eq('id', existingConnection.id);

        if (updateError) {
          console.error('Error updating connection:', updateError);
          throw updateError;
        }
      } else {
        // Create new connection
        console.log('Creating new connection...');

        const { error: insertError } = await supabase
          .from('user_connections')
          .insert({
            user_id: user.id,
            connected_user_id: userId,
            connection_type: connectionType,
            connection_status: 'pending'
          });

        if (insertError) {
          console.error('Error creating connection:', insertError);
          throw insertError;
        }
      }

      // Track the activity
      await activityService.trackActivity('connect_request', {
        connected_user_id: userId,
        connection_type: connectionType
      });

      // Trigger global connection synchronization
      const { useConnectionsStore } = await import('../stores/connections');
      const connectionsStore = useConnectionsStore();
      await connectionsStore.synchronizeConnectionData();

      // Get user details to create a personalized notification
      const { data: userData, error: userError } = await supabase
        .from('personal_details')
        .select('first_name, last_name, email')
        .eq('user_id', user.id)
        .single();

      if (!userError && userData) {
        // Create a notification for the recipient
        const senderName = userData.first_name && userData.last_name
          ? `${userData.first_name} ${userData.last_name}`
          : getUniversalUsername(userData.email);

        // Get the connection ID for the notification
        let connectionId = existingConnection ? existingConnection.id : null;

        // If we created a new connection, get its ID
        if (!connectionId) {
          const { data: newConnectionData } = await supabase
            .from('user_connections')
            .select('id')
            .eq('user_id', user.id)
            .eq('connected_user_id', userId)
            .eq('connection_status', 'pending')
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

          connectionId = newConnectionData?.id || null;
        }

        // Use the new integrated notification service for connection requests
        const { integratedNotificationService } = await import('./integratedNotificationService');
        await integratedNotificationService.createConnectionRequestNotification(
          user.id,
          userId,
          connectionId
        );

        console.log('Connection request notification created for user:', userId);
      }

      return true;
    } catch (error) {
      console.error('Error in connectWithUser:', error);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Accept a connection request
   *
   * @param connectionId The connection ID to accept
   * @returns Success status
   */
  async function acceptConnection(connectionId: string): Promise<boolean> {
    if (isLoading.value) return false;

    try {
      isLoading.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot accept connection: User not authenticated');
        return false;
      }

      console.log(`Accepting connection ${connectionId}`);

      // Get the connection - don't use single() to avoid 406 errors
      const { data: connections, error: getError } = await supabase
        .from('user_connections')
        .select('*')
        .eq('id', connectionId)
        .eq('connected_user_id', user.id)
        .eq('connection_status', 'pending')
        .limit(1);

      if (getError) {
        console.error('Error getting connection:', getError);
        throw getError;
      }

      if (!connections || connections.length === 0) {
        console.error('Connection request not found');
        throw new Error('Connection request not found');
      }

      const connection = connections[0];

      // Update the connection status
      const { error: updateError } = await supabase
        .from('user_connections')
        .update({
          connection_status: 'accepted',
          updated_at: new Date().toISOString()
        })
        .eq('id', connectionId);

      if (updateError) {
        console.error('Error accepting connection:', updateError);
        throw updateError;
      }

      // Track the activity
      await activityService.trackActivity('connect_accept', {
        connection_id: connectionId,
        user_id: connection.user_id
      });

      // Trigger global connection synchronization
      const { useConnectionsStore } = await import('../stores/connections');
      const connectionsStore = useConnectionsStore();
      await connectionsStore.synchronizeConnectionData();

      // Get user details to create a personalized notification
      const { data: userData, error: userError } = await supabase
        .from('personal_details')
        .select('first_name, last_name, email')
        .eq('user_id', user.id)
        .single();

      if (!userError && userData) {
        // Create a notification for the sender
        const acceptorName = userData.first_name && userData.last_name
          ? `${userData.first_name} ${userData.last_name}`
          : getUniversalUsername(userData.email);

        // Use the new integrated notification service for connection acceptance
        const { integratedNotificationService } = await import('./integratedNotificationService');
        await integratedNotificationService.createConnectionAcceptedNotification(
          connection.user_id,
          user.id,
          connectionId
        );

        console.log('Connection acceptance notification created for user:', connection.user_id);
      }

      return true;
    } catch (error) {
      console.error('Error in acceptConnection:', error);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Decline a connection request
   *
   * @param connectionId The connection ID to decline
   * @returns Success status
   */
  async function declineConnectionRequest(connectionId: string): Promise<boolean> {
    if (isLoading.value) return false;

    try {
      isLoading.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot decline connection: User not authenticated');
        return false;
      }

      console.log(`Declining connection ${connectionId}`);

      // Get the connection - don't use single() to avoid 406 errors
      const { data: connections, error: getError } = await supabase
        .from('user_connections')
        .select('*')
        .eq('id', connectionId)
        .eq('connected_user_id', user.id)
        .eq('connection_status', 'pending')
        .limit(1);

      if (getError) {
        console.error('Error getting connection:', getError);
        throw getError;
      }

      if (!connections || connections.length === 0) {
        console.error('Connection request not found');
        throw new Error('Connection request not found');
      }

      const connection = connections[0];

      // Update the connection status to declined
      const { error: updateError } = await supabase
        .from('user_connections')
        .update({
          connection_status: 'declined',
          updated_at: new Date().toISOString()
        })
        .eq('id', connectionId);

      if (updateError) {
        console.error('Error declining connection:', updateError);
        throw updateError;
      }

      // Track the activity
      await activityService.trackActivity('connect_decline', {
        connection_id: connectionId,
        user_id: connection.user_id
      });

      return true;
    } catch (error) {
      console.error('Error in declineConnectionRequest:', error);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Get user connections
   *
   * @param userId The user ID (defaults to current user)
   * @param status Connection status filter
   * @param limit Maximum number of connections to return
   * @param page Page number for pagination
   * @returns Array of user connections
   */
  async function getUserConnections(
    userId?: string,
    status: string = 'accepted',
    limit: number = 10,
    page: number = 1
  ): Promise<any[]> {
    try {
      if (!userId) {
        const { data: { user } } = await supabase.auth.getUser();
        userId = user?.id;
      }

      if (!userId) {
        console.error('Cannot get connections: No user ID provided');
        return [];
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      console.log(`Fetching connections for user ${userId} (status: ${status}, page: ${page}, limit: ${limit})`);

      // First check if the user_connections table exists
      const { error: tableCheckError } = await supabase
        .from('user_connections')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      // If the table doesn't exist, return an empty array
      if (tableCheckError &&
          (tableCheckError.code === 'PGRST200' ||
           tableCheckError.message?.includes("relation \"user_connections\" does not exist"))) {
        console.warn('The user_connections table does not exist yet. Please run the database fix from the admin panel.');
        return [];
      }

      // Fetch connections where the user is the initiator (user_id)
      const { data: connectionsAsUser, error: error1 } = await supabase
        .from('user_connections')
        .select('*')
        .eq('user_id', userId)
        .eq('connection_status', status)
        .order('created_at', { ascending: false });

      // Fetch connections where the user is the target (connected_user_id)
      const { data: connectionsAsTarget, error: error2 } = await supabase
        .from('user_connections')
        .select('*')
        .eq('connected_user_id', userId)
        .eq('connection_status', status)
        .order('created_at', { ascending: false });

      if (error1) {
        console.error('Error fetching user connections (as user):', error1);
        throw error1;
      }

      if (error2) {
        console.error('Error fetching user connections (as target):', error2);
        throw error2;
      }

      // Combine both sets of connections
      const allConnections = [
        ...(connectionsAsUser || []),
        ...(connectionsAsTarget || [])
      ];

      // Get user details for each connection separately
      const connectionsWithUserData = await Promise.all(
        allConnections.map(async (connection) => {
          try {
            // Determine which user ID to fetch (the other user in the connection)
            const otherUserId = connection.user_id === userId
              ? connection.connected_user_id
              : connection.user_id;

            // Try to get user details from personal_details
            const { data: userData, error: userError } = await supabase
              .from('personal_details')
              .select('user_id, email, first_name, last_name, profile_name')
              .eq('user_id', otherUserId)
              .single();

            if (!userError && userData) {
              return {
                ...connection,
                connected_user: userData
              };
            }

            // Fallback: return connection with minimal user data
            return {
              ...connection,
              connected_user: {
                user_id: otherUserId,
                email: 'Unknown',
                first_name: '',
                last_name: '',
                profile_name: 'Unknown User'
              }
            };
          } catch (error) {
            console.warn(`Could not fetch user data for connection ${connection.id}:`, error);
            return {
              ...connection,
              connected_user: {
                user_id: connection.user_id === userId ? connection.connected_user_id : connection.user_id,
                email: 'Unknown',
                first_name: '',
                last_name: '',
                profile_name: 'Unknown User'
              }
            };
          }
        })
      );

      // Sort by created_at descending
      connectionsWithUserData.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      // Apply pagination
      const paginatedConnections = connectionsWithUserData.slice(from, to + 1);

      console.log(`Fetched ${paginatedConnections.length} connections for user ${userId} with status ${status} (${connectionsAsUser?.length || 0} as user, ${connectionsAsTarget?.length || 0} as target)`);
      return paginatedConnections;
    } catch (error) {
      console.error('Error in getUserConnections:', error);
      return [];
    }
  }

  /**
   * Get connection requests
   *
   * @param limit Maximum number of requests to return
   * @param page Page number for pagination
   * @returns Array of connection requests
   */
  async function getConnectionRequests(
    limit: number = 10,
    page: number = 1
  ): Promise<any[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot get connection requests: User not authenticated');
        return [];
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      console.log(`Fetching connection requests (page: ${page}, limit: ${limit})`);

      // First check if the user_connections table exists
      const { error: tableCheckError } = await supabase
        .from('user_connections')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      // If the table doesn't exist, return an empty array
      if (tableCheckError &&
          (tableCheckError.code === 'PGRST200' ||
           tableCheckError.message?.includes("relation \"user_connections\" does not exist"))) {
        console.warn('The user_connections table does not exist yet. Please run the database fix from the admin panel.');
        return [];
      }

      // First get the connection requests
      const { data: connections, error: connectionsError } = await supabase
        .from('user_connections')
        .select('*')
        .eq('connected_user_id', user.id)
        .eq('connection_status', 'pending')
        .order('created_at', { ascending: false })
        .range(from, to);

      if (connectionsError) {
        console.error('Error fetching connection requests:', connectionsError);
        throw connectionsError;
      }

      if (!connections || connections.length === 0) {
        return [];
      }

      // Get user details for each connection request separately
      const requestsWithUserData = await Promise.all(
        connections.map(async (connection) => {
          try {
            // Try to get user details from personal_details first
            const { data: userData, error: userError } = await supabase
              .from('personal_details')
              .select('user_id, email, first_name, last_name, profile_name')
              .eq('user_id', connection.user_id)
              .single();

            if (!userError && userData) {
              return {
                ...connection,
                user: userData,
                requester_name: userData.first_name && userData.last_name
                  ? `${userData.first_name} ${userData.last_name}`
                  : userData.profile_name || userData.email,
                requester_email: userData.email,
                requester_profile_name: userData.profile_name
              };
            }

            // Fallback: try to get basic user info from auth.users (if accessible)
            // For now, return connection with minimal user data
            return {
              ...connection,
              user: {
                id: connection.user_id,
                email: 'Unknown',
                first_name: '',
                last_name: '',
                profile_name: 'Unknown User'
              },
              requester_name: 'Unknown User',
              requester_email: 'Unknown',
              requester_profile_name: 'Unknown User'
            };
          } catch (error) {
            console.warn(`Could not fetch user data for connection ${connection.id}:`, error);
            return {
              ...connection,
              user: {
                id: connection.user_id,
                email: 'Unknown',
                first_name: '',
                last_name: '',
                profile_name: 'Unknown User'
              },
              requester_name: 'Unknown User',
              requester_email: 'Unknown',
              requester_profile_name: 'Unknown User'
            };
          }
        })
      );

      return requestsWithUserData;
    } catch (error) {
      console.error('Error in getConnectionRequests:', error);
      return [];
    }
  }

  /**
   * Check if the current user is connected to another user
   *
   * @param userId The user ID to check
   * @returns Connection status
   */
  async function getConnectionStatus(userId: string): Promise<string> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user || user.id === userId) return 'self';

      // Check outgoing connection - don't use single() to avoid 406 errors
      const { data: outgoingData, error: outgoingError } = await supabase
        .from('user_connections')
        .select('connection_status')
        .eq('user_id', user.id)
        .eq('connected_user_id', userId)
        .limit(1);

      // If we have outgoing data, return the connection status
      if (outgoingData && outgoingData.length > 0) {
        return outgoingData[0].connection_status;
      }

      // Check incoming connection - don't use single() to avoid 406 errors
      const { data: incomingData, error: incomingError } = await supabase
        .from('user_connections')
        .select('connection_status')
        .eq('user_id', userId)
        .eq('connected_user_id', user.id)
        .limit(1);

      // If we have incoming data, return the connection status with 'incoming_' prefix
      if (incomingData && incomingData.length > 0) {
        return `incoming_${incomingData[0].connection_status}`;
      }

      return 'none';
    } catch (error) {
      console.error('Error in getConnectionStatus:', error);
      return 'error';
    }
  }

  return {
    connectWithUser,
    acceptConnection,
    declineConnectionRequest,
    getUserConnections,
    getConnectionRequests,
    getConnectionStatus,
    isLoading
  };
}
