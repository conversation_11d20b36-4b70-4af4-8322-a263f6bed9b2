/**
 * Utility functions for user-related operations
 */

import { getNameFromEmail as extractNameFromEmail, getInitials as extractInitialsFromString } from './nameUtils';

/**
 * Generates initials from a user's name or email
 *
 * @param name The user's full name (if available)
 * @param email The user's email (used as fallback)
 * @returns A string with the user's initials (1-2 characters)
 */
export function generateInitials(name?: string, email?: string): string {
  // If name is provided, use it to generate initials
  if (name && name.trim()) {
    return extractInitialsFromString(name);
  }

  // Fallback to email if name is not available
  if (email && email.trim()) {
    return extractInitialsFromString(email);
  }

  // Default fallback
  return 'ZB'; // Changed to match platform branding
}

/**
 * Derives a display name from an email address
 *
 * @param email The user's email address
 * @returns A formatted display name
 */
export function getNameFromEmail(email?: string): string {
  if (!email || !email.trim()) return 'ZB User'; // Changed to match platform branding

  return extractNameFromEmail(email);
}

/**
 * Universal function to get a user's display name from various sources
 * This is the recommended function to use throughout the application for consistent username display
 *
 * @param user The user object which may contain name, email, or other identifying information
 * @returns A formatted display name
 */
export function getUniversalUsername(user?: any): string {
  if (!user) return 'ZB User'; // Changed to match platform branding

  // First priority: Use first_name and last_name if both exist and are not empty
  if (user.first_name && user.first_name.trim() && user.last_name && user.last_name.trim()) {
    return `${user.first_name} ${user.last_name}`;
  }

  // Second priority: Use email to derive a name (this is now higher priority)
  if (user.email) {
    return getNameFromEmail(user.email);
  }

  // Third priority: Use profile_name if it exists
  if (user.profile_name && user.profile_name.trim()) {
    return user.profile_name;
  }

  // Fourth priority: Use name if it exists
  if (user.name && user.name.trim()) {
    return user.name;
  }

  // Final fallback
  return 'SmileFactory User'; // Changed to match platform branding
}

/**
 * Generates a background color based on a string (name or email)
 * This ensures the same user always gets the same color
 *
 * @param identifier A string to use for generating the color (name or email)
 * @returns A CSS color string
 */
export function generateAvatarColor(identifier?: string): string {
  if (!identifier) return '#0D8A3E'; // Default to primary color

  // Generate a hash from the string
  let hash = 0;
  for (let i = 0; i < identifier.length; i++) {
    hash = identifier.charCodeAt(i) + ((hash << 5) - hash);
  }

  // Define a set of pleasant, accessible colors
  const colors = [
    '#0D8A3E', // Primary green
    '#2196F3', // Blue
    '#673AB7', // Deep Purple
    '#E91E63', // Pink
    '#FF5722', // Deep Orange
    '#009688', // Teal
    '#3F51B5', // Indigo
    '#4CAF50', // Green
    '#FF9800', // Orange
    '#607D8B', // Blue Grey
  ];

  // Use the hash to select a color
  const colorIndex = Math.abs(hash) % colors.length;
  return colors[colorIndex];
}
