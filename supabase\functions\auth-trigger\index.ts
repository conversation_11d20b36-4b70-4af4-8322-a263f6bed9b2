// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

const SUPABASE_URL = Deno.env.get('ZB_SUPABASE_URL') || 'https://dpicnvisvxpmgjtbeicf.supabase.co'
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('ZB_SUPABASE_SERVICE_ROLE_KEY') || ''
const EMAIL_FUNCTION_URL = `${SUPABASE_URL}/functions/v1/send-email-verification`

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the request body
    const body = await req.json()

    // Check if this is a signup event
    if (body.type !== 'INSERT' || body.table !== 'auth.users') {
      return new Response(
        JSON.stringify({ message: 'Not a user signup event' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
      )
    }

    // Get the new user record
    const record = body.record

    if (!record || !record.id || !record.email) {
      return new Response(
        JSON.stringify({ error: 'Invalid user record' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Create a Supabase client with the service role key
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    // Get the authorization header from the current request
    const authHeader = req.headers.get('Authorization') || ''

    // Welcome is now included within the Auth verification email template
    const logResult = await supabase
      .from('email_logs')
      .insert({
        user_id: record.id,
        email_type: 'welcome_embedded',
        status: 'success',
        result: { message: 'Welcome content delivered via verification template' },
        sent_at: new Date().toISOString()
      })

    // Return success (no separate welcome email sent)
    return new Response(
      JSON.stringify({ success: true, message: 'Welcome will be delivered in the verification email' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )

  } catch (error) {
    console.error('Error processing auth trigger:', error)

    return new Response(
      JSON.stringify({ error: error.message || 'Internal server error' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
