# AI Service Implementation Plan

## Overview

This document outlines the specific implementation plan for AI services based on the ZbInnovation platform requirements and database structure analysis.

## Core AI Services Architecture

### **1. Authentication-Aware AI Context Service**

```typescript
// src/services/aiContextService.ts
interface UserContext {
  // Authentication State
  is_authenticated: boolean;
  user_id?: string;
  session_id: string;
  
  // Profile Information
  profile_types: string[]; // Can have multiple profiles
  profile_completion_status: Record<string, number>; // completion % per profile type
  primary_profile_type?: string;
  onboarding_completed: boolean;
  
  // Platform Engagement
  platform_familiarity_score: number; // 1-10 based on usage patterns
  last_active: Date;
  current_page: string;
  current_section?: string;
  active_filters?: Record<string, any>;
  
  // Behavioral Context
  recent_interactions: UserInteraction[];
  interests: string[];
  goals: string[];
  challenges: string[];
  
  // Permissions & Access
  accessible_content_types: string[];
  user_role: string;
}

class AIContextService {
  async buildUserContext(userId?: string, sessionContext?: any): Promise<UserContext> {
    if (!userId) {
      return this.buildUnauthenticatedContext(sessionContext);
    }
    
    // Get user profile information
    const profiles = await this.getUserProfiles(userId);
    const interactions = await this.getRecentInteractions(userId);
    const insights = await this.getUserInsights(userId);
    
    return {
      is_authenticated: true,
      user_id: userId,
      profile_types: profiles.map(p => p.type),
      profile_completion_status: this.calculateCompletionStatus(profiles),
      platform_familiarity_score: this.calculateFamiliarityScore(interactions),
      recent_interactions: interactions,
      interests: insights.interests || [],
      goals: insights.goals || [],
      challenges: insights.challenges || [],
      // ... other context data
    };
  }
  
  private buildUnauthenticatedContext(sessionContext: any): UserContext {
    return {
      is_authenticated: false,
      session_id: sessionContext.session_id,
      current_page: sessionContext.current_page,
      platform_familiarity_score: 1, // New user
      // ... minimal context for unauthenticated users
    };
  }
}
```

### **2. Bidirectional Integration Service**

```typescript
// src/services/aiBidirectionalService.ts
interface AIToUIAction {
  id: string;
  type: 'navigation' | 'dialog' | 'filter' | 'prefill' | 'highlight';
  
  // Navigation Actions
  route?: string;
  route_params?: Record<string, any>;
  
  // Dialog Actions
  dialog_type?: 'auth' | 'profile' | 'connection' | 'post' | 'message';
  dialog_data?: any;
  
  // Filter Actions
  filter_section?: 'feed' | 'profiles' | 'events' | 'groups' | 'marketplace';
  filter_criteria?: Record<string, any>;
  
  // Prefill Actions
  form_type?: 'post' | 'message' | 'profile' | 'event';
  prefill_data?: Record<string, any>;
  
  // Execution Context
  requires_auth: boolean;
  confirmation_required: boolean;
  success_message?: string;
  error_fallback?: string;
}

interface UIToAITrigger {
  trigger_id: string;
  context_type: 'profile_completion' | 'content_discovery' | 'matchmaking' | 'help' | 'creation_assistance';
  
  // Context Data
  current_page: string;
  user_context: UserContext;
  page_state?: any;
  
  // Prefilled Content
  initial_message?: string;
  suggested_questions: string[];
  
  // Expected Outcomes
  expected_actions: AIToUIAction[];
  conversation_goal: string;
}

class AIBidirectionalService {
  // Execute AI-generated actions in the UI
  async executeAIAction(action: AIToUIAction): Promise<boolean> {
    try {
      switch (action.type) {
        case 'navigation':
          return await this.handleNavigation(action);
        case 'dialog':
          return await this.handleDialog(action);
        case 'filter':
          return await this.handleFiltering(action);
        case 'prefill':
          return await this.handlePrefill(action);
        case 'highlight':
          return await this.handleHighlight(action);
        default:
          throw new Error(`Unknown action type: ${action.type}`);
      }
    } catch (error) {
      console.error('Failed to execute AI action:', error);
      return false;
    }
  }
  
  // Generate context-aware AI triggers from UI
  async generateAITrigger(context: any): Promise<UIToAITrigger> {
    const userContext = await this.aiContextService.buildUserContext(context.user_id, context);
    
    // Determine trigger type based on context
    const triggerType = this.determineTriggerType(context, userContext);
    
    // Generate contextual message and suggestions
    const { initialMessage, suggestions } = await this.generateContextualContent(triggerType, userContext);
    
    return {
      trigger_id: this.generateTriggerId(),
      context_type: triggerType,
      current_page: context.current_page,
      user_context: userContext,
      initial_message: initialMessage,
      suggested_questions: suggestions,
      expected_actions: this.getExpectedActions(triggerType, userContext),
      conversation_goal: this.getConversationGoal(triggerType, userContext)
    };
  }
}
```

### **3. Intelligent Matchmaking Service**

```typescript
// src/services/aiMatchmakingService.ts
interface MatchmakingRequest {
  user_id: string;
  match_type: 'profiles' | 'content' | 'opportunities' | 'collaborations';
  context?: {
    current_section?: string;
    applied_filters?: Record<string, any>;
    search_query?: string;
  };
  preferences?: {
    max_results?: number;
    min_score_threshold?: number;
    include_reasons?: boolean;
  };
}

interface MatchResult {
  id: string;
  type: string;
  score: number;
  reasons: string[];
  data: any;
  context: {
    why_recommended: string;
    confidence_level: number;
    match_category: string;
  };
}

class AIMatchmakingService {
  async findMatches(request: MatchmakingRequest): Promise<MatchResult[]> {
    // 1. Get user context and preferences
    const userContext = await this.aiContextService.buildUserContext(request.user_id);
    const userPreferences = await this.getUserPreferences(request.user_id);
    
    // 2. Generate user embedding based on profile and preferences
    const userEmbedding = await this.generateUserEmbedding(userContext, userPreferences);
    
    // 3. Perform hybrid search (vector + SQL)
    const results = await this.performHybridSearch(request, userEmbedding);
    
    // 4. Apply AI ranking and filtering
    const rankedResults = await this.rankAndFilterResults(results, userContext);
    
    // 5. Generate explanations
    return this.addExplanations(rankedResults, userContext);
  }
  
  private async performHybridSearch(request: MatchmakingRequest, userEmbedding: number[]): Promise<any[]> {
    switch (request.match_type) {
      case 'profiles':
        return this.searchProfiles(userEmbedding, request);
      case 'content':
        return this.searchContent(userEmbedding, request);
      case 'opportunities':
        return this.searchOpportunities(userEmbedding, request);
      case 'collaborations':
        return this.searchCollaborations(userEmbedding, request);
      default:
        throw new Error(`Unsupported match type: ${request.match_type}`);
    }
  }
  
  private async searchProfiles(userEmbedding: number[], request: MatchmakingRequest): Promise<any[]> {
    // Use the database function we created
    const { data, error } = await supabase.rpc('find_similar_profiles', {
      profile_type: request.context?.profile_type || 'all',
      query_embedding: userEmbedding,
      match_threshold: request.preferences?.min_score_threshold || 0.7,
      max_results: request.preferences?.max_results || 20,
      exclude_user_id: request.user_id
    });
    
    if (error) throw error;
    return data || [];
  }
}
```

## Platform-Specific AI Triggers

### **1. Profile Completion Triggers**

```typescript
// Trigger locations and contexts
const PROFILE_COMPLETION_TRIGGERS = {
  dashboard: {
    location: 'below_profile_completion_card',
    context: 'profile_guidance',
    messages: {
      no_profile: "I can help you create your first profile! What type of profile would you like to start with?",
      incomplete: "Let's complete your profile to unlock better connections and opportunities!",
      complete: "Great profile! I can help you optimize it for better visibility and connections."
    },
    suggestions: [
      "Help me choose the right profile type",
      "What information should I include?",
      "How can I make my profile stand out?",
      "Show me similar successful profiles"
    ]
  }
};
```

### **2. Content Discovery Triggers**

```typescript
// Community section triggers
const CONTENT_DISCOVERY_TRIGGERS = {
  feed_tab: {
    location: 'filter_section',
    context: 'content_discovery',
    messages: {
      new_user: "I can help you discover relevant content based on your interests!",
      returning_user: "Looking for something specific? I can help you find the perfect content.",
      power_user: "Want to discover trending content in your areas of expertise?"
    },
    suggestions: [
      "Show me opportunities in my field",
      "Find collaboration requests",
      "Discover funding opportunities",
      "Show trending innovation challenges"
    ]
  },
  
  profiles_tab: {
    location: 'filter_section',
    context: 'profile_discovery',
    messages: {
      innovator: "I can help you find the right mentors, investors, or collaborators!",
      mentor: "Looking for mentees or fellow experts to connect with?",
      investor: "I can help you discover promising startups and innovations!"
    }
  }
};
```

### **3. Matchmaking Triggers**

```typescript
// Cross-platform matchmaking triggers
const MATCHMAKING_TRIGGERS = {
  events_section: {
    context: 'event_discovery',
    ai_capabilities: [
      'Find events matching your expertise level',
      'Discover networking opportunities',
      'Suggest events based on your goals',
      'Find events with relevant speakers'
    ]
  },
  
  groups_section: {
    context: 'group_discovery',
    ai_capabilities: [
      'Find groups aligned with your interests',
      'Discover collaboration opportunities',
      'Suggest groups for skill development',
      'Find regional or industry-specific groups'
    ]
  },
  
  marketplace_section: {
    context: 'marketplace_discovery',
    ai_capabilities: [
      'Find products/services you need',
      'Discover potential customers',
      'Suggest pricing strategies',
      'Find partnership opportunities'
    ]
  }
};
```

## Implementation Phases

### **Phase 1: Core Services (Week 1-2)**
1. **AIContextService**: Build user context awareness
2. **AIBidirectionalService**: Implement action execution system
3. **Basic AI chat integration**: Connect with existing chat components

### **Phase 2: Matchmaking Intelligence (Week 3-4)**
1. **AIMatchmakingService**: Implement hybrid search
2. **Profile matching**: Semantic similarity for all profile types
3. **Content discovery**: Intelligent content recommendations

### **Phase 3: UI Integration (Week 5-6)**
1. **Trigger placement**: Add AI triggers across platform sections
2. **Action buttons**: Implement AI-to-UI actions
3. **Context awareness**: Dynamic triggers based on user state

### **Phase 4: Advanced Features (Week 7-8)**
1. **Cross-section intelligence**: Multi-segment recommendations
2. **Behavioral learning**: Improve recommendations based on interactions
3. **Performance optimization**: Caching and real-time updates

## Expected Outcomes

### **User Experience Improvements**
- **Contextual Assistance**: AI understands where users are and what they need
- **Intelligent Discovery**: Personalized recommendations across all platform sections
- **Seamless Integration**: Natural conversation flow with actionable outcomes
- **Progressive Enhancement**: AI gets smarter as users interact more

### **Platform Benefits**
- **Increased Engagement**: Users discover more relevant content and connections
- **Better Matching**: Higher success rates for collaborations and connections
- **Reduced Friction**: AI guides users to complete profiles and take actions
- **Data-Driven Insights**: Better understanding of user behavior and preferences

This implementation plan leverages your existing strong database foundation while adding intelligent AI capabilities that enhance rather than replace your current systems.
