<template>
  <q-form @submit="onSubmit" class="q-gutter-md">
    <div class="text-body1 q-mb-md">
      <strong>Mentorship Request:</strong> {{ request.title }}
    </div>
    
    <q-input
      v-model="form.title"
      label="Session Title *"
      outlined
      :rules="[val => !!val || 'Title is required']"
    />

    <q-input
      v-model="form.description"
      label="Session Description"
      type="textarea"
      outlined
      rows="3"
    />

    <q-select
      v-model="form.session_type"
      label="Session Type"
      outlined
      :options="sessionTypeOptions"
      emit-value
      map-options
    />

    <div class="row q-gutter-md">
      <div class="col">
        <q-input
          v-model="form.scheduled_start"
          label="Start Date & Time *"
          outlined
          type="datetime-local"
          :rules="[val => !!val || 'Start time is required']"
        />
      </div>
      <div class="col">
        <q-input
          v-model="form.scheduled_end"
          label="End Date & Time *"
          outlined
          type="datetime-local"
          :rules="[val => !!val || 'End time is required']"
        />
      </div>
    </div>

    <q-select
      v-model="form.meeting_platform"
      label="Meeting Platform"
      outlined
      :options="platformOptions"
      emit-value
      map-options
    />

    <q-input
      v-model="form.meeting_link"
      label="Meeting Link"
      outlined
      placeholder="https://zoom.us/j/..."
    />

    <q-input
      v-model="form.meeting_location"
      label="Meeting Location"
      outlined
      placeholder="Physical location or additional details"
    />

    <q-input
      v-model="form.agenda"
      label="Session Agenda"
      type="textarea"
      outlined
      rows="3"
      placeholder="What will be covered in this session?"
    />

    <div class="row q-gutter-sm justify-end">
      <q-btn
        label="Cancel"
        color="grey-7"
        outline
        @click="$emit('cancel')"
      />
      <q-btn
        label="Schedule Session"
        color="primary"
        type="submit"
        :loading="loading"
      />
    </div>
  </q-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

interface Props {
  request: any
}

const props = defineProps<Props>()
const emit = defineEmits(['submit', 'cancel'])

const loading = ref(false)

const form = reactive({
  title: `Mentorship Session - ${props.request.title}`,
  description: '',
  session_type: 'one-on-one',
  scheduled_start: '',
  scheduled_end: '',
  meeting_platform: 'zoom',
  meeting_link: '',
  meeting_location: '',
  agenda: ''
})

const sessionTypeOptions = [
  { label: 'One-on-One', value: 'one-on-one' },
  { label: 'Group Session', value: 'group' },
  { label: 'Workshop', value: 'workshop' },
  { label: 'Review Session', value: 'review' }
]

const platformOptions = [
  { label: 'Zoom', value: 'zoom' },
  { label: 'Google Meet', value: 'google-meet' },
  { label: 'Microsoft Teams', value: 'teams' },
  { label: 'In Person', value: 'in-person' },
  { label: 'Other', value: 'other' }
]

async function onSubmit() {
  try {
    loading.value = true
    
    // Validate end time is after start time
    if (new Date(form.scheduled_end) <= new Date(form.scheduled_start)) {
      throw new Error('End time must be after start time')
    }
    
    emit('submit', { ...form })
  } catch (error: any) {
    console.error('Form validation error:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.q-form {
  max-width: 600px;
}
</style>
