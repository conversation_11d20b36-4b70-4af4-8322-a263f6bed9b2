<template>
  <q-page class="flex flex-center bg-grey-2">
    <q-card class="password-reset-card q-pa-lg">
      <q-card-section class="text-center">
        <div class="text-h4 q-mb-md">Reset Password</div>
        <div class="text-subtitle1 text-grey-7">
          Enter your email to receive a password reset link
        </div>
      </q-card-section>

      <q-card-section>
        <div v-if="resetLinkSent" class="text-center q-pa-md">
          <q-icon :name="resetError ? 'warning' : 'check_circle'" :color="resetError ? 'warning' : 'positive'" size="48px" />
          <p class="text-body1 q-mb-md" v-if="!resetError">
            If an account exists with this email, we've sent a password reset link.
            Please check your inbox and follow the instructions.
          </p>
          <p class="text-body1 q-mb-md" v-else>
            There was an issue with the email service. We're trying an alternative method to send your reset link.
            If you don't receive an email within a few minutes, please contact support at <a href="mailto:<EMAIL>"><EMAIL></a>.
          </p>
          <q-btn
            flat
            color="primary"
            label="Back to Sign In"
            class="q-mt-md"
            to="/sign-in"
          />
        </div>

        <div v-else>
          <q-form @submit="handlePasswordReset" class="q-gutter-md">
            <q-input
              v-model="email"
              label="Email"
              type="email"
              outlined
              :rules="[
                (val) => !!val || 'Email is required',
                (val) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || 'Please enter a valid email'
              ]"
            >
              <template v-slot:prepend>
                <q-icon name="email" />
              </template>
            </q-input>

            <div class="q-mt-lg">
              <q-btn
                type="submit"
                color="primary"
                label="Send Reset Link"
                class="full-width"
                :loading="loading"
              />
            </div>

            <div class="text-center q-mt-md">
              <q-btn
                flat
                color="primary"
                label="Back to Sign In"
                to="/sign-in"
                :disable="loading"
              />
            </div>
          </q-form>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '../../../stores/auth'
import { useNotificationStore } from '../../../stores/notifications'

const authStore = useAuthStore()
const notificationStore = useNotificationStore()
const email = ref('')
const loading = ref(false)
const resetLinkSent = ref(false)
const resetError = ref(false)

const handlePasswordReset = async () => {
  try {
    loading.value = true
    resetError.value = false

    // Basic validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!email.value || !emailRegex.test(email.value)) {
      throw new Error('Please enter a valid email address')
    }

    console.log('Initiating simplified password reset for:', email.value)

    // Use simplified auth store for password reset
    await authStore.resetPassword(email.value)

    // Show success message
    resetLinkSent.value = true

  } catch (error: any) {
    console.error('Password reset error:', error)
    notificationStore.error(error.message || 'Failed to send reset link. Please try again.')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.password-reset-card {
  width: 100%;
  max-width: 400px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .password-reset-card {
    width: 90%;
  }
}
</style>
