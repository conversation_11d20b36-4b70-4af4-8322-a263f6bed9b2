<!-- New reusable component for auth options -->
<template>
  <div class="auth-options q-pa-md">
    <div class="row q-col-gutter-md justify-center">
      <!-- Email Link Option removed as requested -->
      <!--
      <div class="col-12">
        <q-btn
          class="full-width auth-btn"
          outline
          color="grey-6"
          disable
          :loading="loading && activeProvider === 'email'"
        >
          <div class="q-mr-sm">
            <unified-icon name="mail" color="grey-6" />
          </div>
          Continue with Email Link
          <q-tooltip>This feature is currently disabled</q-tooltip>
        </q-btn>
      </div>
      -->

      <!-- Social login options disabled for now -->
      <!-- Uncomment these when ready to enable social logins
      <div class="col-12">
        <q-btn
          class="full-width auth-btn"
          outline
          color="red"
          @click="handleGoogleAuth"
          :loading="loading && activeProvider === 'google'"
        >
          <q-icon name="fab fa-google" class="q-mr-sm" />
          Continue with Google
        </q-btn>
      </div>

      <div class="col-12">
        <q-btn
          class="full-width auth-btn"
          outline
          color="blue-7"
          @click="handleLinkedInAuth"
          :loading="loading && activeProvider === 'linkedin'"
        >
          <q-icon name="fab fa-linkedin" class="q-mr-sm" />
          Continue with LinkedIn
        </q-btn>
      </div>

      <div class="col-12">
        <q-btn
          class="full-width auth-btn"
          outline
          color="blue-9"
          @click="handleFacebookAuth"
          :loading="loading && activeProvider === 'facebook'"
        >
          <q-icon name="fab fa-facebook" class="q-mr-sm" />
          Continue with Facebook
        </q-btn>
      </div>
      -->

      <!-- Phone Option and Info Banner removed -->

      <!-- Divider -->
      <div class="col-12 text-center q-py-sm">
        <div class="row items-center">
          <div class="col">
            <q-separator />
          </div>
          <div class="col">
            <q-separator />
          </div>
        </div>
      </div>

      <!-- Traditional Email/Password -->
      <div class="col-12 text-center">
        <q-btn
          class="full-width auth-btn"
          flat
          color="primary"
          @click="handleEmailPassword"
          :loading="loading && activeProvider === 'email-password'"
          rounded
          size="md"
        >
          <div class="q-mr-sm">
            <unified-icon name="person" />
          </div>
          {{ mode === 'signup' ? 'Sign Up with Email & Password' : 'Sign In with Email & Password' }}
        </q-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useNotificationStore } from '@/stores/notifications'
import { useQuasar, Dialog } from 'quasar'
import UnifiedIcon from '@/components/ui/UnifiedIcon.vue'

const props = defineProps<{
  mode: 'signup' | 'login'
}>()

const emit = defineEmits<{
  (e: 'email-password'): void
  (e: 'email-password-signup'): void
}>()

const authStore = useAuthStore()
const notifications = useNotificationStore()
const loading = ref(false)
const activeProvider = ref<string | null>(null)
const $q = useQuasar()
const emailInput = ref('')

const handleEmailLink = async () => {
  try {
    // Use the Dialog plugin directly
    const email = await Dialog.create({
      title: 'Email Magic Link',
      message: 'Enter your email to receive a magic link',
      prompt: {
        model: emailInput.value,
        type: 'email',
        isValid: (val) => val.includes('@')
      },
      cancel: true,
      persistent: true
    })

    if (email) {
      console.log('Email entered:', email)
      // Process the email
      activeProvider.value = 'email'
      loading.value = true
      try {
        await authStore.signInWithEmailLink(email)
      } catch (err: any) {
        console.error('Error in signInWithEmailLink:', err)
        notifications.error(err.message || 'Failed to send magic link')
      }
    }
  } catch (error: any) {
    console.error('Dialog error:', error)
    if (error && error.message) {
      notifications.error(error.message)
    }
  } finally {
    loading.value = false
    activeProvider.value = null
  }
}

// Social login handlers are commented out for now
// Will be re-enabled when social logins are implemented
/*
const handleGoogleAuth = async () => {
  activeProvider.value = 'google'
  loading.value = true
  try {
    await authStore.signInWithGoogle()
  } catch (error: any) {
    notifications.error(error.message)
  } finally {
    loading.value = false
    activeProvider.value = null
  }
}

const handleLinkedInAuth = async () => {
  activeProvider.value = 'linkedin'
  loading.value = true
  try {
    await authStore.signInWithLinkedIn()
  } catch (error: any) {
    notifications.error(error.message)
  } finally {
    loading.value = false
    activeProvider.value = null
  }
}

const handleFacebookAuth = async () => {
  activeProvider.value = 'facebook'
  loading.value = true
  try {
    await authStore.signInWithFacebook()
  } catch (error: any) {
    notifications.error(error.message)
  } finally {
    loading.value = false
    activeProvider.value = null
  }
}
*/

// Phone handler removed

const handleEmailPassword = () => {
  if (props.mode === 'signup') {
    emit('email-password-signup')
  } else {
    emit('email-password')
  }
}
</script>

<style scoped>
.auth-options {
  max-width: 400px;
  margin: 0 auto;
}

.auth-btn {
  height: 44px;
  border-radius: 8px;
  font-weight: 500;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.auth-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
}

.auth-btn :deep(.q-icon) {
  font-size: 20px;
}

.auth-btn svg {
  width: 20px;
  height: 20px;
  stroke: currentColor;
}

/* Center alignment for auth option buttons */
.col-12.text-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.col-12.text-center .auth-btn {
  max-width: 280px;
  margin: 0 auto;
}

/* Ensure button content is centered */
.auth-btn .q-btn__content {
  justify-content: center;
  align-items: center;
  text-align: center;
}
</style>
