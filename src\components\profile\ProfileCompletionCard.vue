<template>
  <q-card class="profile-completion-card">
    <q-card-section class="bg-primary text-white">
      <div class="text-h6">Profile Completion</div>
    </q-card-section>

    <q-card-section>
      <!-- Profile Completion Status -->
      <div class="row items-center q-mb-md">
        <div class="col">
          <div class="text-h6">{{ profileName }}</div>
          <div class="text-subtitle2 q-mt-xs">
            <q-badge :color="getProfileTypeColor(currentProfile?.profile_type)" text-color="white" class="q-mr-xs">
              {{ formatProfileType(currentProfile?.profile_type) }}
            </q-badge>
            <q-badge :color="getProfileStateColor(currentProfile?.profile_state)" text-color="white">
              {{ formatProfileState(currentProfile?.profile_state) }}
            </q-badge>
          </div>
        </div>
        <div class="col-auto">
          <q-circular-progress
            :value="completionPercentage"
            size="70px"
            :thickness="0.2"
            :color="getCompletionColor(completionPercentage)"
            track-color="grey-3"
            class="q-mr-md"
          >
            <div class="text-subtitle1">{{ completionPercentage }}%</div>
          </q-circular-progress>
        </div>
      </div>

      <!-- Progress Bar -->
      <q-linear-progress
        :value="completionPercentage / 100"
        :color="getCompletionColor(completionPercentage)"
        size="10px"
        class="q-mb-md"
      />

      <!-- Steps Explanation -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-sm">
          <q-badge :color="getCompletionColor(completionPercentage)" :label="`${completedSteps}/${totalSteps} steps`" class="q-mr-sm" />
          Complete these steps to finish your profile
        </div>

        <div class="steps-container q-pl-md">
          <div class="step-item" :class="{ 'step-completed': isStepCompleted(1) }">
            <div class="step-marker" :class="{ 'bg-positive': isStepCompleted(1) }"></div>
            <div class="step-content">
              <div class="text-subtitle2">Profile Category</div>
              <div class="text-caption">Select your profile type (Innovator, Investor, Mentor, etc.)</div>
            </div>
          </div>

          <div class="step-item" :class="{ 'step-completed': isStepCompleted(2) }">
            <div class="step-marker" :class="{ 'bg-positive': isStepCompleted(2) }"></div>
            <div class="step-content">
              <div class="text-subtitle2">Personal Details</div>
              <div class="text-caption">Name, email, phone number, gender, and bio</div>
            </div>
          </div>

          <div class="step-item" :class="{ 'step-completed': isStepCompleted(3) }">
            <div class="step-marker" :class="{ 'bg-positive': isStepCompleted(3) }"></div>
            <div class="step-content">
              <div class="text-subtitle2">Profile Details</div>
              <div class="text-caption">{{ formatProfileType(currentProfile?.profile_type) }}-specific information</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Button -->
      <div class="text-center">
        <q-btn
          color="primary"
          :label="completionPercentage < 100 ? 'Complete Your Profile' : 'View Your Profile'"
          class="full-width dashboard-action-btn"
          size="md"
          outline
          :to="{ name: completionPercentage < 100 ? 'profile-edit' : 'profile-view', params: { id: currentProfile?.user_id } }"
        >
          <template v-slot:prepend>
            <q-icon :name="completionPercentage < 100 ? 'edit' : 'visibility'" class="q-mr-xs" />
          </template>
        </q-btn>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useProfileStore } from '../../stores/profile'

const props = defineProps({
  profile: {
    type: Object,
    default: null
  },
  totalSteps: {
    type: Number,
    default: 3
  }
})

const profileStore = useProfileStore()

// Use the provided profile or fall back to the current profile from the store
const currentProfile = computed(() => props.profile || profileStore.currentProfile)

// Calculate profile name
const profileName = computed(() => {
  if (!currentProfile.value) return 'My Profile'
  return currentProfile.value.profile_name ||
         `${currentProfile.value.first_name || ''} ${currentProfile.value.last_name || ''}`.trim() ||
         'My Profile'
})

// Calculate completion percentage
const completionPercentage = computed(() => {
  if (!currentProfile.value) return 0

  // Use the stored profile_completion value for consistency
  // This ensures we're showing the same value that's stored in the database
  return Math.round(currentProfile.value.profile_completion || 0)
})

// Calculate completed steps based on completion percentage
const completedSteps = computed(() => {
  return Math.ceil((completionPercentage.value / 100) * props.totalSteps)
})

// Check if a specific step is completed
function isStepCompleted(step: number): boolean {
  return step <= completedSteps.value
}

// Format profile type for display
function formatProfileType(type: string | null | undefined): string {
  if (!type) return 'Profile'

  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

// Format profile state for display
function formatProfileState(state: string | null | undefined): string {
  if (!state) return 'Unknown'

  return state
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}

// Get color for profile type
function getProfileTypeColor(type: string | null | undefined): string {
  if (!type) return 'grey'

  const colors: Record<string, string> = {
    'innovator': 'purple',
    'investor': 'green',
    'mentor': 'blue',
    'professional': 'teal',
    'industry_expert': 'deep-orange',
    'academic_student': 'indigo',
    'academic_institution': 'light-blue',
    'organisation': 'amber'
  }

  return colors[type] || 'grey'
}

// Get color for profile state
function getProfileStateColor(state: string | undefined): string {
  if (!state) return 'grey'

  const colors: Record<string, string> = {
    'DRAFT': 'grey',
    'IN_PROGRESS': 'blue',
    'PENDING_APPROVAL': 'orange',
    'ACTIVE': 'green',
    'DISABLED': 'red',
    'DECLINED': 'red-6'
  }

  return colors[state] || 'grey'
}

// Get color based on completion percentage
function getCompletionColor(percentage: number): string {
  if (percentage < 30) return 'red'
  if (percentage < 70) return 'orange'
  if (percentage < 100) return 'blue'
  return 'green'
}
</script>

<style scoped>
.profile-completion-card {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.steps-container {
  position: relative;
  padding-left: 20px;
  margin-bottom: 20px;
}

.step-item {
  position: relative;
  padding-bottom: 20px;
  padding-left: 20px;
  opacity: 0.7;
}

.step-completed {
  opacity: 1;
}

.step-marker {
  position: absolute;
  left: -10px;
  top: 5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #ccc;
  z-index: 2;
}

.step-item:not(:last-child)::before {
  content: '';
  position: absolute;
  left: -2px;
  top: 15px;
  bottom: 0;
  width: 2px;
  background-color: #e0e0e0;
  z-index: 1;
}

.step-content {
  padding-bottom: 10px;
}

/* Dashboard action button styling for consistency */
.dashboard-action-btn {
  min-height: 44px;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dashboard-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>
