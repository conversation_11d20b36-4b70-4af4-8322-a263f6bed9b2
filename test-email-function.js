// Test script for the new welcome email function
import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client
const supabaseUrl = 'https://dpicnvisvxpmgjtbeicf.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc'
const supabase = createClient(supabaseUrl, supabaseKey)

async function testWelcomeEmail() {
  console.log('Testing welcome email function...')

  try {
    const { data, error } = await supabase.functions.invoke('welcome-email', {
      body: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User'
      }
    })

    if (error) {
      console.error('Error:', error)
      return
    }

    console.log('Success:', data)
  } catch (err) {
    console.error('Network error:', err)
  }
}

// Run the test
testWelcomeEmail()