<template>
  <q-card class="compact-menu-card hub-facilities-menu">
    <q-card-section class="q-pa-sm bg-primary text-white">
      <div class="text-subtitle1 text-weight-medium">Hub Facilities</div>
      <div class="text-caption q-mt-xs">Discover our world-class facilities and upcoming events</div>
    </q-card-section>

    <div class="q-pa-md">
      <div class="row q-col-gutter-md">
        <!-- First Column -->
        <div class="col-12 col-md-6">
          <div class="text-subtitle2 text-weight-bold text-primary q-mb-sm">
            <q-icon name="business" size="sm" class="q-mr-xs" />
            Our Facilities
          </div>
          <q-list padding dense class="rounded-borders menu-list">
            <q-item clickable v-ripple @click="navigateTo('/hub-facilities')">
              <q-item-section avatar>
                <q-icon name="business" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Hub Features & Facilities</q-item-label>
                <q-item-label caption>Explore our state-of-the-art workspace</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable v-ripple @click="navigateTo('/hub-facilities?section=coworking')">
              <q-item-section avatar>
                <q-icon name="desk" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Co-working Spaces</q-item-label>
                <q-item-label caption>Flexible workspaces for innovators</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable v-ripple @click="navigateTo('/hub-facilities?section=meeting')">
              <q-item-section avatar>
                <q-icon name="meeting_room" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Meeting Rooms</q-item-label>
                <q-item-label caption>Professional spaces for collaboration</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <!-- Second Column -->
        <div class="col-12 col-md-6">
          <div class="text-subtitle2 text-weight-bold text-primary q-mb-sm">
            <q-icon name="event" size="sm" class="q-mr-xs" />
            Events & Programs
          </div>
          <q-list padding dense class="rounded-borders menu-list">
            <q-item clickable v-ripple @click="navigateTo('/hub-events')">
              <q-item-section avatar>
                <q-icon name="event" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Hub Events & Programs</q-item-label>
                <q-item-label caption>Discover our upcoming events</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable v-ripple @click="navigateTo('/hub-events?type=workshops')">
              <q-item-section avatar>
                <q-icon name="school" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Workshops & Training</q-item-label>
                <q-item-label caption>Skill-building opportunities</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable v-ripple @click="navigateTo('/hub-events?type=networking')">
              <q-item-section avatar>
                <q-icon name="handshake" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Networking Events</q-item-label>
                <q-item-label caption>Connect with fellow innovators</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
    </div>

    <q-separator />

    <q-card-actions align="right">
      <q-btn
        flat
        color="primary"
        label="View All Facilities"
        icon-right="arrow_forward"
        @click="navigateTo('/hub-facilities')"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useMegaMenuStore } from '../../stores/megaMenu';
import { useRouter } from 'vue-router';

const megaMenuStore = useMegaMenuStore();
const router = useRouter();

function closeMenu() {
  megaMenuStore.closeMenu();
}

async function navigateTo(path) {
  // Close the menu first
  closeMenu();

  // Use the unified navigation service
  const { navigateWithMenuClose } = await import('../../services/navigationService').then(m => ({ navigateWithMenuClose: m.useNavigation().navigateWithMenuClose }));

  const success = await navigateWithMenuClose(path);

  if (!success) {
    console.error('HubFacilitiesMenu: Navigation failed for path:', path);
  }
}
</script>

<style scoped>
.compact-menu-card {
  width: 100%;
  max-width: 600px;
}

.menu-list .q-item {
  border-radius: 4px;
}

.menu-list .q-item:hover {
  background-color: rgba(13, 138, 62, 0.05);
}
</style>
