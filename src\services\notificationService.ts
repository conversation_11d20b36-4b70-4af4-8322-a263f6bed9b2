import { supabase } from '@/lib/supabase';
import { ref } from 'vue';
import { useNotificationStore } from '../stores/notifications';

export interface UserNotification {
  id: string;
  user_id: string;
  title: string;
  content: string;
  type: string;
  related_entity_id: string | null;
  is_read: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Notification Service
 *
 * @deprecated This service is deprecated. Use integratedNotificationService.ts instead.
 *
 * This service provides functions for creating and managing user notifications.
 * It interacts with the user_notifications table in the database.
 */
export function useNotificationService() {
  const isLoading = ref(false);
  const notificationStore = useNotificationStore();

  // Subscription for real-time notifications
  let notificationSubscription: any = null;

  /**
   * Initialize the notification service
   * Sets up real-time subscription for new notifications
   */
  async function initialize() {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('Cannot initialize notification service: User not authenticated');
      return;
    }

    // Check if the user_notifications table exists
    try {
      const { error: tableCheckError } = await supabase
        .from('user_notifications')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      if (tableCheckError) {
        console.warn('The user_notifications table does not exist yet:', tableCheckError.message);
        return;
      }

      // Set up real-time subscription for new notifications
      notificationSubscription = supabase
        .channel('user-notifications')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'user_notifications',
            filter: `user_id=eq.${user.id}`
          },
          (payload) => {
            console.log('New notification received:', payload);

            // Show a notification
            const notification = payload.new as UserNotification;
            notificationStore.info({
              message: notification.title,
              caption: notification.content,
              icon: getNotificationIcon(notification.type),
              timeout: 5000
            });
          }
        )
        .subscribe();
    } catch (error) {
      console.error('Error initializing notification service:', error);
    }
  }

  /**
   * Clean up the notification service
   * Removes real-time subscription
   */
  function cleanup() {
    if (notificationSubscription) {
      supabase.removeChannel(notificationSubscription);
      notificationSubscription = null;
    }
  }

  /**
   * Create a notification for a user
   *
   * @param userId The user ID to create the notification for
   * @param title The notification title
   * @param content The notification content
   * @param type The notification type (e.g., 'connection_request', 'connection_accepted')
   * @param relatedEntityId Optional related entity ID (e.g., connection ID)
   * @returns The created notification ID or null if failed
   */
  async function createNotification(
    userId: string,
    title: string,
    content: string,
    type: string,
    relatedEntityId?: string
  ): Promise<string | null> {
    try {
      isLoading.value = true;

      // Check if the user_notifications table exists
      const { error: tableCheckError } = await supabase
        .from('user_notifications')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      if (tableCheckError) {
        console.warn('The user_notifications table does not exist yet:', tableCheckError.message);
        return null;
      }

      // Create the notification
      console.log('Creating notification:', {
        user_id: userId,
        title,
        content,
        type,
        related_entity_id: relatedEntityId || null
      });

      const { data, error } = await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          title,
          content,
          type,
          related_entity_id: relatedEntityId || null,
          is_read: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error creating notification:', error);
        return null;
      }

      return data.id;
    } catch (error) {
      console.error('Error in createNotification:', error);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Get user notifications
   *
   * @param limit Maximum number of notifications to return
   * @param page Page number for pagination
   * @param unreadOnly Whether to only return unread notifications
   * @returns Array of user notifications
   */
  async function getUserNotifications(
    limit: number = 10,
    page: number = 1,
    unreadOnly: boolean = false
  ): Promise<UserNotification[]> {
    try {
      isLoading.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot get notifications: User not authenticated');
        return [];
      }

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      // Check if the user_notifications table exists
      const { error: tableCheckError } = await supabase
        .from('user_notifications')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      if (tableCheckError) {
        console.warn('The user_notifications table does not exist yet:', tableCheckError.message);
        return [];
      }

      // Build the query
      let query = supabase
        .from('user_notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .range(from, to);

      // Add filter for unread notifications if requested
      if (unreadOnly) {
        query = query.eq('is_read', false);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching user notifications:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in getUserNotifications:', error);
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Mark notifications as read
   *
   * @param notificationIds Array of notification IDs to mark as read
   * @returns Success status
   */
  async function markAsRead(notificationIds: string[]): Promise<boolean> {
    if (!notificationIds.length) return true;

    try {
      isLoading.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot mark notifications as read: User not authenticated');
        return false;
      }

      const { error } = await supabase
        .from('user_notifications')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .in('id', notificationIds)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error marking notifications as read:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Error in markAsRead:', error);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Mark all notifications as read
   *
   * @returns Success status
   */
  async function markAllAsRead(): Promise<boolean> {
    try {
      isLoading.value = true;

      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot mark all notifications as read: User not authenticated');
        return false;
      }

      const { error } = await supabase
        .from('user_notifications')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) {
        console.error('Error marking all notifications as read:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Error in markAllAsRead:', error);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Get unread notification count
   *
   * @returns Count of unread notifications
   */
  async function getUnreadCount(): Promise<number> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error('Cannot get unread count: User not authenticated');
        return 0;
      }

      // Check if the user_notifications table exists
      const { error: tableCheckError } = await supabase
        .from('user_notifications')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      if (tableCheckError) {
        console.warn('The user_notifications table does not exist yet:', tableCheckError.message);
        return 0;
      }

      const { count, error } = await supabase
        .from('user_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) {
        console.error('Error getting unread notification count:', error);
        throw error;
      }

      return count || 0;
    } catch (error) {
      console.error('Error in getUnreadCount:', error);
      return 0;
    }
  }

  /**
   * Get notification icon based on type
   */
  function getNotificationIcon(type: string): string {
    switch (type) {
      case 'connection_request':
        return 'person_add';
      case 'connection_accepted':
        return 'how_to_reg';
      case 'message':
        return 'chat';
      case 'post_like':
        return 'favorite';
      case 'post_comment':
        return 'comment';
      default:
        return 'notifications';
    }
  }

  return {
    initialize,
    cleanup,
    createNotification,
    getUserNotifications,
    markAsRead,
    markAllAsRead,
    getUnreadCount,
    isLoading
  };
}
