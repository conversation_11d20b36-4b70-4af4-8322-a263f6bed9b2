import { supabase } from './supabase';
import { getProfilePKColumn, getProfileTableName } from './profileKeys';

/**
 * Checks if the personal_details table exists
 * Optimized to reduce unnecessary database checks
 */
export async function checkPersonalDetailsTable(): Promise<boolean> {
  try {
    // First check if table exists
    const { error } = await supabase.from('personal_details').select('user_id').limit(1);

    if (error) {
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error checking personal_details table:', error);
    return false;
  }
}

/**
 * Creates a personal details record for a user
 */
export async function createPersonalDetails(userId: string, email: string): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Creating personal details for user:', userId);

    // Check if personal details already exist
    // Use a more robust approach to handle potential 406 errors
    try {
      const { data: existingDetails, error: checkError } = await supabase
        .from('personal_details')
        .select('user_id')
        .eq('user_id', userId)
        .maybeSingle();

      if (existingDetails) {
        console.log('Personal details already exist for user:', userId);
        return { success: true, message: 'Personal details already exist' };
      }
    } catch (err) {
      console.warn('Error checking for existing personal details, will attempt to create:', err);
      // Continue with creation attempt
    }

    // Previous check code has been moved into the try/catch block above

    // Create new personal details
    const { error } = await supabase
      .from('personal_details')
      .insert({
        user_id: userId,
        email,
        role: 'user',
        profile_name: 'My Profile',
        profile_state: 'IN_PROGRESS',
        profile_visibility: 'private',
        is_verified: false,
        profile_completion: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error creating personal details:', error.message);
      return { success: false, message: error.message };
    }

    console.log('✅ Personal details created successfully');
    return { success: true, message: 'Personal details created successfully' };
  } catch (error: any) {
    console.error('Error creating personal details:', error.message);
    return { success: false, message: error.message };
  }
}

/**
 * Fetches the user's personal details
 */
export async function getPersonalDetails(userId: string): Promise<any> {
  try {
    console.log('Fetching personal details for user:', userId);

    const { data, error } = await supabase
      .from('personal_details')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching personal details:', error.message);
      return null;
    }

    console.log('✅ Personal details fetched successfully');
    return data;
  } catch (error: any) {
    console.error('Error fetching personal details:', error.message);
    return null;
  }
}

/**
 * Checks if a profile table exists
 * Optimized to reduce unnecessary database checks
 */
export async function checkProfileTable(profileType: string): Promise<boolean> {
  try {
    // Always use plural table names for consistency
    const tableName = `${profileType}_profiles`;

    // Check if table exists
    const { error } = await supabase.from(tableName).select('*').limit(1);

    if (error) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Creates a profile for a user
 */
export async function createProfile(
  userId: string,
  profileType: string,
  profileName: string
): Promise<{ success: boolean; message: string; profileId?: string }> {
  try {
    console.log(`Creating ${profileType} profile for user:`, userId);

    // Always use plural table names for consistency
    const tableName = getProfileTableName(profileType as any);

    // Check if table exists
    const tableExists = await checkProfileTable(profileType);
    if (!tableExists) {
      return {
        success: false,
        message: `${profileType}_profiles table does not exist`
      };
    }

    // First, update the personal_details record with the profile type
    const { error: updateError } = await supabase
      .from('personal_details')
      .update({
        profile_type: profileType,
        profile_name: profileName,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (updateError) {
      console.error(`Error updating personal details with profile type:`, updateError.message);
      return { success: false, message: updateError.message };
    }

    // Check if profile already exists
    const { data: existingProfile, error: checkError } = await supabase
      .from(tableName)
      .select('*')
      .eq('user_id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error(`Error checking ${profileType} profile:`, checkError.message);
      return { success: false, message: checkError.message };
    }

    if (existingProfile) {
      console.log(`${profileType} profile already exists for user:`, userId);
      return {
        success: true,
        message: `${profileType} profile already exists`,
        profileId: existingProfile.user_id
      };
    }

    // Insert the profile
    const { data, error } = await supabase
      .from(tableName)
      .insert({
        user_id: userId,
        profile_name: profileName,
        is_public: false,
        completion_percentage: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select();

    if (error) {
      console.error(`Error creating ${profileType} profile:`, error.message);
      return { success: false, message: error.message };
    }

    if (!data || data.length === 0) {
      return { success: false, message: 'No data returned from profile creation' };
    }

    const profileIdField = `${profileType}_profile_id`;
    const profileId = data[0][profileIdField];

    console.log(`✅ ${profileType} profile created successfully with ID:`, profileId);
    return {
      success: true,
      message: `${profileType} profile created successfully`,
      profileId
    };
  } catch (error: any) {
    console.error(`Error creating ${profileType} profile:`, error.message);
    return { success: false, message: error.message };
  }
}

/**
 * Fetches a profile by ID
 */
export async function getProfile(
  profileType: string,
  profileId: string
): Promise<any> {
  try {
    console.log(`Fetching ${profileType} profile with ID:`, profileId);

    // Always use plural table names for consistency
    const tableName = getProfileTableName(profileType as any);
    const profileIdField = getProfilePKColumn(profileType as any);

    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .eq(profileIdField, profileId)
      .single();

    if (error) {
      console.error(`Error fetching ${profileType} profile:`, error.message);
      return null;
    }

    console.log(`✅ ${profileType} profile fetched successfully`);
    return data;
  } catch (error: any) {
    console.error(`Error fetching ${profileType} profile:`, error.message);
    return null;
  }
}

/**
 * Fetches all profiles for a user
 * Optimized to reduce redundant database operations
 */
export async function getUserProfiles(userId: string): Promise<any[]> {
  try {
    // Define profile types
    const profileTypes = [
      'innovator',
      'investor',
      'mentor',
      'professional',
      'industry_expert',
      'academic_student',
      'academic_institution',
      'organisation'
    ];

    const allProfiles: any[] = [];

    // First check if user has a profile type in personal_details
    const { data: personalDetails, error: personalError } = await supabase
      .from('personal_details')
      .select('profile_type')
      .eq('user_id', userId)
      .single();

    if (personalError || !personalDetails?.profile_type) {
      return [];
    }

    // Only fetch from the table that matches the user's profile type
    const profileType = personalDetails.profile_type;
    const tableName = `${profileType}_profiles`;
    const profileIdField = getProfilePKColumn(profileType as any);

    try {
      const { data, error } = await supabase
        .from(tableName)
        .select(`*, ${profileIdField}`)
        .eq('user_id', userId);

      if (!error && data && data.length > 0) {
        // Add profile type to each profile
        const profiles = data.map(profile => ({
          ...profile,
          profile_type: profileType,
          id: profile[profileIdField]
        }));

        allProfiles.push(...profiles);
      }
    } catch (err) {
      // Skip tables that don't exist
    }

    return allProfiles;
  } catch (error: any) {
    return [];
  }
}
